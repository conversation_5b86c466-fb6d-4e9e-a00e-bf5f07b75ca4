import { PlainMessage } from '@bufbuild/protobuf';
import { faBook, faPlus, faSave, faTrash } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { Button, Input, Modal, Tag, Layout, Select } from 'antd';
import { useCallback, useEffect, useMemo, useState } from 'react';

import { Runbook, TargetSelector } from '@ui/lib/apiclient/argocd/v1/argocd_pb';
import {
  useGetInstanceQuery,
  useListInstances,
  usePatchInstanceSpecMutation
} from '@ui/lib/apiclient/organization/kubevision-queries';
import { Loading } from '@ui/lib/components';
import { CodeEditor } from '@ui/lib/components/code-editor';
import { useKubeVisionContext } from '@ui/lib/context/kubevision-context';
import { useModal } from '@ui/lib/hooks';
import { useSpotlightSearch } from '@ui/lib/hooks/use-spotlight-search';

import useCustomSearchParams from '../../hooks/use-custom-search-params';

import { RunbookModal } from './runbook-modal';

const { Content, Sider } = Layout;

type RunbookItem = PlainMessage<Runbook> & {
  instanceId: string;
  index: number;
};

type KubevisionIncidentRunbookProps = {
  instanceId: string;
};

export const KubevisionIncidentRunbook = ({ instanceId }: KubevisionIncidentRunbookProps) => {
  const { getSearchParam, setSearchParams } = useCustomSearchParams();
  const { isDarkTheme, organizationId, enabledClustersInfo } = useKubeVisionContext();
  const borderColor = isDarkTheme ? 'border-zinc-800' : 'border-zinc-200';
  const backgroundColor = isDarkTheme ? 'bg-zinc-800' : 'bg-zinc-100';

  const { mutateAsync: patchInstanceSpec } = usePatchInstanceSpecMutation();
  const [editingRunbook, setEditingRunbook] = useState<RunbookItem | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [editedContent, setEditedContent] = useState('');
  const [editedName, setEditedName] = useState('');
  const [editedAppliedTo, setEditedAppliedTo] = useState<PlainMessage<TargetSelector>>({
    argocdApplications: [],
    k8sNamespaces: [],
    clusters: []
  });

  const {
    data: instanceResp,
    isLoading: isInstanceLoading,
    refetch: refetchInstance
  } = useGetInstanceQuery(instanceId, {
    enabled: !!instanceId
  });
  const {
    data: instancesResp,
    isLoading: isInstancesLoading,
    refetch: refetchInstances
  } = useListInstances({
    enabled: !instanceId
  });
  const isLoading = isInstanceLoading || isInstancesLoading;
  const refetch = useCallback(() => {
    if (instanceId) {
      refetchInstance();
    } else {
      refetchInstances();
    }
  }, [instanceId, refetchInstance, refetchInstances]);

  const { resources } = useSpotlightSearch(organizationId, instanceId, true);
  const applications = useMemo(() => {
    const applications: string[] = [];
    resources.forEach((resource) => {
      if (resource.kind === 'Application') {
        applications.push(resource.name);
      }
    });
    return applications;
  }, [resources]);
  const clusters = useMemo(() => {
    const clusters: string[] = [];
    resources.forEach((resource) => {
      if (resource.kind === 'Namespace') {
        clusters.push(enabledClustersInfo.clusterName(resource.clusterId));
      }
    });
    return [...new Set(clusters)];
  }, [resources, instanceId, enabledClustersInfo]);
  const namespaces = useMemo(() => {
    const namespaces: string[] = [];
    resources.forEach((r) => {
      if (r.kind !== 'Namespace') {
        return;
      }
      const selectedClusterNames = [];
      if (editedAppliedTo.clusters?.length === 0) {
        selectedClusterNames.push(...clusters);
      } else if (editedAppliedTo.clusters.includes('*')) {
        selectedClusterNames.push(...clusters);
      } else {
        selectedClusterNames.push(...editedAppliedTo.clusters);
      }
      if (!selectedClusterNames.includes(enabledClustersInfo.clusterName(r.clusterId))) {
        return;
      }
      namespaces.push(r.name);
    });
    return [...new Set(namespaces)];
  }, [resources, clusters, editedAppliedTo]);

  const instances = useMemo(
    () =>
      instanceId
        ? instanceResp?.instance
          ? [instanceResp.instance]
          : []
        : (instancesResp?.instances ?? []),
    [instanceResp, instancesResp, instanceId]
  );

  const runbooks: RunbookItem[] = useMemo(() => {
    const items: RunbookItem[] = [];
    instances.forEach((i) => {
      i.spec?.kubeVisionConfig?.aiConfig?.runbooks?.forEach((r, idx) => {
        items.push({ ...r, instanceId: i.id, index: idx });
      });
    });
    return items;
  }, [instances]);

  const getInstanceRunbooks = useCallback(
    (instanceId: string) =>
      instances?.find((i) => i?.id === instanceId)?.spec?.kubeVisionConfig?.aiConfig?.runbooks ??
      [],
    [instances]
  );

  const isSelected = useCallback(
    (runbook: RunbookItem) => {
      return (
        editingRunbook?.instanceId === runbook.instanceId && editingRunbook?.name === runbook.name
      );
    },
    [editingRunbook]
  );

  useEffect(() => {
    const instanceId = getSearchParam('instanceId');
    const runbookName = getSearchParam('runbook');
    const runbooks = getInstanceRunbooks(instanceId);
    const runbook = runbooks.find((r) => r.name === runbookName);
    if (runbook && !editingRunbook) {
      setEditingRunbook({
        ...runbook,
        instanceId: instanceId,
        index: runbooks.findIndex((r) => r.name === runbookName)
      });
    }
  }, [getSearchParam, getInstanceRunbooks, setEditingRunbook, editingRunbook]);
  const { show } = useModal();

  return (
    <Layout className='h-[calc(100vh-310px)] bg-transparent'>
      <Sider
        width={320}
        className={`bg-transparent overflow-y-auto border-0 border-r border-solid ${borderColor}`}
      >
        {isLoading ? (
          <Loading></Loading>
        ) : (
          <>
            <div className='flex flex-row items-center justify-between px-4 mb-2'>
              <div className='text-sm text-gray-500'>{runbooks.length} Runbook(s)</div>
              <Button
                type='primary'
                size='small'
                icon={<FontAwesomeIcon icon={faPlus} />}
                onClick={() =>
                  show((props) => (
                    <RunbookModal
                      {...props}
                      isDarkTheme={isDarkTheme}
                      index={-1}
                      runbooks={[]}
                      instanceId={instanceId}
                      instances={instances}
                      showInstanceSelect={!instanceId}
                      setRunbooks={async (runbooks, instanceId) => {
                        const currentRunbooks = getInstanceRunbooks(instanceId);
                        const newRunbooks = [...currentRunbooks, ...runbooks];
                        await patchInstanceSpec({
                          instanceId: instanceId,
                          data: {
                            kubeVisionConfig: {
                              aiConfig: {
                                runbooks: newRunbooks
                              }
                            }
                          }
                        });
                        refetch();
                      }}
                      resources={resources}
                    />
                  ))
                }
              >
                Create
              </Button>
            </div>

            <div>
              {runbooks.map((runbook, idx) => (
                <div
                  key={idx}
                  className={`py-2 px-4
                    ${isDarkTheme ? 'hover:bg-zinc-800' : 'hover:bg-zinc-100'}
                    cursor-pointer flex flex-row items-center
                    ${isSelected(runbook) ? backgroundColor : ''}`}
                  onClick={() => {
                    setSearchParams({
                      instanceId: runbook.instanceId,
                      runbook: runbook.name
                    });
                    setEditingRunbook(runbook);
                  }}
                >
                  <div className='mr-4'>
                    <FontAwesomeIcon icon={faBook} className='text-gray-500 w-[24px] h-[24px]' />
                  </div>
                  <div className='flex-1'>
                    <div>{runbook.name}</div>
                  </div>
                  <div className='ms-auto'>
                    {!instanceId && (
                      <Tag color='blue'>
                        {instances.find((i) => i.id === runbook.instanceId)?.name}
                      </Tag>
                    )}
                  </div>
                </div>
              ))}
              {runbooks.length === 0 && (
                <div className='text-sm text-gray-500 px-4 mb-2'>No runbooks found</div>
              )}
            </div>
          </>
        )}
      </Sider>
      <Content className='p-4 bg-transparent'>
        {runbooks.length === 0 ? (
          <div className='flex w-full h-full items-center justify-center text-sm text-gray-500'>
            No runbooks found
          </div>
        ) : (
          <>
            {editingRunbook ? (
              <div className='px-4 h-full'>
                <div className='flex w-full justify-end gap-2 mt-2'>
                  {isEditing ? (
                    <>
                      <Button
                        type='primary'
                        icon={<FontAwesomeIcon icon={faSave} />}
                        onClick={async () => {
                          const runbooks = [...getInstanceRunbooks(editingRunbook.instanceId)];
                          runbooks[editingRunbook.index] = new Runbook({
                            ...runbooks[editingRunbook.index],
                            name: editedName,
                            content: editedContent,
                            appliedTo: editedAppliedTo
                          });
                          await patchInstanceSpec({
                            instanceId: editingRunbook.instanceId,
                            data: {
                              kubeVisionConfig: {
                                aiConfig: {
                                  runbooks: runbooks.map(
                                    (r) =>
                                      new Runbook({
                                        name: r.name,
                                        content: r.content,
                                        appliedTo: r.appliedTo
                                      })
                                  )
                                }
                              }
                            }
                          });
                          setEditingRunbook({
                            ...editingRunbook,
                            name: editedName,
                            content: editedContent,
                            appliedTo: editedAppliedTo
                          });
                          setIsEditing(false);
                          setEditedContent(editingRunbook.content);
                          setEditedName(editingRunbook.name);
                          setEditedAppliedTo(editingRunbook.appliedTo);
                          refetch();
                        }}
                      >
                        Save
                      </Button>
                      <Button
                        type='default'
                        onClick={() => {
                          setIsEditing(false);
                          setEditedContent(editingRunbook.content);
                          setEditedName(editingRunbook.name);
                          setEditedAppliedTo(
                            editingRunbook.appliedTo || {
                              argocdApplications: [],
                              k8sNamespaces: [],
                              clusters: []
                            }
                          );
                        }}
                      >
                        Cancel
                      </Button>
                    </>
                  ) : (
                    <>
                      <Button
                        type='default'
                        icon={<FontAwesomeIcon icon={faSave} />}
                        onClick={() => {
                          setIsEditing(true);
                          setEditedContent(editingRunbook.content);
                          setEditedName(editingRunbook.name);
                          setEditedAppliedTo(
                            editingRunbook.appliedTo || {
                              argocdApplications: [],
                              k8sNamespaces: [],
                              clusters: []
                            }
                          );
                        }}
                      >
                        Edit
                      </Button>
                      <Button
                        type='primary'
                        icon={<FontAwesomeIcon icon={faTrash} />}
                        onClick={() =>
                          show((props) => (
                            <Modal
                              title='Delete Runbook'
                              open={props.visible}
                              onOk={async () => {
                                const runbooks = [
                                  ...getInstanceRunbooks(editingRunbook.instanceId)
                                ];
                                runbooks.splice(editingRunbook.index, 1);
                                await patchInstanceSpec({
                                  instanceId: editingRunbook.instanceId,
                                  data: {
                                    kubeVisionConfig: {
                                      aiConfig: {
                                        runbooks: runbooks
                                      }
                                    }
                                  }
                                });
                                refetch();
                                setEditingRunbook(null);
                                props.hide();
                              }}
                              onCancel={props.hide}
                            >
                              <p>
                                Are you sure you want to delete this runbook? This action cannot be
                                undone.
                              </p>
                            </Modal>
                          ))
                        }
                      >
                        Delete
                      </Button>
                    </>
                  )}
                </div>
                <div className='text-sm mb-2 font-semibold'>Name</div>
                <Input
                  className='mb-4'
                  value={isEditing ? editedName : editingRunbook.name}
                  readOnly={!isEditing}
                  onChange={(e) => isEditing && setEditedName(e.target.value)}
                />
                <div className='text-sm mb-2 font-semibold'>Applied To</div>
                <div className='flex flex-row gap-4 items-center mb-4'>
                  <div className='flex flex-row gap-2 p-2 bg-neutral-100 rounded-md'>
                    <div>
                      Argo CD Apps:{' '}
                      {isEditing ? (
                        <Select
                          value={editedAppliedTo.argocdApplications || []}
                          options={[
                            { label: '*', value: '*' },
                            ...applications.map((application) => ({
                              label: application,
                              value: application
                            }))
                          ]}
                          onChange={(v) => {
                            setEditedAppliedTo({ ...editedAppliedTo, argocdApplications: v });
                          }}
                          className='inline-block min-w-[160px]'
                          mode='tags'
                        />
                      ) : (
                        <span className='text-gray-500 min-w-[100px] inline-block'>
                          {editingRunbook.appliedTo.argocdApplications?.length > 0
                            ? editingRunbook.appliedTo.argocdApplications.join(', ')
                            : 'none'}
                        </span>
                      )}
                    </div>
                  </div>
                  <div className='flex flex-row gap-2 p-2 bg-neutral-100 rounded-md'>
                    <div>
                      K8S Namespaces:{' '}
                      {isEditing ? (
                        <Select
                          value={editedAppliedTo.k8sNamespaces || []}
                          options={[
                            { label: '*', value: '*' },
                            ...namespaces.map((namespace) => ({
                              label: namespace,
                              value: namespace
                            }))
                          ]}
                          onChange={(v) =>
                            setEditedAppliedTo({ ...editedAppliedTo, k8sNamespaces: v })
                          }
                          className='inline-block min-w-[160px]'
                          mode='tags'
                        />
                      ) : (
                        <span className='text-gray-500 inline-block'>
                          {editingRunbook.appliedTo.k8sNamespaces?.length > 0
                            ? editingRunbook.appliedTo.k8sNamespaces.join(', ')
                            : 'none'}
                        </span>
                      )}
                    </div>
                    <div>
                      Clusters:{' '}
                      {isEditing ? (
                        <Select
                          value={editedAppliedTo.clusters || []}
                          options={[
                            { label: '*', value: '*' },
                            ...clusters.map((cluster) => ({
                              label: cluster,
                              value: cluster
                            }))
                          ]}
                          onChange={(v) => setEditedAppliedTo({ ...editedAppliedTo, clusters: v })}
                          className='inline-block min-w-[160px]'
                          mode='tags'
                        />
                      ) : (
                        <span className='text-gray-500 inline-block'>
                          {editingRunbook.appliedTo.clusters?.length > 0
                            ? editingRunbook.appliedTo.clusters.join(', ')
                            : 'none'}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
                <div className='text-sm mb-2 font-semibold'>Content</div>

                <div className='h-[calc(100%-200px)]'>
                  <CodeEditor
                    theme={isDarkTheme ? 'github_dark' : 'github_light_default'}
                    style={{ height: '100%' }}
                    mode={{
                      name: 'markdown',
                      options: {}
                    }}
                    value={isEditing ? editedContent : editingRunbook.content}
                    readOnly={!isEditing}
                    onChange={(v) => isEditing && setEditedContent(v)}
                  />
                </div>
              </div>
            ) : (
              <div className='flex w-full h-full items-center justify-center text-sm text-gray-500'>
                No runbook selected
              </div>
            )}
          </>
        )}
      </Content>
    </Layout>
  );
};
