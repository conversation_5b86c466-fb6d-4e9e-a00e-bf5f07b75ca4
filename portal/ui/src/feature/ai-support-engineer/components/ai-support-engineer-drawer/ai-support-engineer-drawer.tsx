import { PlainMessage } from '@bufbuild/protobuf';
import {
  faGripVertical,
  faHistory,
  faPlus,
  faColumns,
  faRobot
} from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { useQueryClient } from '@tanstack/react-query';
import { Button, Drawer, Space, Tabs, ConfigProvider, Modal } from 'antd';
import React, { useEffect, useState, useCallback, useRef } from 'react';

import {
  useListAIConversationsQuery,
  useCreateAIConversationMutation,
  useCreateAIMessageMutation
} from '@ui/lib/apiclient/organization/ai-queries';
import {
  AIMessage,
  AIMessageContext,
  IncidentStatus
} from '@ui/lib/apiclient/organization/v1/organization_pb';
import { queryKeys } from '@ui/lib/apiclient/query-keys';
import { useAISupportEngineerContext } from '@ui/lib/context/ai-support-engineer-context';
import { useModal } from '@ui/lib/hooks';
import { useNativeSearchParams } from '@ui/lib/hooks/use-native-search-params';
import { useLocalStorage } from '@ui/lib/utils';

import { useResizeDrawer } from '../../hooks/use-resize-drawer';
import { ConversationTabItem } from '../conversation-tab-item/conversation-tab-item';
import { ConversationTabLabel } from '../conversation-tab-item/conversation-tab-label';
import { FloatingActionButton } from '../floating-action-button/floating-action-button';
import { HistoryPanelFilter } from '../history-panel/filter-modal';
import { HistoryPanel } from '../history-panel/history-panel';

import './ai-support-engineer-drawer.less';

interface AISupportEngineerDrawerProps {
  open: boolean;
  clearSelection: () => void;
  selectedConversationId?: string;
  lastOpenConversationId?: string;
  setIsOpen: (open: boolean, selectedConversationId?: string) => void;
}

const MAX_TABS = 5;

export const AISupportEngineerDrawer = ({
  open,
  clearSelection,
  setIsOpen,
  selectedConversationId,
  lastOpenConversationId
}: AISupportEngineerDrawerProps) => {
  const { isKubeVisionEnabled, isDarkTheme, organizationId, instanceId, defaultContexts, isInAKP } =
    useAISupportEngineerContext();

  const createConversationMutation = useCreateAIConversationMutation();
  const createMessageMutation = useCreateAIMessageMutation();
  const [searchParams, setSearchParams] = useNativeSearchParams();
  const showHistory = searchParams.get('akuity-chat-history') !== 'false';
  const [tabIdsStr, setTabIdsStr] = useLocalStorage('akuity-chat-tab', '');
  const tabs = new Set(tabIdsStr.split(',').filter((id) => !!id));
  const isInitializingRef = useRef(false);

  const setTabs = (val: Set<string> | ((prev: Set<string>) => Set<string>)) => {
    const prev = tabIdsStr.split(',').filter((id) => !!id);
    const updated = typeof val === 'function' ? val(new Set(prev)) : val;
    setTabIdsStr(Array.from(updated).join(','));
  };

  const setShowHistory = (val: boolean) => {
    const newSearchParams = new URLSearchParams(searchParams);
    if (val) {
      newSearchParams.delete('akuity-chat-history');
    } else {
      newSearchParams.set('akuity-chat-history', 'false');
    }
    setSearchParams(newSearchParams);
  };

  const [historyPanelFilter, _setHistoryPanelFilter] = useState<HistoryPanelFilter>({
    incidentOnly: false,
    incidentStatus: IncidentStatus.UNSPECIFIED,
    incidentApplication: '',
    incidentNamespace: '',
    titleContains: '',
    offset: 0,
    limit: 20
  });
  const setHistoryPanelFilter = (filter: HistoryPanelFilter) => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set('akuity-chat-incidentOnly', filter.incidentOnly.toString());
    newSearchParams.set(
      'akuity-chat-incidentStatus',
      filter.incidentStatus === IncidentStatus.RESOLVED
        ? 'resolved'
        : filter.incidentStatus === IncidentStatus.UNRESOLVED
          ? 'unresolved'
          : ''
    );
    newSearchParams.set('akuity-chat-application', filter.incidentApplication);
    newSearchParams.set('akuity-chat-namespace', filter.incidentNamespace);
    newSearchParams.set('akuity-chat-titleContains', filter.titleContains);
    newSearchParams.set('akuity-chat-offset', filter.offset.toString());
    setSearchParams(newSearchParams);
    _setHistoryPanelFilter(filter);
  };

  useEffect(() => {
    _setHistoryPanelFilter({
      incidentOnly: searchParams.get('akuity-chat-incidentOnly') === 'true',
      incidentStatus:
        searchParams.get('akuity-chat-incidentStatus') === 'resolved'
          ? IncidentStatus.RESOLVED
          : searchParams.get('akuity-chat-incidentStatus') === 'unresolved'
            ? IncidentStatus.UNRESOLVED
            : IncidentStatus.UNSPECIFIED,
      incidentApplication: searchParams.get('akuity-chat-application') || '',
      incidentNamespace: searchParams.get('akuity-chat-namespace') || '',
      titleContains: searchParams.get('akuity-chat-titleContains') || '',
      offset: Number(searchParams.get('akuity-chat-offset')) || 0,
      limit: 20
    });
  }, [open]);

  const [isHistoryMounted, setIsHistoryMounted] = useState(false);
  const [isHorizontalLayout, setIsHorizontalLayout] = useState(false);
  const { width, handleMouseDown, handleTouchStart } = useResizeDrawer({ isHorizontalLayout });
  const { show } = useModal();

  const queryClient = useQueryClient();

  const {
    data: conversationsData,
    refetch: refetchConversations,
    isLoading: isConversationsLoading
  } = useListAIConversationsQuery(historyPanelFilter, {
    enabled: open
  });

  useEffect(() => {
    if (isConversationsLoading || createConversationMutation.isPending || isInitializingRef.current)
      return;
    if (open && conversationsData?.conversations) {
      // open the first conversation if no conversation is selected
      if (!selectedConversationId && conversationsData?.conversations.length > 0) {
        setIsOpen(true, [...tabs][0] || conversationsData.conversations[0].id);
      }
    }

    // create the conversation if no conversation exist
    if (open && !selectedConversationId && conversationsData?.conversations?.length === 0) {
      isInitializingRef.current = true;
      createConversationMutation
        .mutateAsync({
          organizationId,
          instanceId: instanceId,
          contexts: defaultContexts,
          incident: false
        })
        .then((response) => {
          refetchConversations();
          setIsOpen(true, response.conversation.id);
        })
        .finally(() => {
          isInitializingRef.current = false;
        });
    }
  }, [open, conversationsData?.conversations, selectedConversationId, isConversationsLoading]);

  useEffect(() => {
    if (isConversationsLoading) return;

    // if a conversation is selected then automatically add it to the tabs
    if (selectedConversationId && !tabs.has(selectedConversationId)) {
      setTabs((prev) => prev.add(selectedConversationId));
    }
  }, [selectedConversationId, tabs, conversationsData?.conversations, isConversationsLoading]);

  // Handle history panel visibility changes
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    if (showHistory) {
      // When history panel opens, refresh the conversation list
      timeoutId = setTimeout(() => {
        refetchConversations();
      }, 100);
    }

    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [showHistory]);

  const handleNewConversation = async () => {
    if (createConversationMutation.isPending) return;
    const response = await createConversationMutation.mutateAsync({
      organizationId,
      instanceId,
      contexts: defaultContexts,
      incident: false
    });

    await refetchConversations();

    setIsOpen(true, response.conversation.id);
  };

  const handleSendMessage = async ({
    message,
    contexts
  }: {
    message: string;
    contexts: PlainMessage<AIMessageContext>[];
  }) => {
    if (!selectedConversationId) return;

    await createMessageMutation.mutateAsync({
      organizationId,
      instanceId,
      conversationId: selectedConversationId,
      content: message,
      contexts
    });

    refetchConversations();
  };

  const handleFloatingButtonClick = () => {
    if (!isKubeVisionEnabled) {
      show(({ visible, hide }) => (
        <Modal
          title={`Akuity Intelligence ${isInAKP ? 'feature' : 'extension'} is disabled`}
          open={visible}
          onOk={hide}
          onCancel={hide}
          okText='OK'
        >
          <p>
            {isInAKP
              ? 'Akuity Intelligence is not enabled for any Argo CD instances or clusters. Please enable this feature for at least one instance and cluster.'
              : 'Akuity Intelligence extension is is disabled. Please install it to use Akuity Intelligence.'}
          </p>
        </Modal>
      ));
      return;
    }
    if (!open) {
      setIsOpen(true, lastOpenConversationId);
    }
  };

  const handleSelectChat = (messages: AIMessage[], conversationId: string) => {
    setIsOpen(true, conversationId);
  };

  const onEdit = async (
    targetKey: React.MouseEvent | React.KeyboardEvent | string,
    action: 'add' | 'remove'
  ) => {
    if (action === 'add' && tabs.size < MAX_TABS) {
      const response = await createConversationMutation.mutateAsync({
        organizationId,
        instanceId,
        contexts: defaultContexts,
        incident: false
      });
      await refetchConversations();

      setIsOpen(true, response.conversation.id);
    } else if (action === 'remove' && typeof targetKey === 'string') {
      const newTabs = new Set([...tabs]);
      newTabs.delete(targetKey);
      setTabs(newTabs);
      if (selectedConversationId === targetKey) {
        setIsOpen(true, [...newTabs][newTabs.size - 1]);
      }
    }
  };

  // Add effect to handle history panel mounting/unmounting
  useEffect(() => {
    if (showHistory) {
      setIsHistoryMounted(true);
    } else {
      const timer = setTimeout(() => {
        setIsHistoryMounted(false);
      }, 300);
      return () => clearTimeout(timer);
    }
  }, [showHistory]);

  const removeConversationFromTabs = useCallback(
    (conversationId: string) => {
      const newTabs = new Set([...tabs]);
      newTabs.delete(conversationId);
      const newSelectedConversationId = [...newTabs][newTabs.size - 1];
      setTabs(newTabs);
      setIsOpen(open, newSelectedConversationId);
    },
    [open, setIsOpen, tabs]
  );

  const handleConversationDelete = useCallback(
    async (conversationId: string) => {
      await refetchConversations();
      removeConversationFromTabs(conversationId);
    },
    [refetchConversations, removeConversationFromTabs]
  );

  return (
    <>
      {/* TODO: The feature has been moved to ArgoCD and Kargo, and is hidden on AKP using a boolean flag in case we reverse the decision. Related to issue #8105. */}
      {!open && !isInAKP && (
        <FloatingActionButton
          onClick={handleFloatingButtonClick}
          key={`${window.innerWidth}-${window.innerHeight}`}
        />
      )}

      <div
        className={isDarkTheme ? 'dark' : ''}
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          display: `${open ? 'initial' : 'none'}`
        }}
      >
        {isHistoryMounted && (
          <div
            className={`history-panel-container ${
              showHistory ? 'animate-slide-in' : 'animate-slide-out'
            }`}
          >
            <HistoryPanel
              onClose={() => setShowHistory(false)}
              clearSelection={clearSelection}
              onSelectChat={handleSelectChat}
              onConversationListUpdate={refetchConversations}
              onConversationUpdate={(conversationId) => {
                queryClient.invalidateQueries({
                  queryKey: queryKeys.ai.getConversation(organizationId, instanceId, conversationId)
                    .queryKey
                });

                refetchConversations();
              }}
              onConversationDelete={handleConversationDelete}
              conversations={conversationsData?.conversations || []}
              conversationsTotalCount={Number(conversationsData?.count || 0)}
              drawerWidth={width}
              isLoading={isConversationsLoading}
              filter={historyPanelFilter}
              onFilterChange={(filter) => {
                setHistoryPanelFilter(filter);
              }}
              selectedConversationId={selectedConversationId}
            />
          </div>
        )}
        <Drawer
          className={`ai-support-engineer-drawer ${isDarkTheme ? 'dark' : ''}`}
          title={
            <Space>
              <FontAwesomeIcon icon={faRobot} />
              Akuity Intelligence
            </Space>
          }
          placement='right'
          onClose={() => {
            const searchParams = new URLSearchParams(window.location.search);
            for (const [key] of searchParams.entries()) {
              if (
                key.startsWith('akuity-chat-') &&
                key !== 'akuity-chat' &&
                key !== 'akuity-chat-history'
              ) {
                searchParams.delete(key);
              }
            }
            setSearchParams(searchParams);
            setIsOpen(false);
          }}
          open={open}
          width={width}
          zIndex={1001}
          extra={
            <Space>
              <Button
                icon={<FontAwesomeIcon icon={faColumns} />}
                onClick={() => setIsHorizontalLayout(!isHorizontalLayout)}
                type={!isHorizontalLayout ? 'default' : 'primary'}
              >
                Layout
              </Button>
              <Button
                icon={<FontAwesomeIcon icon={faHistory} />}
                onClick={() => setShowHistory(!showHistory)}
                type={showHistory ? 'primary' : 'default'}
              >
                History
              </Button>
              <Button
                type='primary'
                icon={<FontAwesomeIcon icon={faPlus} />}
                onClick={handleNewConversation}
              >
                New Conversation
              </Button>
            </Space>
          }
        >
          <ConfigProvider
            theme={{
              components: {
                Tabs: {
                  cardHeight: 20,
                  cardPaddingSM: '2px 16px',
                  colorText: '#64748b',
                  colorTextDescription: '#94a3b8',
                  colorTextHeading: '#475569',
                  colorPrimary: '#475569'
                }
              }
            }}
          >
            <Tabs
              type='editable-card'
              size='small'
              onChange={(id) => setIsOpen(true, id)}
              activeKey={selectedConversationId}
              onEdit={onEdit}
              items={[...tabs].map((item) => {
                return {
                  key: item,
                  label: (
                    <ConversationTabLabel
                      conversationId={item}
                      onError={() => {
                        removeConversationFromTabs(item);
                      }}
                    />
                  ),
                  closable: tabs.size > 1,
                  children: (
                    <ConversationTabItem
                      isSelected={item === selectedConversationId}
                      conversationId={item}
                      isHorizontalLayout={isHorizontalLayout}
                      onSendMessage={(message, contexts) => {
                        handleSendMessage({ message, contexts });
                      }}
                      onConversationUpdate={() => {
                        queryClient.invalidateQueries({
                          queryKey: queryKeys.ai.getConversation(organizationId, instanceId, item)
                            .queryKey
                        });
                        refetchConversations();
                      }}
                      onConversationDelete={() => {
                        handleConversationDelete(item);
                      }}
                    />
                  )
                };
              })}
              hideAdd={tabs.size >= MAX_TABS}
              className='h-full flex flex-col'
            />
          </ConfigProvider>

          <div
            className='resize-handle'
            onMouseDown={handleMouseDown}
            onTouchStart={handleTouchStart}
          >
            {Array(4)
              .fill(null)
              .map((_, index) => (
                <FontAwesomeIcon key={index} icon={faGripVertical} className='text-gray-400' />
              ))}
          </div>
        </Drawer>
      </div>
    </>
  );
};
