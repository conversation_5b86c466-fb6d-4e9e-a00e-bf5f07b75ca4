/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

/** @default "UNSPECIFIED" */
export enum V1Type {
  UNSPECIFIED = "UNSPECIFIED",
  ID = "ID",
  NAME = "NAME",
}

/** @default "STATUS_CODE_UNSPECIFIED" */
export enum ReconciliationV1StatusCode {
  STATUS_CODE_UNSPECIFIED = "STATUS_CODE_UNSPECIFIED",
  STATUS_CODE_SUCCESSFUL = "STATUS_CODE_SUCCESSFUL",
  STATUS_CODE_PROGRESSING = "STATUS_CODE_PROGRESSING",
  STATUS_CODE_FAILED = "STATUS_CODE_FAILED",
}

/** @default "PRUNE_RESOURCE_TYPE_UNSPECIFIED" */
export enum KargoV1PruneResourceType {
  PRUNE_RESOURCE_TYPE_UNSPECIFIED = "PRUNE_RESOURCE_TYPE_UNSPECIFIED",
  PRUNE_RESOURCE_TYPE_ALL = "PRUNE_RESOURCE_TYPE_ALL",
  PRUNE_RESOURCE_TYPE_AGENTS = "PRUNE_RESOURCE_TYPE_AGENTS",
  PRUNE_RESOURCE_TYPE_PROJECTS = "PRUNE_RESOURCE_TYPE_PROJECTS",
  PRUNE_RESOURCE_TYPE_WAREHOUSES = "PRUNE_RESOURCE_TYPE_WAREHOUSES",
  PRUNE_RESOURCE_TYPE_STAGES = "PRUNE_RESOURCE_TYPE_STAGES",
  PRUNE_RESOURCE_TYPE_ANALYSIS_TEMPLATES = "PRUNE_RESOURCE_TYPE_ANALYSIS_TEMPLATES",
  PRUNE_RESOURCE_TYPE_PROMOTION_TASKS = "PRUNE_RESOURCE_TYPE_PROMOTION_TASKS",
  PRUNE_RESOURCE_TYPE_CLUSTER_PROMOTION_TASKS = "PRUNE_RESOURCE_TYPE_CLUSTER_PROMOTION_TASKS",
  PRUNE_RESOURCE_TYPE_REPO_CREDENTIALS = "PRUNE_RESOURCE_TYPE_REPO_CREDENTIALS",
}

/** @default "STATUS_CODE_UNSPECIFIED" */
export enum HealthV1StatusCode {
  STATUS_CODE_UNSPECIFIED = "STATUS_CODE_UNSPECIFIED",
  STATUS_CODE_HEALTHY = "STATUS_CODE_HEALTHY",
  STATUS_CODE_PROGRESSING = "STATUS_CODE_PROGRESSING",
  STATUS_CODE_DEGRADED = "STATUS_CODE_DEGRADED",
  STATUS_CODE_UNKNOWN = "STATUS_CODE_UNKNOWN",
}

/** @default "PRUNE_RESOURCE_TYPE_UNSPECIFIED" */
export enum ArgocdV1PruneResourceType {
  PRUNE_RESOURCE_TYPE_UNSPECIFIED = "PRUNE_RESOURCE_TYPE_UNSPECIFIED",
  PRUNE_RESOURCE_TYPE_ALL = "PRUNE_RESOURCE_TYPE_ALL",
  PRUNE_RESOURCE_TYPE_CLUSTERS = "PRUNE_RESOURCE_TYPE_CLUSTERS",
  PRUNE_RESOURCE_TYPE_REPO_CREDENTIAL_SECRETS = "PRUNE_RESOURCE_TYPE_REPO_CREDENTIAL_SECRETS",
  PRUNE_RESOURCE_TYPE_CONFIG_MANAGEMENT_PLUGINS = "PRUNE_RESOURCE_TYPE_CONFIG_MANAGEMENT_PLUGINS",
  PRUNE_RESOURCE_TYPE_APPLICATIONS = "PRUNE_RESOURCE_TYPE_APPLICATIONS",
  PRUNE_RESOURCE_TYPE_APPLICATION_SETS = "PRUNE_RESOURCE_TYPE_APPLICATION_SETS",
  PRUNE_RESOURCE_TYPE_APP_PROJECTS = "PRUNE_RESOURCE_TYPE_APP_PROJECTS",
}

/** @default "WORKSPACE_MEMBER_ROLE_UNSPECIFIED" */
export enum WorkspaceMemberRole {
  WORKSPACE_MEMBER_ROLE_UNSPECIFIED = "WORKSPACE_MEMBER_ROLE_UNSPECIFIED",
  WORKSPACE_MEMBER_ROLE_MEMBER = "WORKSPACE_MEMBER_ROLE_MEMBER",
  WORKSPACE_MEMBER_ROLE_ADMIN = "WORKSPACE_MEMBER_ROLE_ADMIN",
}

/** @default "TIMELINE_EVENT_TYPE_UNSPECIFIED" */
export enum TimelineEventType {
  TIMELINE_EVENT_TYPE_UNSPECIFIED = "TIMELINE_EVENT_TYPE_UNSPECIFIED",
  TIMELINE_EVENT_TYPE_AUDIT_LOG = "TIMELINE_EVENT_TYPE_AUDIT_LOG",
  TIMELINE_EVENT_TYPE_SYNC_OPERATION = "TIMELINE_EVENT_TYPE_SYNC_OPERATION",
  TIMELINE_EVENT_TYPE_HEALTH_CHANGED = "TIMELINE_EVENT_TYPE_HEALTH_CHANGED",
  TIMELINE_EVENT_TYPE_SPEC_CHANGED = "TIMELINE_EVENT_TYPE_SPEC_CHANGED",
  TIMELINE_EVENT_TYPE_NODE = "TIMELINE_EVENT_TYPE_NODE",
  TIMELINE_EVENT_TYPE_KARGO_PROMOTION = "TIMELINE_EVENT_TYPE_KARGO_PROMOTION",
}

/** @default "TIMELINE_EVENT_SEVERITY_UNSPECIFIED" */
export enum TimelineEventSeverity {
  TIMELINE_EVENT_SEVERITY_UNSPECIFIED = "TIMELINE_EVENT_SEVERITY_UNSPECIFIED",
  TIMELINE_EVENT_SEVERITY_INFO = "TIMELINE_EVENT_SEVERITY_INFO",
  TIMELINE_EVENT_SEVERITY_WARNING = "TIMELINE_EVENT_SEVERITY_WARNING",
  TIMELINE_EVENT_SEVERITY_CRITICAL = "TIMELINE_EVENT_SEVERITY_CRITICAL",
}

/** @default "TENANT_PHASE_UNSPECIFIED" */
export enum TenantPhase {
  TENANT_PHASE_UNSPECIFIED = "TENANT_PHASE_UNSPECIFIED",
  TENANT_PHASE_HEALTHY = "TENANT_PHASE_HEALTHY",
  TENANT_PHASE_PROGRESSING = "TENANT_PHASE_PROGRESSING",
  TENANT_PHASE_DEGRADED = "TENANT_PHASE_DEGRADED",
  TENANT_PHASE_UNKNOWN = "TENANT_PHASE_UNKNOWN",
}

/** @default "SYNC_STATUS_UNSPECIFIED" */
export enum SyncStatus {
  SYNC_STATUS_UNSPECIFIED = "SYNC_STATUS_UNSPECIFIED",
  SYNC_STATUS_SYNCED = "SYNC_STATUS_SYNCED",
  SYNC_STATUS_OUT_OF_SYNC = "SYNC_STATUS_OUT_OF_SYNC",
  SYNC_STATUS_UNKNOWN = "SYNC_STATUS_UNKNOWN",
}

/** @default "SYNC_OPERATION_GROUP_FIELD_UNSPECIFIED" */
export enum SyncOperationGroupField {
  SYNC_OPERATION_GROUP_FIELD_UNSPECIFIED = "SYNC_OPERATION_GROUP_FIELD_UNSPECIFIED",
  SYNC_OPERATION_GROUP_FIELD_APPS = "SYNC_OPERATION_GROUP_FIELD_APPS",
  SYNC_OPERATION_GROUP_FIELD_PROJECTS = "SYNC_OPERATION_GROUP_FIELD_PROJECTS",
  SYNC_OPERATION_GROUP_FIELD_INITIATORS = "SYNC_OPERATION_GROUP_FIELD_INITIATORS",
  SYNC_OPERATION_GROUP_FIELD_INSTANCE_NAMES = "SYNC_OPERATION_GROUP_FIELD_INSTANCE_NAMES",
  SYNC_OPERATION_GROUP_FIELD_STATUS = "SYNC_OPERATION_GROUP_FIELD_STATUS",
}

/** @default "SYNC_OPERATION_FIELD_UNSPECIFIED" */
export enum SyncOperationField {
  SYNC_OPERATION_FIELD_UNSPECIFIED = "SYNC_OPERATION_FIELD_UNSPECIFIED",
  SYNC_OPERATION_FIELD_APPS = "SYNC_OPERATION_FIELD_APPS",
  SYNC_OPERATION_FIELD_PROJECTS = "SYNC_OPERATION_FIELD_PROJECTS",
  SYNC_OPERATION_FIELD_INITIATORS = "SYNC_OPERATION_FIELD_INITIATORS",
  SYNC_OPERATION_FIELD_REPOS = "SYNC_OPERATION_FIELD_REPOS",
  SYNC_OPERATION_FIELD_INSTANCE_NAMES = "SYNC_OPERATION_FIELD_INSTANCE_NAMES",
}

/** @default "SORT_ADDONS_UNSPECIFIED" */
export enum SortAddons {
  SORT_ADDONS_UNSPECIFIED = "SORT_ADDONS_UNSPECIFIED",
  SORT_ADDONS_BY_NAME_ASCENDING = "SORT_ADDONS_BY_NAME_ASCENDING",
  SORT_ADDONS_BY_NAME_DESCENDING = "SORT_ADDONS_BY_NAME_DESCENDING",
}

/** @default "SELECTOR_OPERATOR_UNSPECIFIED" */
export enum SelectorOperator {
  SELECTOR_OPERATOR_UNSPECIFIED = "SELECTOR_OPERATOR_UNSPECIFIED",
  SELECTOR_OPERATOR_IN = "SELECTOR_OPERATOR_IN",
  SELECTOR_OPERATOR_NOT_IN = "SELECTOR_OPERATOR_NOT_IN",
}

/**
 * - SAML_SIGNATURE_ALGORITHM_RSA_SHA256: RSA-SHA256
 *  - SAML_SIGNATURE_ALGORITHM_RSA_SHA1: RSA-SHA1
 * @default "SAML_SIGNATURE_ALGORITHM_UNSPECIFIED"
 */
export enum SAMLSignatureAlgorithm {
  SAML_SIGNATURE_ALGORITHM_UNSPECIFIED = "SAML_SIGNATURE_ALGORITHM_UNSPECIFIED",
  SAML_SIGNATURE_ALGORITHM_RSA_SHA256 = "SAML_SIGNATURE_ALGORITHM_RSA_SHA256",
  SAML_SIGNATURE_ALGORITHM_RSA_SHA1 = "SAML_SIGNATURE_ALGORITHM_RSA_SHA1",
}

/**
 * - SAML_PROTOCOL_BINDING_HTTP_REDIRECT: HTTP-Redirect
 *  - SAML_PROTOCOL_BINDING_HTTP_POST: HTTP-POST
 * @default "SAML_PROTOCOL_BINDING_UNSPECIFIED"
 */
export enum SAMLProtocolBinding {
  SAML_PROTOCOL_BINDING_UNSPECIFIED = "SAML_PROTOCOL_BINDING_UNSPECIFIED",
  SAML_PROTOCOL_BINDING_HTTP_REDIRECT = "SAML_PROTOCOL_BINDING_HTTP_REDIRECT",
  SAML_PROTOCOL_BINDING_HTTP_POST = "SAML_PROTOCOL_BINDING_HTTP_POST",
}

/**
 * - SAML_DIGEST_ALGORITHM_SHA256: SHA256
 *  - SAML_DIGEST_ALGORITHM_SHA1: SHA1
 * @default "SAML_DIGEST_ALGORITHM_UNSPECIFIED"
 */
export enum SAMLDigestAlgorithm {
  SAML_DIGEST_ALGORITHM_UNSPECIFIED = "SAML_DIGEST_ALGORITHM_UNSPECIFIED",
  SAML_DIGEST_ALGORITHM_SHA256 = "SAML_DIGEST_ALGORITHM_SHA256",
  SAML_DIGEST_ALGORITHM_SHA1 = "SAML_DIGEST_ALGORITHM_SHA1",
}

/** @default "RESOURCE_CATEGORY_UNSPECIFIED" */
export enum ResourceCategory {
  RESOURCE_CATEGORY_UNSPECIFIED = "RESOURCE_CATEGORY_UNSPECIFIED",
  RESOURCE_CATEGORY_WORKLOADS = "RESOURCE_CATEGORY_WORKLOADS",
  RESOURCE_CATEGORY_NETWORKING = "RESOURCE_CATEGORY_NETWORKING",
  RESOURCE_CATEGORY_STORAGE = "RESOURCE_CATEGORY_STORAGE",
  RESOURCE_CATEGORY_CONFIGURATION = "RESOURCE_CATEGORY_CONFIGURATION",
  RESOURCE_CATEGORY_RESOURCE_POLICY = "RESOURCE_CATEGORY_RESOURCE_POLICY",
  RESOURCE_CATEGORY_RBAC = "RESOURCE_CATEGORY_RBAC",
  RESOURCE_CATEGORY_CLUSTER_MANAGEMENT = "RESOURCE_CATEGORY_CLUSTER_MANAGEMENT",
  RESOURCE_CATEGORY_ADMISSION = "RESOURCE_CATEGORY_ADMISSION",
  RESOURCE_CATEGORY_CUSTOM_RESOURCE = "RESOURCE_CATEGORY_CUSTOM_RESOURCE",
  RESOURCE_CATEGORY_OTHERS = "RESOURCE_CATEGORY_OTHERS",
}

/** @default "PROMOTION_GROUP_FIELD_UNSPECIFIED" */
export enum PromotionGroupField {
  PROMOTION_GROUP_FIELD_UNSPECIFIED = "PROMOTION_GROUP_FIELD_UNSPECIFIED",
  PROMOTION_GROUP_FIELD_STAGES = "PROMOTION_GROUP_FIELD_STAGES",
  PROMOTION_GROUP_FIELD_PROJECTS = "PROMOTION_GROUP_FIELD_PROJECTS",
  PROMOTION_GROUP_FIELD_INITIATORS = "PROMOTION_GROUP_FIELD_INITIATORS",
  PROMOTION_GROUP_FIELD_INSTANCE_NAMES = "PROMOTION_GROUP_FIELD_INSTANCE_NAMES",
  PROMOTION_GROUP_FIELD_STATUS = "PROMOTION_GROUP_FIELD_STATUS",
}

/** @default "PROMOTION_FIELD_UNSPECIFIED" */
export enum PromotionField {
  PROMOTION_FIELD_UNSPECIFIED = "PROMOTION_FIELD_UNSPECIFIED",
  PROMOTION_FIELD_STAGES = "PROMOTION_FIELD_STAGES",
  PROMOTION_FIELD_PROJECTS = "PROMOTION_FIELD_PROJECTS",
  PROMOTION_FIELD_INITIATORS = "PROMOTION_FIELD_INITIATORS",
  PROMOTION_FIELD_PROMOTIONS = "PROMOTION_FIELD_PROMOTIONS",
  PROMOTION_FIELD_INSTANCE_NAMES = "PROMOTION_FIELD_INSTANCE_NAMES",
}

/** @default "POD_GROUP_BY_UNSPECIFIED" */
export enum PodGroupBy {
  POD_GROUP_BY_UNSPECIFIED = "POD_GROUP_BY_UNSPECIFIED",
  POD_GROUP_BY_CLUSTER = "POD_GROUP_BY_CLUSTER",
  POD_GROUP_BY_AVAILABILITY_ZONE = "POD_GROUP_BY_AVAILABILITY_ZONE",
  POD_GROUP_BY_REGION = "POD_GROUP_BY_REGION",
  POD_GROUP_BY_NODE = "POD_GROUP_BY_NODE",
}

/** @default "POD_FILLER_UNSPECIFIED" */
export enum PodFiller {
  POD_FILLER_UNSPECIFIED = "POD_FILLER_UNSPECIFIED",
  POD_FILLER_USAGE_CPU = "POD_FILLER_USAGE_CPU",
  POD_FILLER_USAGE_MEMORY = "POD_FILLER_USAGE_MEMORY",
  POD_FILLER_STATUS = "POD_FILLER_STATUS",
}

/** @default "ON_CONFLICT_ACTION_UNSPECIFIED" */
export enum OnConflictAction {
  ON_CONFLICT_ACTION_UNSPECIFIED = "ON_CONFLICT_ACTION_UNSPECIFIED",
  ON_CONFLICT_ACTION_SKIP = "ON_CONFLICT_ACTION_SKIP",
  ON_CONFLICT_ACTION_OVERWRITE = "ON_CONFLICT_ACTION_OVERWRITE",
}

/**
 * `NullValue` is a singleton enumeration to represent the null value for the
 * `Value` type union.
 *
 * The JSON representation for `NullValue` is JSON `null`.
 *
 *  - NULL_VALUE: Null value.
 * @default "NULL_VALUE"
 */
export enum NullValue {
  NULL_VALUE = "NULL_VALUE",
}

/** @default "NOTIFICATION_DELIVERY_STATUS_UNSPECIFIED" */
export enum NotificationDeliveryStatus {
  NOTIFICATION_DELIVERY_STATUS_UNSPECIFIED = "NOTIFICATION_DELIVERY_STATUS_UNSPECIFIED",
  NOTIFICATION_DELIVERY_STATUS_SUCCESS = "NOTIFICATION_DELIVERY_STATUS_SUCCESS",
  NOTIFICATION_DELIVERY_STATUS_FAILURE = "NOTIFICATION_DELIVERY_STATUS_FAILURE",
}

/** @default "NOTIFICATION_CATEGORY_UNSPECIFIED" */
export enum NotificationCategory {
  NOTIFICATION_CATEGORY_UNSPECIFIED = "NOTIFICATION_CATEGORY_UNSPECIFIED",
  NOTIFICATION_CATEGORY_BILLING = "NOTIFICATION_CATEGORY_BILLING",
  NOTIFICATION_CATEGORY_MARKETING = "NOTIFICATION_CATEGORY_MARKETING",
  NOTIFICATION_CATEGORY_USAGE_ALERTS = "NOTIFICATION_CATEGORY_USAGE_ALERTS",
  NOTIFICATION_CATEGORY_PRODUCT_UPDATES = "NOTIFICATION_CATEGORY_PRODUCT_UPDATES",
}

/** @default "NODE_GROUP_BY_UNSPECIFIED" */
export enum NodeGroupBy {
  NODE_GROUP_BY_UNSPECIFIED = "NODE_GROUP_BY_UNSPECIFIED",
  NODE_GROUP_BY_CLUSTER = "NODE_GROUP_BY_CLUSTER",
  NODE_GROUP_BY_AVAILABILITY_ZONE = "NODE_GROUP_BY_AVAILABILITY_ZONE",
  NODE_GROUP_BY_REGION = "NODE_GROUP_BY_REGION",
  NODE_GROUP_BY_HOSTNAME = "NODE_GROUP_BY_HOSTNAME",
}

/** @default "NODE_FILLER_UNSPECIFIED" */
export enum NodeFiller {
  NODE_FILLER_UNSPECIFIED = "NODE_FILLER_UNSPECIFIED",
  NODE_FILLER_USAGE_CPU = "NODE_FILLER_USAGE_CPU",
  NODE_FILLER_USAGE_MEMORY = "NODE_FILLER_USAGE_MEMORY",
  NODE_FILLER_ALLOCATED_PODS = "NODE_FILLER_ALLOCATED_PODS",
  NODE_FILLER_USAGE_PODS = "NODE_FILLER_USAGE_PODS",
}

/** @default "NAMESPACE_GROUP_BY_UNSPECIFIED" */
export enum NamespaceGroupBy {
  NAMESPACE_GROUP_BY_UNSPECIFIED = "NAMESPACE_GROUP_BY_UNSPECIFIED",
  NAMESPACE_GROUP_BY_CLUSTER = "NAMESPACE_GROUP_BY_CLUSTER",
}

/** @default "NAMESPACE_FILLER_UNSPECIFIED" */
export enum NamespaceFiller {
  NAMESPACE_FILLER_UNSPECIFIED = "NAMESPACE_FILLER_UNSPECIFIED",
  NAMESPACE_FILLER_USAGE_CPU = "NAMESPACE_FILLER_USAGE_CPU",
  NAMESPACE_FILLER_USAGE_MEMORY = "NAMESPACE_FILLER_USAGE_MEMORY",
  NAMESPACE_FILLER_USAGE_TO_REQUEST_CPU = "NAMESPACE_FILLER_USAGE_TO_REQUEST_CPU",
  NAMESPACE_FILLER_USAGE_TO_REQUEST_MEMORY = "NAMESPACE_FILLER_USAGE_TO_REQUEST_MEMORY",
}

/** @default "MANAGED_SECRET_TYPE_UNSPECIFIED" */
export enum ManagedSecretType {
  MANAGED_SECRET_TYPE_UNSPECIFIED = "MANAGED_SECRET_TYPE_UNSPECIFIED",
  MANAGED_SECRET_TYPE_REPO_CREDS = "MANAGED_SECRET_TYPE_REPO_CREDS",
}

/** @default "KUBERNETES_POD_STATUS_UNSPECIFIED" */
export enum KubernetesPodStatus {
  KUBERNETES_POD_STATUS_UNSPECIFIED = "KUBERNETES_POD_STATUS_UNSPECIFIED",
  KUBERNETES_POD_STATUS_PENDING = "KUBERNETES_POD_STATUS_PENDING",
  KUBERNETES_POD_STATUS_RUNNING = "KUBERNETES_POD_STATUS_RUNNING",
  KUBERNETES_POD_STATUS_SUCCEEDED = "KUBERNETES_POD_STATUS_SUCCEEDED",
  KUBERNETES_POD_STATUS_FAILED = "KUBERNETES_POD_STATUS_FAILED",
}

/** @default "KARGO_AGENT_SIZE_UNSPECIFIED" */
export enum KargoAgentSize {
  KARGO_AGENT_SIZE_UNSPECIFIED = "KARGO_AGENT_SIZE_UNSPECIFIED",
  KARGO_AGENT_SIZE_SMALL = "KARGO_AGENT_SIZE_SMALL",
  KARGO_AGENT_SIZE_MEDIUM = "KARGO_AGENT_SIZE_MEDIUM",
  KARGO_AGENT_SIZE_LARGE = "KARGO_AGENT_SIZE_LARGE",
}

/** @default "INCIDENT_STATUS_UNSPECIFIED" */
export enum IncidentStatus {
  INCIDENT_STATUS_UNSPECIFIED = "INCIDENT_STATUS_UNSPECIFIED",
  INCIDENT_STATUS_RESOLVED = "INCIDENT_STATUS_RESOLVED",
  INCIDENT_STATUS_UNRESOLVED = "INCIDENT_STATUS_UNRESOLVED",
}

/** @default "HEALTH_STATUS_UNSPECIFIED" */
export enum HealthStatus {
  HEALTH_STATUS_UNSPECIFIED = "HEALTH_STATUS_UNSPECIFIED",
  HEALTH_STATUS_HEALTHY = "HEALTH_STATUS_HEALTHY",
  HEALTH_STATUS_DEGRADED = "HEALTH_STATUS_DEGRADED",
  HEALTH_STATUS_MISSING = "HEALTH_STATUS_MISSING",
  HEALTH_STATUS_UNKNOWN = "HEALTH_STATUS_UNKNOWN",
  HEALTH_STATUS_PROGRESSING = "HEALTH_STATUS_PROGRESSING",
  HEALTH_STATUS_SUSPENDED = "HEALTH_STATUS_SUSPENDED",
}

/** @default "GROUP_BY_INTERVAL_UNSPECIFIED" */
export enum GroupByInterval {
  GROUP_BY_INTERVAL_UNSPECIFIED = "GROUP_BY_INTERVAL_UNSPECIFIED",
  GROUP_BY_INTERVAL_MINUTE = "GROUP_BY_INTERVAL_MINUTE",
  GROUP_BY_INTERVAL_HOUR = "GROUP_BY_INTERVAL_HOUR",
  GROUP_BY_INTERVAL_DAY = "GROUP_BY_INTERVAL_DAY",
  GROUP_BY_INTERVAL_WEEK = "GROUP_BY_INTERVAL_WEEK",
  GROUP_BY_INTERVAL_MONTH = "GROUP_BY_INTERVAL_MONTH",
  GROUP_BY_INTERVAL_YEAR = "GROUP_BY_INTERVAL_YEAR",
}

/** @default "FILL_VALUE_UNIT_UNSPECIFIED" */
export enum FillValueUnit {
  FILL_VALUE_UNIT_UNSPECIFIED = "FILL_VALUE_UNIT_UNSPECIFIED",
  FILL_VALUE_UNIT_PERCENTAGE = "FILL_VALUE_UNIT_PERCENTAGE",
  FILL_VALUE_UNIT_COUNT = "FILL_VALUE_UNIT_COUNT",
}

/** @default "FEATURE_STATUS_UNSPECIFIED" */
export enum FeatureStatus {
  FEATURE_STATUS_UNSPECIFIED = "FEATURE_STATUS_UNSPECIFIED",
  FEATURE_STATUS_NOT_AVAILABLE = "FEATURE_STATUS_NOT_AVAILABLE",
  FEATURE_STATUS_ENABLED = "FEATURE_STATUS_ENABLED",
  FEATURE_STATUS_DISABLED = "FEATURE_STATUS_DISABLED",
}

/** @default "EVENT_TYPE_UNSPECIFIED" */
export enum EventType {
  EVENT_TYPE_UNSPECIFIED = "EVENT_TYPE_UNSPECIFIED",
  EVENT_TYPE_ADDED = "EVENT_TYPE_ADDED",
  EVENT_TYPE_MODIFIED = "EVENT_TYPE_MODIFIED",
  EVENT_TYPE_DELETED = "EVENT_TYPE_DELETED",
}

/**
 * - DIRECT_CLUSTER_TYPE_UPBOUND: buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
 * @default "DIRECT_CLUSTER_TYPE_UPBOUND"
 */
export enum DirectClusterType {
  DIRECT_CLUSTER_TYPE_UPBOUND = "DIRECT_CLUSTER_TYPE_UPBOUND",
  DIRECT_CLUSTER_TYPE_KARGO = "DIRECT_CLUSTER_TYPE_KARGO",
}

/** @default "DEPRECATED_INFO_SEVERITY_UNSPECIFIED" */
export enum DeprecatedInfoSeverity {
  DEPRECATED_INFO_SEVERITY_UNSPECIFIED = "DEPRECATED_INFO_SEVERITY_UNSPECIFIED",
  DEPRECATED_INFO_SEVERITY_LOW = "DEPRECATED_INFO_SEVERITY_LOW",
  DEPRECATED_INFO_SEVERITY_MEDIUM = "DEPRECATED_INFO_SEVERITY_MEDIUM",
  DEPRECATED_INFO_SEVERITY_HIGH = "DEPRECATED_INFO_SEVERITY_HIGH",
  DEPRECATED_INFO_SEVERITY_CRITICAL = "DEPRECATED_INFO_SEVERITY_CRITICAL",
}

/** @default "DELIVERY_METHOD_UNSPECIFIED" */
export enum DeliveryMethod {
  DELIVERY_METHOD_UNSPECIFIED = "DELIVERY_METHOD_UNSPECIFIED",
  DELIVERY_METHOD_WEBHOOK = "DELIVERY_METHOD_WEBHOOK",
}

/** @default "CONTAINER_TYPE_UNSPECIFIED" */
export enum ContainerType {
  CONTAINER_TYPE_UNSPECIFIED = "CONTAINER_TYPE_UNSPECIFIED",
  CONTAINER_TYPE_CONTAINER = "CONTAINER_TYPE_CONTAINER",
  CONTAINER_TYPE_INIT_CONTAINER = "CONTAINER_TYPE_INIT_CONTAINER",
  CONTAINER_TYPE_SIDECAR_CONTAINER = "CONTAINER_TYPE_SIDECAR_CONTAINER",
  CONTAINER_TYPE_EPHEMERAL_CONTAINER = "CONTAINER_TYPE_EPHEMERAL_CONTAINER",
}

/** @default "CONTAINER_STATUS_UNSPECIFIED" */
export enum ContainerStatus {
  CONTAINER_STATUS_UNSPECIFIED = "CONTAINER_STATUS_UNSPECIFIED",
  CONTAINER_STATUS_RUNNING = "CONTAINER_STATUS_RUNNING",
  CONTAINER_STATUS_ERROR = "CONTAINER_STATUS_ERROR",
  CONTAINER_STATUS_COMPLETED = "CONTAINER_STATUS_COMPLETED",
  CONTAINER_STATUS_PENDING = "CONTAINER_STATUS_PENDING",
}

/** @default "CLUSTER_SIZE_UNSPECIFIED" */
export enum ClusterSize {
  CLUSTER_SIZE_UNSPECIFIED = "CLUSTER_SIZE_UNSPECIFIED",
  CLUSTER_SIZE_SMALL = "CLUSTER_SIZE_SMALL",
  CLUSTER_SIZE_MEDIUM = "CLUSTER_SIZE_MEDIUM",
  CLUSTER_SIZE_LARGE = "CLUSTER_SIZE_LARGE",
  CLUSTER_SIZE_AUTO = "CLUSTER_SIZE_AUTO",
}

/** @default "CLUSTER_COMMAND_FOR_UNSPECIFIED" */
export enum ClusterCommandFor {
  CLUSTER_COMMAND_FOR_UNSPECIFIED = "CLUSTER_COMMAND_FOR_UNSPECIFIED",
  CLUSTER_COMMAND_FOR_KUBECTL = "CLUSTER_COMMAND_FOR_KUBECTL",
  CLUSTER_COMMAND_FOR_EKS = "CLUSTER_COMMAND_FOR_EKS",
  CLUSTER_COMMAND_FOR_AKUITY_CLI = "CLUSTER_COMMAND_FOR_AKUITY_CLI",
}

/** @default "AGENT_UPDATE_STATUS_UNSPECIFIED" */
export enum AgentUpdateStatus {
  AGENT_UPDATE_STATUS_UNSPECIFIED = "AGENT_UPDATE_STATUS_UNSPECIFIED",
  AGENT_UPDATE_STATUS_UPDATED = "AGENT_UPDATE_STATUS_UPDATED",
  AGENT_UPDATE_STATUS_IN_PROGRESS = "AGENT_UPDATE_STATUS_IN_PROGRESS",
  AGENT_UPDATE_STATUS_DELAYED = "AGENT_UPDATE_STATUS_DELAYED",
}

/** @default "ADDON_TYPE_UNSPECIFIED" */
export enum AddonType {
  ADDON_TYPE_UNSPECIFIED = "ADDON_TYPE_UNSPECIFIED",
  ADDON_TYPE_HELM = "ADDON_TYPE_HELM",
  ADDON_TYPE_KUSTOMIZE = "ADDON_TYPE_KUSTOMIZE",
}

/** @default "AI_CONVERSATION_STEP_STATUS_UNSPECIFIED" */
export enum AIConversationStepStatus {
  AI_CONVERSATION_STEP_STATUS_UNSPECIFIED = "AI_CONVERSATION_STEP_STATUS_UNSPECIFIED",
  AI_CONVERSATION_STEP_STATUS_RUNNING = "AI_CONVERSATION_STEP_STATUS_RUNNING",
  AI_CONVERSATION_STEP_STATUS_FAILED = "AI_CONVERSATION_STEP_STATUS_FAILED",
  AI_CONVERSATION_STEP_STATUS_SUCCEEDED = "AI_CONVERSATION_STEP_STATUS_SUCCEEDED",
}

export interface AIConfig {
  runbooks?: Runbook[];
  incidents?: IncidentsConfig;
}

export interface AIConversation {
  id?: string;
  title?: string;
  /** @format date-time */
  createTime?: string;
  /** @format date-time */
  lastUpdateTime?: string;
  messages?: AIMessage[];
  processing?: boolean;
  public?: boolean;
  ownedByMe?: boolean;
  contexts?: AIMessageContext[];
  processingError?: string;
  incident?: Incident;
  feedbacks?: string[];
  promotionAnalysis?: PromotionAnalysis;
  instanceId?: string;
}

export interface AIConversationStep {
  name?: string;
  status?: AIConversationStepStatus;
  /** @format date-time */
  startTime?: string;
  /** @format date-time */
  endTime?: string;
  summary?: string;
}

export interface AIConversationSuggestion {
  description?: string;
  prompt?: string;
}

export interface AIMessage {
  /** @format date-time */
  createTime?: string;
  role?: string;
  content?: string;
  thinkingProcess?: string;
  suggestedChanges?: AISuggestedChange[];
  suggestedContexts?: AIMessageContext[];
  id?: string;
  steps?: AIConversationStep[];
  username?: string;
  ownedByMe?: boolean;
  isUseful?: boolean;
  runbook?: Runbook;
}

export interface AIMessageContext {
  argoCdApp?: ArgoCDAppContext;
  k8sNamespace?: K8SNamespaceContext;
  kargoProject?: KargoProjectContext;
}

export interface AISettings {
  provider?: string;
  modelVersion?: string;
}

export interface AISuggestedChange {
  context?: AIMessageContext;
  old?: string;
  new?: string;
  patch?: string;
  applied?: boolean;
}

export interface AIUsage {
  /** @format int64 */
  inputTokens?: number;
  /** @format int64 */
  cachedInputTokens?: number;
  /** @format int64 */
  outputTokens?: number;
  /** @format double */
  cost?: number;
}

export interface AKVersion {
  version?: string;
  features?: string[];
}

export interface APIKey {
  id?: string;
  description?: string;
  secret?: string;
  organizationId?: string;
  permissions?: Permissions;
  /** @format date-time */
  createTime?: string;
  /** @format date-time */
  expireTime?: string;
}

export interface AddTeamMemberBody {
  userId?: string;
}

export interface AddTeamMemberResponse {
  teamMember?: TeamMember;
}

export interface AddWorkspaceMemberBody {
  memberRef?: WorkspaceMemberRef;
}

export interface AddWorkspaceMemberResponse {
  workspaceMember?: WorkspaceMember;
}

export interface Addon {
  id?: string;
  organizationId?: string;
  instanceId?: string;
  repoId?: string;
  spec?: AddonSpec;
  statusOperation?: StatusOperation;
  /** @format int64 */
  generation?: number;
  status?: AddonStatus;
  /** @format date-time */
  deleteTime?: string;
  statusSourceUpdate?: StatusSourceUpdate;
  workspaceId?: string;
  addonMarketplaceInstallsId?: string;
  addonMarketplaceInstallStatusInfo?: AddonMarketplaceStatus;
}

export interface AddonError {
  type?: string;
  error?: string;
}

export interface AddonErrorList {
  errors?: AddonError[];
}

export interface AddonEvent {
  type?: string;
  message?: string;
  /** @format date-time */
  time?: string;
  dependencies?: ChartDependency[];
}

export interface AddonFilter {
  sortBy?: SortAddons;
  enabled?: boolean;
  name?: string;
  addonType?: AddonType;
  clusterNameLike?: string;
  clusterLabels?: Record<string, string>;
}

export interface AddonHealthStatus {
  health?: ApplicationsHealth;
  syncStatus?: ApplicationsSyncStatus;
}

export interface AddonMarketplaceInstallConfig {
  repoUrl?: string;
  revision?: string;
  addonName?: string;
  helmChartConfig?: HelmChartInstallConfig;
  type?: string;
  overrides?: AddonMarketplaceInstallOverrides;
}

export interface AddonMarketplaceInstallFilter {
  id?: string;
  repoUrl?: string;
  revision?: string;
  addonName?: string;
  dependency?: ChartDependency[];
  chartDepOrRelation?: boolean;
}

export interface AddonMarketplaceInstallOverrides {
  envs?: string[];
  clusters?: string[];
}

export interface AddonMarketplaceInstallResponse {
  addonInstall?: V1AddonMarketplaceInstall;
}

export interface AddonMarketplaceStatus {
  eventList?: AddonEvent[];
  lastProcessedHash?: string;
  processing?: boolean;
}

export interface AddonRepo {
  id?: string;
  organizationId?: string;
  instanceId?: string;
  spec?: RepoSpec;
  /** @format int64 */
  generation?: number;
  status?: RepoStatus;
  /** @format date-time */
  deleteTime?: string;
}

export interface AddonSpec {
  name?: string;
  clusterOverrides?: Record<string, ManifestSource>;
  envOverrides?: Record<string, ManifestSource>;
  addonType?: string;
  enabled?: boolean;
  clusterSelector?: ClusterSelector;
  appTemplate?: AppTemplate;
  defaultManifest?: ManifestSource;
  helmValues?: HelmValues;
  patchCustomizations?: PatchCustomization[];
}

export interface AddonStatus {
  lastSyncTime?: string;
  lastSyncCommit?: string;
  /** @format int64 */
  clusterCount?: number;
  reconciliationStatus?: ReconciliationV1Status;
  /** @format int64 */
  processedGeneration?: number;
  health?: ApplicationsHealth;
  syncStatus?: ApplicationsSyncStatus;
  lastSourceUpdateStatus?: SourceUpdateResult;
  clusterOverrides?: Record<string, AddonHealthStatus>;
  envOverrides?: Record<string, AddonHealthStatus>;
  hasError?: boolean;
}

export interface AgentAggregatedHealthResponse {
  /** @format uint64 */
  minObservedGeneration?: string;
  healthy?: Record<string, AgentHealthStatus>;
  progressing?: Record<string, AgentHealthStatus>;
  degraded?: Record<string, AgentHealthStatus>;
  unknown?: Record<string, AgentHealthStatus>;
  priorityStatus?: TenantPhase;
}

export interface AgentHealthStatus {
  /** @format uint64 */
  observedGeneration?: string;
  status?: TenantPhase;
  message?: string;
}

export interface AgentPermissionsRule {
  apiGroups?: string[];
  resources?: string[];
  verbs?: string[];
}

export interface AgentResources {
  applicationController?: ApplicationControllerResources;
  repoServer?: RepoServerResources;
}

export interface AgentState {
  version?: string;
  argoCdVersion?: string;
  /** @format date-time */
  observeTime?: string;
  status?: AgentAggregatedHealthResponse;
  agentIds?: string[];
  /** @format uint64 */
  lastUserAppliedGeneration?: string;
  agentResources?: AgentResources;
  updateStatus?: AgentUpdateStatus;
}

export interface AkuityIntelligence {
  aiSupportEngineerEnabled?: boolean;
  enabled?: boolean;
  allowedUsernames?: string[];
  allowedGroups?: string[];
  modelVersion?: string;
}

export interface AkuityIntelligenceExtension {
  enabled?: boolean;
  allowedUsernames?: string[];
  allowedGroups?: string[];
  aiSupportEngineerEnabled?: boolean;
  modelVersion?: string;
}

/**
 * `Any` contains an arbitrary serialized protocol buffer message along with a
 * URL that describes the type of the serialized message.
 *
 * Protobuf library provides support to pack/unpack Any values in the form
 * of utility functions or additional generated methods of the Any type.
 *
 * Example 1: Pack and unpack a message in C++.
 *
 *     Foo foo = ...;
 *     Any any;
 *     any.PackFrom(foo);
 *     ...
 *     if (any.UnpackTo(&foo)) {
 *       ...
 *     }
 *
 * Example 2: Pack and unpack a message in Java.
 *
 *     Foo foo = ...;
 *     Any any = Any.pack(foo);
 *     ...
 *     if (any.is(Foo.class)) {
 *       foo = any.unpack(Foo.class);
 *     }
 *     // or ...
 *     if (any.isSameTypeAs(Foo.getDefaultInstance())) {
 *       foo = any.unpack(Foo.getDefaultInstance());
 *     }
 *
 *  Example 3: Pack and unpack a message in Python.
 *
 *     foo = Foo(...)
 *     any = Any()
 *     any.Pack(foo)
 *     ...
 *     if any.Is(Foo.DESCRIPTOR):
 *       any.Unpack(foo)
 *       ...
 *
 *  Example 4: Pack and unpack a message in Go
 *
 *      foo := &pb.Foo{...}
 *      any, err := anypb.New(foo)
 *      if err != nil {
 *        ...
 *      }
 *      ...
 *      foo := &pb.Foo{}
 *      if err := any.UnmarshalTo(foo); err != nil {
 *        ...
 *      }
 *
 * The pack methods provided by protobuf library will by default use
 * 'type.googleapis.com/full.type.name' as the type URL and the unpack
 * methods only use the fully qualified type name after the last '/'
 * in the type URL, for example "foo.bar.com/x/y.z" will yield type
 * name "y.z".
 *
 * JSON
 * ====
 * The JSON representation of an `Any` value uses the regular
 * representation of the deserialized, embedded message, with an
 * additional field `@type` which contains the type URL. Example:
 *
 *     package google.profile;
 *     message Person {
 *       string first_name = 1;
 *       string last_name = 2;
 *     }
 *
 *     {
 *       "@type": "type.googleapis.com/google.profile.Person",
 *       "firstName": <string>,
 *       "lastName": <string>
 *     }
 *
 * If the embedded message type is well-known and has a custom JSON
 * representation, that representation will be embedded adding a field
 * `value` which holds the custom JSON in addition to the `@type`
 * field. Example (for message [google.protobuf.Duration][]):
 *
 *     {
 *       "@type": "type.googleapis.com/google.protobuf.Duration",
 *       "value": "1.212s"
 *     }
 */
export interface Any {
  /**
   * A URL/resource name that uniquely identifies the type of the serialized
   * protocol buffer message. This string must contain at least
   * one "/" character. The last segment of the URL's path must represent
   * the fully qualified name of the type (as in
   * `path/google.protobuf.Duration`). The name should be in a canonical form
   * (e.g., leading "." is not accepted).
   *
   * In practice, teams usually precompile into the binary all types that they
   * expect it to use in the context of Any. However, for URLs which use the
   * scheme `http`, `https`, or no scheme, one can optionally set up a type
   * server that maps type URLs to message definitions as follows:
   *
   * * If no scheme is provided, `https` is assumed.
   * * An HTTP GET on the URL must yield a [google.protobuf.Type][]
   *   value in binary format, or produce an error.
   * * Applications are allowed to cache lookup results based on the
   *   URL, or have them precompiled into a binary to avoid any
   *   lookup. Therefore, binary compatibility needs to be preserved
   *   on changes to types. (Use versioned type names to manage
   *   breaking changes.)
   *
   * Note: this functionality is not currently available in the official
   * protobuf release, and it is not used for type URLs beginning with
   * type.googleapis.com. As of May 2023, there are no widely used type server
   * implementations and no plans to implement one.
   *
   * Schemes other than `http`, `https` (or the empty scheme) might be
   * used with implementation specific semantics.
   */
  "@type"?: string;
  [key: string]: any;
}

export interface AppControllerAutoScalingConfig {
  resourceMinimum?: Resources;
  resourceMaximum?: Resources;
}

export interface AppCreationOptions {
  onConflict?: OnConflictAction;
}

export interface AppDeletionOptions {
  nonCascade?: boolean;
}

export interface AppInAnyNamespaceConfig {
  enabled?: boolean;
}

export interface AppReconciliationsRateLimiting {
  bucketRateLimiting?: BucketRateLimiting;
  itemRateLimiting?: ItemRateLimiting;
}

export interface AppSetDelegate {
  managedCluster?: ManagedCluster;
}

export interface AppSyncOptions {
  autoSync?: boolean;
  autoHeal?: boolean;
  pruneResources?: boolean;
  syncOptionsList?: string[];
}

export interface AppTemplate {
  nameTemplate?: string;
  creationOptions?: AppCreationOptions;
  deletionOptions?: AppDeletionOptions;
  syncOptions?: AppSyncOptions;
  projectTemplate?: string;
  namespaceTemplate?: string;
  helmOptions?: HelmOptions;
  kustomizeOptions?: KustomizeOptions;
}

export interface ApplicationControllerResources {
  requests?: Resources;
  limits?: Resources;
}

export interface ApplicationSetExtension {
  enabled?: boolean;
}

export interface ApplicationsHealth {
  /** @format int64 */
  healthyCount?: number;
  /** @format int64 */
  degradedCount?: number;
  /** @format int64 */
  progressingCount?: number;
  /** @format int64 */
  unknownCount?: number;
  /** @format int64 */
  suspendedCount?: number;
  /** @format int64 */
  missingCount?: number;
}

export interface ApplicationsStatus {
  /** @format int64 */
  applicationCount?: number;
  /** @format int64 */
  resourcesCount?: number;
  /** @format int64 */
  syncInProgressCount?: number;
  /** @format int64 */
  warningCount?: number;
  /** @format int64 */
  errorCount?: number;
  health?: ApplicationsHealth;
  syncStatus?: ApplicationsSyncStatus;
  /** @format int64 */
  appOfAppCount?: number;
}

export interface ApplicationsSyncStatus {
  /** @format int64 */
  syncedCount?: number;
  /** @format int64 */
  outOfSyncCount?: number;
  /** @format int64 */
  unknownCount?: number;
}

export interface ApplyInstanceBody {
  idType?: V1Type;
  argocd?: object;
  argocdConfigmap?: object;
  argocdRbacConfigmap?: object;
  argocdSecret?: object;
  notificationsConfigmap?: object;
  notificationsSecret?: object;
  imageUpdaterConfigmap?: object;
  imageUpdaterSshConfigmap?: object;
  imageUpdaterSecret?: object;
  clusters?: object[];
  /**
   * prune_clusters is deprecated and will be ignored.
   * Use prune_resource_types instead.
   */
  pruneClusters?: boolean;
  argocdKnownHostsConfigmap?: object;
  argocdTlsCertsConfigmap?: object;
  repoCredentialSecrets?: object[];
  repoTemplateCredentialSecrets?: object[];
  /**
   * prune_repo_credential_secrets is deprecated and will be ignored.
   * Use prune_resource_types instead.
   */
  pruneRepoCredentialSecrets?: boolean;
  configManagementPlugins?: object[];
  pruneResourceTypes?: ArgocdV1PruneResourceType[];
  applicationSetSecret?: object;
  applications?: object[];
  applicationSets?: object[];
  appProjects?: object[];
}

export type ApplyInstanceResponse = object;

export interface ApplyKargoInstanceBody {
  idType?: V1Type;
  kargo?: object;
  agents?: object[];
  pruneResourceTypes?: KargoV1PruneResourceType[];
  kargoConfigmap?: object;
  kargoSecret?: object;
  projects?: object[];
  warehouses?: object[];
  stages?: object[];
  analysisTemplates?: object[];
  promotionTasks?: object[];
  clusterPromotionTasks?: object[];
  repoCredentials?: object[];
}

/** empty */
export type ApplyKargoInstanceResponse = object;

export interface AppsetPlugins {
  name?: string;
  token?: string;
  baseUrl?: string;
  /** @format int32 */
  requestTimeout?: number;
}

export interface AppsetPolicy {
  policy?: string;
  overridePolicy?: boolean;
}

export interface ArgoCDAlertConfig {
  message?: string;
  url?: string;
}

export interface ArgoCDAppContext {
  instanceId?: string;
  name?: string;
}

export interface ArgoCDApplicationInfo {
  name?: string;
  syncStatus?: SyncStatus;
  healthStatus?: HealthStatus;
  link?: string;
}

export interface ArgoCDBannerConfig {
  message?: string;
  url?: string;
  permanent?: boolean;
}

export interface ArgoCDConfigMap {
  adminEnabled?: boolean;
  statusBadge?: ArgoCDStatusBadgeConfig;
  googleAnalytics?: ArgoCDGoogleAnalyticsConfig;
  allowAnonymousUser?: boolean;
  banner?: ArgoCDBannerConfig;
  chat?: ArgoCDAlertConfig;
  instanceLabelKey?: string;
  kustomizeSettings?: ArgoCDKustomizeSettings;
  helmSettings?: ArgoCDHelmSettings;
  resourceSettings?: ArgoCDResourceSettings;
  usersSessionDuration?: string;
  oidcConfig?: string;
  dexConfig?: string;
  webTerminal?: ArgoCDWebTerminalConfig;
  deepLinks?: ArgoCDDeepLinks;
  logsRbacEnabled?: boolean;
}

export interface ArgoCDDeepLinks {
  projectLinks?: DeepLink[];
  applicationLinks?: DeepLink[];
  resourceLinks?: DeepLink[];
}

export interface ArgoCDExtensionInstallEntry {
  id?: string;
  version?: string;
}

export interface ArgoCDGoogleAnalyticsConfig {
  trackingId?: string;
  anonymizeUsers?: boolean;
}

export interface ArgoCDHelmSettings {
  enabled?: boolean;
  valueFileSchemas?: string;
}

export interface ArgoCDKustomizeSettings {
  enabled?: boolean;
  buildOptions?: string;
}

export interface ArgoCDRBACConfigMap {
  defaultPolicy?: string;
  policyCsv?: string;
  scopes?: string[];
  overlayPolicies?: OverlayPolicy[];
}

export interface ArgoCDResourceSettings {
  inclusions?: string;
  exclusions?: string;
  compareOptions?: string;
}

export interface ArgoCDServiceAddonMarketplaceInstallBody {
  config?: AddonMarketplaceInstallConfig;
}

export interface ArgoCDStatusBadgeConfig {
  enabled?: boolean;
  url?: string;
}

export interface ArgoCDWebTerminalConfig {
  enabled?: boolean;
  shells?: string;
}

export interface AssignWorkspace {
  name?: string;
  role?: WorkspaceMemberRole;
}

export interface AssistantMessage {
  role?: string;
  content?: string;
  name?: string;
  toolCalls?: AssistantToolCall[];
  toolCallId?: string;
}

export interface AssistantToolCall {
  id?: string;
  functionName?: string;
  arguments?: string;
}

export interface AuditActor {
  type?: string;
  id?: string;
  ip?: string;
}

export interface AuditDetails {
  message?: string;
  patch?: string;
  actionType?: string;
}

export interface AuditFilters {
  actorId?: string[];
  k8sResource?: ObjectFilter;
  argocdApplication?: ObjectFilter;
  argocdCluster?: ObjectFilter;
  argocdInstance?: ObjectFilter;
  argocdProject?: ObjectFilter;
  member?: ObjectFilter;
  organizationInvite?: ObjectFilter;
  action?: string[];
  actorType?: string[];
  startTime?: string;
  endTime?: string;
  /** @format int64 */
  limit?: number;
  /** @format int64 */
  offset?: number;
  kargoInstance?: ObjectFilter;
  kargoAgent?: ObjectFilter;
  kargoPromotion?: ObjectFilter;
  kargoFreight?: ObjectFilter;
  customRoles?: ObjectFilter;
  notificationCfg?: ObjectFilter;
  apiKeys?: ObjectFilter;
  addons?: ObjectFilter;
  addonRepos?: ObjectFilter;
  addonMarketplaceInstall?: ObjectFilter;
}

export interface AuditLog {
  timestamp?: string;
  action?: string;
  actor?: AuditActor;
  object?: AuditObject;
  details?: AuditDetails;
  /** @format int64 */
  count?: number;
  lastOccurredTimestamp?: string;
}

export interface AuditLogArchive {
  /**
   * * A full date, with non-zero year, month, and day values
   * * A month and day value, with a zero year, such as an anniversary
   * * A year on its own, with zero month and day values
   * * A year and month value, with a zero day, such as a credit card expiration
   * date
   *
   * Related types are [google.type.TimeOfDay][google.type.TimeOfDay] and
   * `google.protobuf.Timestamp`.
   */
  startDate?: Date;
  /**
   * * A full date, with non-zero year, month, and day values
   * * A month and day value, with a zero year, such as an anniversary
   * * A year on its own, with zero month and day values
   * * A year and month value, with a zero day, such as a credit card expiration
   * date
   *
   * Related types are [google.type.TimeOfDay][google.type.TimeOfDay] and
   * `google.protobuf.Timestamp`.
   */
  endDate?: Date;
  presignedUrlExpiration?: string;
  presignedUrl?: string;
  /** @format int64 */
  records?: number;
}

export interface AuditLogArchiveFilters {
  startDate?: string;
  endDate?: string;
  /** @format int64 */
  limit?: number;
  /** @format int64 */
  offset?: number;
}

export interface AuditObjId {
  name?: string;
  kind?: string;
  group?: string;
}

export interface AuditObject {
  type?: string;
  id?: AuditObjId;
  parentId?: AuditParentId;
}

export interface AuditParentId {
  name?: string;
  parentName?: string;
  applicationName?: string;
}

export interface AutoScalerConfig {
  applicationController?: AppControllerAutoScalingConfig;
  repoServer?: RepoServerAutoScalingConfig;
}

export interface AzureADSSOOptions {
  clientId?: string;
  clientSecret?: string;
  azureAdDomain?: string;
  domainAlias?: string;
  domainAliases?: string[];
}

export interface Banner {
  title?: string;
  message?: string;
  closable?: string;
  type?: string;
  links?: BannerLink[];
}

export interface BannerLink {
  name?: string;
  url?: string;
}

export interface BillingCheckoutBody {
  billingName?: string;
  billingEmail?: string;
  plan?: string;
  addons?: SubscriptionAddon[];
}

export interface BillingCheckoutResponse {
  url?: string;
}

export interface BillingDetails {
  email?: string;
  hasActiveSubscription?: boolean;
  metadata?: BillingMetadata;
  customerId?: string;
  billingAuthority?: string;
  manual?: boolean;
  lastFourCardDigits?: string;
  addons?: SubscriptionAddon[];
}

export interface BillingMetadata {
  name?: string;
}

export interface BucketRateLimiting {
  enabled?: boolean;
  /** @format int64 */
  bucketSize?: number;
  /** @format int64 */
  bucketQps?: number;
}

export type CancelSubscriptionBody = object;

/** empty */
export type CancelSubscriptionResponse = object;

export interface ChartDependency {
  name?: string;
  version?: string;
  repository?: string;
  repositoryName?: string;
}

export type ClearAddonStatusSourceHistoryBody = object;

export interface ClearAddonStatusSourceHistoryResponse {
  addon?: Addon;
}

export interface Cluster {
  id?: string;
  name?: string;
  description?: string;
  /** Use data.namespace instead */
  namespace?: string;
  /** Use data.namespace_scoped instead */
  namespaceScoped?: boolean;
  data?: ClusterData;
  /** @format date-time */
  deleteTime?: string;
  /** @format uint64 */
  observedGeneration?: string;
  credentialRotationAllowed?: boolean;
  agentState?: AgentState;
  healthStatus?: HealthV1Status;
  reconciliationStatus?: ReconciliationV1Status;
  /** @format uint64 */
  readonlySettingsChangedGeneration?: string;
  k8sStatus?: ClusterKubernetesStatus;
}

export interface ClusterAddonStatusOperation {
  revision?: string;
  prune?: boolean;
  syncOptions?: string[];
  initiator?: string;
}

export interface ClusterArgoCDNotificationsSettings {
  inClusterSettings?: boolean;
}

export interface ClusterCompatibility {
  ipv6Only?: boolean;
}

export interface ClusterCustomization {
  autoUpgradeDisabled?: boolean;
  kustomization?: object;
  appReplication?: boolean;
  redisTunneling?: boolean;
}

export interface ClusterData {
  size?: ClusterSize;
  labels?: Record<string, string>;
  annotations?: Record<string, string>;
  autoUpgradeDisabled?: boolean;
  kustomization?: object;
  appReplication?: boolean;
  targetVersion?: string;
  redisTunneling?: boolean;
  directClusterSpec?: DirectClusterSpec;
  datadogAnnotationsEnabled?: boolean;
  namespace?: string;
  namespaceScoped?: boolean;
  eksAddonEnabled?: boolean;
  managedClusterConfig?: ManagedClusterConfig;
  maintenanceMode?: boolean;
  multiClusterK8sDashboardEnabled?: boolean;
  autoscalerConfig?: AutoScalerConfig;
  project?: string;
  compatibility?: ClusterCompatibility;
  argocdNotificationsSettings?: ClusterArgoCDNotificationsSettings;
}

export interface ClusterFilter {
  nameLike?: string;
  agentStatus?: TenantPhase[];
  agentVersion?: string[];
  argocdVersion?: string[];
  /** @format int64 */
  limit?: string;
  /** @format int64 */
  offset?: string;
  excludeAgentVersion?: string;
  outdatedManifest?: boolean;
  namespace?: string[];
  namespaceScoped?: boolean;
  labels?: Record<string, string>;
  needReapply?: boolean;
  excludeDirectCluster?: boolean;
}

export interface ClusterKubernetesStatus {
  kubernetesVersion?: string;
  /** @format int64 */
  apiResourceCount?: number;
  /** @format int64 */
  objectCount?: number;
}

export interface ClusterResource {
  instanceId?: string;
  clusterId?: string;
  name?: string;
  namespace?: string;
  group?: string;
  version?: string;
  kind?: string;
  columns?: Record<string, string>;
  argocdApplicationInfo?: ArgoCDApplicationInfo;
  createTime?: string;
  ownerId?: string;
  uid?: string;
  hasChildObjects?: boolean;
  category?: ResourceCategory;
  deprecatedInfo?: DeprecatedInfo;
  deleteTime?: string;
  referenceResources?: string[];
}

export interface ClusterSecretMapping {
  clusters?: ObjectSelector;
  secrets?: ObjectSelector;
}

export interface ClusterSelector {
  nameFilters?: Selector[];
  labelFilters?: Selector[];
}

export interface ColumnInfo {
  name?: string;
  title?: string;
}

export interface Command {
  command?: string[];
  args?: string[];
}

export interface ComponentVersion {
  version?: string;
  label?: string;
  securityAdvisories?: SecurityAdvisory[];
  akVersions?: AKVersion[];
}

/**
 * ConfigManagementPlugin is defined based on argocd config management
 * plugin:https://argo-cd.readthedocs.io/en/stable/operator-manual/config-management-plugins/
 */
export interface ConfigManagementPlugin {
  name?: string;
  enabled?: boolean;
  image?: string;
  spec?: PluginSpec;
}

export interface Container {
  name?: string;
  status?: ContainerStatus;
  type?: ContainerType;
  /** @format double */
  cpuLimit?: number;
  /** @format double */
  cpuRequest?: number;
  /** @format double */
  memoryLimit?: number;
  /** @format double */
  memoryRequest?: number;
  image?: string;
  imageTag?: string;
  podName?: string;
  podId?: string;
  clusterId?: string;
  instanceId?: string;
  id?: string;
  imageDigest?: string;
  /** @format double */
  cpuUsage?: number;
  /** @format double */
  memoryUsage?: number;
  startTime?: string;
  nodeName?: string;
}

export interface CreateAIConversationBody {
  instanceId?: string;
  contexts?: AIMessageContext[];
  incident?: boolean;
  kargoPromotionAnalysis?: KargoPromotionAnalysis;
  runbooks?: string[];
}

export interface CreateAIConversationResponse {
  conversation?: AIConversation;
}

export interface CreateAIMessageBody {
  content?: string;
  contexts?: AIMessageContext[];
  instanceId?: string;
  runbooks?: string[];
}

export type CreateAIMessageResponse = object;

/** Request and response messages */
export interface CreateCustomRoleBody {
  name?: string;
  description?: string;
  policy?: string;
}

export interface CreateCustomRoleResponse {
  customRole?: CustomRole;
}

export type CreateIncidentResponse = object;

export interface CreateInstanceAddonRepoBody {
  spec?: RepoSpec;
}

export interface CreateInstanceAddonRepoResponse {
  addonRepo?: AddonRepo;
}

export interface CreateInstanceBody {
  name?: string;
  version?: string;
  description?: string;
  shard?: string;
}

export interface CreateInstanceClusterBody {
  name?: string;
  description?: string;
  /** Use data.namespace instead */
  namespace?: string;
  /** Use data.namespace_scoped instead */
  namespaceScoped?: boolean;
  data?: ClusterData;
  upsert?: boolean;
  force?: boolean;
}

export interface CreateInstanceClusterResponse {
  cluster?: Cluster;
}

export interface CreateInstanceRepoBody {
  data?: Record<string, string>;
}

export interface CreateInstanceRepoResponse {
  repo?: Repository;
}

export interface CreateInstanceResponse {
  instance?: Instance;
}

export interface CreateKargoInstanceAgentBody {
  name?: string;
  /** Use data.namespace instead */
  namespace?: string;
  description?: string;
  data?: KargoAgentData;
  /** Use UpdateKargoInstanceAgent endpoint instead */
  upsert?: boolean;
}

export interface CreateKargoInstanceAgentResponse {
  agent?: KargoAgent;
}

export interface CreateKargoInstanceBody {
  name?: string;
  version?: string;
  description?: string;
}

export interface CreateKargoInstanceResponse {
  instance?: KargoInstance;
}

export interface CreateManagedSecretBody {
  managedSecret?: ManagedSecret;
  managedSecretData?: Record<string, string>;
}

/** explicitly empty */
export type CreateManagedSecretResponse = object;

export interface CreateNotificationConfigBody {
  name?: string;
  webhook?: WebhookNotificationCreatePayload;
}

export interface CreateNotificationConfigResponse {
  notificationConfig?: NotificationConfig;
}

export interface CreateOrganizationAPIKeyBody {
  description?: string;
  permissions?: Permissions;
  expireInDuration?: string;
}

export interface CreateOrganizationAPIKeyResponse {
  apiKey?: APIKey;
}

export interface CreateOrganizationRequest {
  name?: string;
}

export interface CreateOrganizationResponse {
  organization?: Organization;
}

export interface CreateTeamBody {
  name?: string;
  description?: string;
  customRoles?: string[];
}

export interface CreateTeamResponse {
  userTeam?: UserTeam;
}

export interface CreateWorkspaceAPIKeyBody {
  description?: string;
  permissions?: Permissions;
  expireInDuration?: string;
}

export interface CreateWorkspaceAPIKeyResponse {
  apiKey?: APIKey;
  workspaceId?: string;
}

export interface CreateWorkspaceBody {
  name?: string;
  description?: string;
}

/** Request and response messages */
export interface CreateWorkspaceCustomRoleBody {
  name?: string;
  description?: string;
  policy?: string;
}

export interface CreateWorkspaceCustomRoleResponse {
  customRole?: CustomRole;
  workspaceId?: string;
}

export interface CreateWorkspaceResponse {
  workspace?: Workspace;
}

export interface CrossplaneExtension {
  resources?: CrossplaneExtensionResource[];
}

export interface CrossplaneExtensionResource {
  /**
   * supports glob pattern - argocd uses
   * [minimatch](https://www.npmjs.com/package/minimatch) package to match group
   */
  group?: string;
}

export interface CustomDeprecatedAPI {
  apiVersion?: string;
  newApiVersion?: string;
  deprecatedInKubernetesVersion?: string;
  unavailableInKubernetesVersion?: string;
}

export interface CustomRole {
  id?: string;
  name?: string;
  description?: string;
  policy?: string;
}

export interface CveScanConfig {
  scanEnabled?: boolean;
  rescanInterval?: string;
}

export interface DataDogRolloutsSecret {
  address?: string;
  apiKey?: string;
  appKey?: string;
}

/**
 * Represents a whole or partial calendar date, such as a birthday. The time of
 * day and time zone are either specified elsewhere or are insignificant. The
 * date is relative to the Gregorian Calendar. This can represent one of the
 * following:
 * * A full date, with non-zero year, month, and day values
 * * A month and day value, with a zero year, such as an anniversary
 * * A year on its own, with zero month and day values
 * * A year and month value, with a zero day, such as a credit card expiration
 * date
 *
 * Related types are [google.type.TimeOfDay][google.type.TimeOfDay] and
 * `google.protobuf.Timestamp`.
 */
export interface Date {
  /**
   * Year of the date. Must be from 1 to 9999, or 0 to specify a date without
   * a year.
   * @format int32
   */
  year?: number;
  /**
   * Month of a year. Must be from 1 to 12, or 0 to specify a year without a
   * month and day.
   * @format int32
   */
  month?: number;
  /**
   * Day of a month. Must be from 1 to 31 and valid for the year and month, or 0
   * to specify a year by itself or a year and month where the day isn't
   * significant.
   * @format int32
   */
  day?: number;
}

export interface DeepLink {
  title?: string;
  url?: string;
  description?: string;
  iconClass?: string;
  if?: string;
}

/** explicitly empty */
export type DeleteAIConversationResponse = object;

/** explicitly empty */
export type DeleteAPIKeyResponse = object;

/** explicitly empty */
export type DeleteAddonMarketplaceInstallResponse = object;

/** explicitly empty */
export type DeleteCustomRoleResponse = object;

/** explicitly empty */
export type DeleteInstanceAccountResponse = object;

/** explicitly empty */
export type DeleteInstanceAddonRepoResponse = object;

/** explicitly empty */
export type DeleteInstanceAddonResponse = object;

/** explicitly empty */
export type DeleteInstanceAgentResponse = object;

/** explicitly empty */
export type DeleteInstanceClusterResponse = object;

export type DeleteKubernetesResourceResponse = object;

/** explicitly empty */
export type DeleteManagedSecretResponse = object;

/** explicitly empty */
export type DeleteNotificationConfigResponse = object;

/** empty */
export type DeleteOrganizationDomainResponse = object;

/** empty */
export type DeleteOrganizationResponse = object;

/** explicitly empty */
export type DeleteSSOConfigurationResponse = object;

/** explicitly empty */
export type DeleteTeamResponse = object;

/** empty */
export type DeleteUserResponse = object;

/** explicitly empty */
export type DeleteWorkspaceAPIKeyResponse = object;

/** explicitly empty */
export type DeleteWorkspaceCustomRoleResponse = object;

/** explicitly empty */
export type DeleteWorkspaceResponse = object;

export interface DeprecatedInfo {
  deprecated?: boolean;
  message?: string;
  deprecatedIn?: string;
  unavailableIn?: string;
  migrateTo?: GroupVersionKind;
  clusterId?: string;
  groupVersionKind?: GroupVersionKind;
  /** @format int64 */
  resourceCount?: number;
  kubernetesVersion?: string;
  instanceId?: string;
  severity?: DeprecatedInfoSeverity;
}

export interface DirectClusterSpec {
  clusterType?: DirectClusterType;
  kargoInstanceId?: string;
  server?: string;
  organization?: string;
  token?: string;
  caData?: string;
}

export interface Discover {
  find?: Find;
  /**
   * Use camel case to match the ConfigManagementPlugin definition of argocd
   * buf:lint:ignore FIELD_LOWER_SNAKE_CASE
   */
  fileName?: string;
}

export interface DomainVerification {
  domain?: string;
  verified?: boolean;
}

export interface Dynamic {
  command?: string[];
  args?: string[];
}

export interface EmailNotificationConfig {
  disabled?: boolean;
  disabledCategories?: NotificationCategory[];
}

export interface EnabledCluster {
  instanceId?: string;
  instanceName?: string;
  clusterId?: string;
  clusterName?: string;
  isDegraded?: boolean;
  metricServerUnavailable?: boolean;
  isEnabled?: boolean;
  shard?: string;
  lastRefreshTime?: string;
}

export interface EnsureSSOConfigurationBody {
  autoAddMember?: boolean;
  azureAd?: AzureADSSOOptions;
  okta?: OktaSSOOptions;
  googleWorkspace?: GoogleWorkspaceSSOOptions;
  oidc?: OIDCSSOOptions;
  saml?: SAMLSSOOptions;
}

export interface EnsureSSOConfigurationResponse {
  autoAddMember?: boolean;
  azureAd?: AzureADSSOOptions;
  okta?: OktaSSOOptions;
  googleWorkspace?: GoogleWorkspaceSSOOptions;
  oidc?: OIDCSSOOptions;
  saml?: SAMLSSOOptions;
}

export interface ExportInstanceResponse {
  argocd?: object;
  argocdConfigmap?: object;
  argocdRbacConfigmap?: object;
  notificationsConfigmap?: object;
  imageUpdaterConfigmap?: object;
  imageUpdaterSshConfigmap?: object;
  clusters?: object[];
  argocdKnownHostsConfigmap?: object;
  argocdTlsCertsConfigmap?: object;
  configManagementPlugins?: object[];
  applications?: object[];
  applicationSets?: object[];
  appProjects?: object[];
}

export interface ExportKargoInstanceResponse {
  kargo?: object;
  agents?: object[];
  kargoConfigmap?: object;
  projects?: object[];
  warehouses?: object[];
  stages?: object[];
  analysisTemplates?: object[];
  promotionTasks?: object[];
  clusterPromotionTasks?: object[];
}

export interface ExtensionInfo {
  id?: string;
  name?: string;
  description?: string;
  home?: string;
  icon?: string;
  versions?: ExtensionVersion[];
}

export interface ExtensionVersion {
  version?: string;
  url?: string;
  sha256Sum?: string;
}

export interface FeatureStatuses {
  sso?: FeatureStatus;
  kargo?: FeatureStatus;
  autoscaler?: FeatureStatus;
  k3sProxyInformers?: FeatureStatus;
  aiAssistantStats?: FeatureStatus;
  agentPermissions?: FeatureStatus;
  team?: FeatureStatus;
  selfServeCancel?: FeatureStatus;
  k3sCertCnReset?: FeatureStatus;
  notification?: FeatureStatus;
  multiClusterK8sDashboard?: FeatureStatus;
  clusterAutoscaler?: FeatureStatus;
  fleetManagement?: FeatureStatus;
  aiSupportEngineer?: FeatureStatus;
  secretManagement?: FeatureStatus;
  shards?: string[];
  auditRecordExport?: FeatureStatus;
  workspaces?: FeatureStatus;
  customRoles?: FeatureStatus;
  scopedApiKeys?: FeatureStatus;
  argocdSso?: FeatureStatus;
  argocdHaControlPlane?: FeatureStatus;
  akuityArgocdExtensions?: FeatureStatus;
  appOfApps?: FeatureStatus;
  applicationSetController?: FeatureStatus;
  argocdCustomSubdomain?: FeatureStatus;
  argocdCustomDomain?: FeatureStatus;
  argocdFlexibleArchitecture?: FeatureStatus;
  argocdAgentStateReplication?: FeatureStatus;
  argocdDeepLinks?: FeatureStatus;
  argocdCustomStyles?: FeatureStatus;
  configManagementPlugins?: FeatureStatus;
  auditArchive?: FeatureStatus;
  argocdClusterIntegration?: FeatureStatus;
  pgpool?: FeatureStatus;
  pgbouncer?: FeatureStatus;
  multiFactorAuth?: FeatureStatus;
  kargoAnalysisLogs?: FeatureStatus;
  kargoEnterprise?: FeatureStatus;
  oidcMap?: FeatureStatus;
  eksAddon?: FeatureStatus;
  k3sTrafficReduction?: FeatureStatus;
  redisTrafficReduction?: FeatureStatus;
}

export interface Find {
  command?: string[];
  args?: string[];
  glob?: string;
}

export interface GetAIAssistantUsageStatsBody {
  instanceId?: string[];
}

export interface GetAIAssistantUsageStatsResponse {
  /** @format int64 */
  totalConversations?: number;
  /** @format int64 */
  resolvedConversations?: number;
}

export interface GetAIConversationResponse {
  conversation?: AIConversation;
}

export interface GetAIConversationStreamResponse {
  conversation?: AIConversation;
}

export interface GetAPIKeyResponse {
  apiKey?: APIKey;
}

export interface GetAgentVersionResponse {
  version?: string;
}

export interface GetAnnouncementResponse {
  banner?: Banner;
}

export interface GetArgoCDAgentSizeSpecResponse {
  small?: GetArgoCDAgentSizeSpecResponseSpec;
  medium?: GetArgoCDAgentSizeSpecResponseSpec;
  large?: GetArgoCDAgentSizeSpecResponseSpec;
}

export interface GetArgoCDAgentSizeSpecResponseSpec {
  controllerCpu?: string;
  controllerMemory?: string;
  /** @format int32 */
  repoServerReplicas?: number;
  repoServerCpu?: string;
  repoServerMemory?: string;
}

export interface GetAuditLogsResponse {
  items?: AuditLog[];
  /** @format int64 */
  totalCount?: number;
}

export interface GetAvailableAddonsResponse {
  addons?: SubscriptionAddon[];
}

export interface GetClusterAPIServerCADataResponse {
  data?: string;
}

export interface GetCustomRoleResponse {
  customRole?: CustomRole;
}

export interface GetCustomerDetailsResponse {
  billingDetails?: BillingDetails;
}

export interface GetDeviceCodeResponse {
  deviceCode?: string;
  userCode?: string;
  verificationUri?: string;
  verificationUriComplete?: string;
  /** @format int32 */
  expiresInSeconds?: number;
  /** @format int32 */
  intervalSeconds?: number;
}

export interface GetDeviceTokenRequest {
  deviceCode?: string;
}

export interface GetDeviceTokenResponse {
  accessToken?: string;
}

export interface GetExtensionSettingsResponse {
  organizationId?: string;
  instanceId?: string;
  akuityIntelligence?: AkuityIntelligence;
}

export interface GetFeatureStatusesResponse {
  featureStatuses?: FeatureStatuses;
}

export interface GetInstanceAddonRepoResponse {
  addonRepo?: AddonRepo;
}

export interface GetInstanceAddonResponse {
  addon?: Addon;
}

export interface GetInstanceAgentCommandResponse {
  command?: string;
}

export interface GetInstanceAppsetSecretResponse {
  secret?: Record<string, string>;
}

export interface GetInstanceCSSResponse {
  css?: string;
}

export interface GetInstanceClusterCommandResponse {
  command?: string;
  /** variables used for command */
  variables?: Record<string, string>;
}

export interface GetInstanceClusterInfoResponse {
  hasApplications?: boolean;
}

export interface GetInstanceClusterResponse {
  cluster?: Cluster;
}

export interface GetInstanceConfigManagementPluginsResponse {
  plugins?: ConfigManagementPlugin[];
}

export interface GetInstanceImageUpdaterSettingsResponse {
  secret?: Record<string, string>;
  config?: Record<string, string>;
  sshConfig?: Record<string, string>;
  version?: string;
}

export interface GetInstanceNotificationCatalogResponse {
  catalog?: Record<string, string>;
}

export interface GetInstanceNotificationSettingsResponse {
  secret?: Record<string, string>;
  config?: Record<string, string>;
}

export interface GetInstanceResourceCustomizationsResponse {
  resourceCustomizations?: ResourceCustomizationConfig[];
}

export interface GetInstanceResponse {
  instance?: Instance;
}

export interface GetKargoAgentSizeSpecResponse {
  small?: GetKargoAgentSizeSpecResponseSpec;
  medium?: GetKargoAgentSizeSpecResponseSpec;
  large?: GetKargoAgentSizeSpecResponseSpec;
}

export interface GetKargoAgentSizeSpecResponseSpec {
  controllerCpu?: string;
  controllerMemory?: string;
}

export interface GetKargoInstanceAgentResponse {
  agent?: KargoAgent;
}

export interface GetKargoInstanceResponse {
  instance?: KargoInstance;
}

export interface GetKubeVisionUsageResponse {
  usage?: KubeVisionUsage[];
}

export interface GetKubernetesAssistantSuggestionBody {
  instanceId?: string;
  clusterId?: string;
  applicationName?: string;
  state?: string;
  messages?: AssistantMessage[];
  resource?: ResourceID;
  question?: string;
}

export interface GetKubernetesAssistantSuggestionResponse {
  state?: string;
  messages?: AssistantMessage[];
  suggestedQuestions?: string[];
}

export interface GetKubernetesClusterDetailResponse {
  id?: string;
  name?: string;
  /** @format double */
  cpuAllocatable?: number;
  /** @format double */
  cpuUsage?: number;
  /** @format double */
  cpuRequest?: number;
  /** @format double */
  memoryAllocatable?: number;
  /** @format double */
  memoryUsage?: number;
  /** @format double */
  memoryRequest?: number;
  /** @format int64 */
  podAllocatable?: number;
  /** @format int64 */
  podRunning?: number;
  /** @format int64 */
  podTotal?: number;
  /** @format int64 */
  nodeCount?: number;
}

export interface GetKubernetesContainerResponse {
  container?: Container;
}

export interface GetKubernetesEventsResponse {
  events?: KubernetesEventsData[];
}

export interface GetKubernetesImageDetailResponse {
  image?: Image;
}

export interface GetKubernetesLogsResponse {
  content?: string;
  last?: boolean;
  timestamp?: string;
  podName?: string;
  containerName?: string;
}

export interface GetKubernetesManifestResponse {
  object?: object;
  resource?: ClusterResource;
}

export interface GetKubernetesNamespaceDetailResponse {
  namespaceDetail?: NamespaceDetail;
}

export interface GetKubernetesNodeResponse {
  node?: KubernetesNode;
}

export interface GetKubernetesPodResponse {
  pod?: KubernetesPodDetail;
}

export interface GetKubernetesResourceDetailResponse {
  resource?: ClusterResource;
  childTypes?: ResourceType[];
  /** A repeated list of child pod and containers if the resource is a workload */
  podInfo?: PodInfo[];
  ownerInfo?: ResourceReferenceInfo[];
}

export interface GetKubernetesSummaryResponse {
  /** @format int64 */
  instanceCount?: number;
  /** @format int64 */
  clusterCount?: number;
  /** @format int64 */
  apiResourceCount?: number;
  /** @format int64 */
  objectCount?: number;
  /** @format int64 */
  nodeCount?: number;
  /** @format int64 */
  podCount?: number;
  /** @format int64 */
  containerCount?: number;
  /** @format int64 */
  imageCount?: number;
  /** @format int64 */
  deprecatedApiCount?: number;
  /** @format int64 */
  cveCount?: number;
  /** @format int64 */
  stuckInDeletionCount?: number;
  /** @format int64 */
  incidentCount?: number;
}

export interface GetNotificationConfigResponse {
  notificationConfig?: NotificationConfig;
}

export interface GetNotificationDeliveryHistoryDetailResponse {
  detail?: NotificationDeliveryDetail;
}

export interface GetNotificationSettingsResponse {
  settings?: NotificationSettings;
}

export interface GetOIDCMapResponse {
  entries?: Record<string, string>;
}

export interface GetOIDCProviderDetailsResponse {
  issuer?: string;
  authorizationEndpoint?: string;
  tokenEndpoint?: string;
  userinfoEndpoint?: string;
  jwksUri?: string;
}

export interface GetOrganizationPermissionsResponse {
  permissions?: Permission[];
  rawPermissions?: string;
}

export interface GetOrganizationResponse {
  organization?: Organization;
}

export interface GetPromotionEventsBody {
  filter?: PromotionFilter;
  /** @format int64 */
  limit?: string;
  /** @format int64 */
  offset?: string;
  field?: PromotionField;
  fieldLike?: string;
}

export interface GetPromotionEventsResponse {
  promotionEvents?: PromotionEvent[];
  /** @format int64 */
  count?: string;
  fieldResult?: string[];
}

export interface GetPromotionStatsBody {
  filter?: PromotionFilter;
  interval?: GroupByInterval;
  /** can either set group_by_field or group_by_label_field(has more preference) */
  groupByField?: PromotionGroupField;
}

export interface GetPromotionStatsResponse {
  promotionStats?: PromotionStat[];
}

export interface GetSSOConfigurationResponse {
  autoAddMember?: boolean;
  azureAd?: AzureADSSOOptions;
  okta?: OktaSSOOptions;
  googleWorkspace?: GoogleWorkspaceSSOOptions;
  oidc?: OIDCSSOOptions;
  saml?: SAMLSSOOptions;
}

export interface GetSettingsResponse {
  env?: string;
  domainSuffix?: string;
  sentryDsn?: string;
  googleTagId?: string;
  nameConfig?: NameConfig;
  selfHosted?: boolean;
  billingEnabled?: boolean;
  stripeCustomerPortalUrl?: string;
  /** list of features that can be enabled/disabled at a global level */
  capabilities?: string[];
  /** @format int64 */
  maxInvitationEmailPerBatchCount?: string;
  instanceSubDomainsEnabled?: boolean;
}

/** for lead time and recovery time graphs */
export interface GetStageSpecificStatsBody {
  filter?: PromotionFilter;
}

export interface GetStageSpecificStatsResponse {
  leadTimeData?: LeadTimeData[];
  recoveryTimeData?: RecoveryTimeData[];
}

export interface GetStatusResponse {
  status?: string;
}

export interface GetSyncOperationsEventsBody {
  filter?: SyncOperationFilter;
  /** @format int64 */
  limit?: string;
  /** @format int64 */
  offset?: string;
  field?: SyncOperationField;
  fieldLike?: string;
}

export interface GetSyncOperationsEventsForApplicationRequest {
  filter?: SyncOperationFilter;
  /** @format int64 */
  limit?: string;
  /** @format int64 */
  offset?: string;
  field?: SyncOperationField;
  fieldLike?: string;
}

export interface GetSyncOperationsEventsForApplicationResponse {
  syncOperationEvents?: SyncOperationEvent[];
  /** @format int64 */
  count?: string;
  fieldResult?: string[];
}

export interface GetSyncOperationsEventsResponse {
  syncOperationEvents?: SyncOperationEvent[];
  /** @format int64 */
  count?: string;
  fieldResult?: string[];
}

export interface GetSyncOperationsStatsBody {
  filter?: SyncOperationFilter;
  interval?: GroupByInterval;
  /** can either set group_by_field or group_by_label_field(has more preference) */
  groupByField?: SyncOperationGroupField;
  groupByLabelField?: string;
}

export interface GetSyncOperationsStatsForApplicationRequest {
  filter?: SyncOperationFilter;
  interval?: GroupByInterval;
  groupByField?: SyncOperationGroupField;
}

export interface GetSyncOperationsStatsForApplicationResponse {
  syncOperationStats?: SyncOperationStat[];
}

export interface GetSyncOperationsStatsResponse {
  syncOperationStats?: SyncOperationStat[];
}

export interface GetTeamMemberResponse {
  teamMember?: TeamMember;
}

export interface GetTeamOIDCMapResponse {
  entries?: Record<string, string>;
}

export interface GetTeamResponse {
  userTeam?: UserTeam;
}

export interface GetUserResponse {
  user?: User;
}

export interface GetUserRoleInOrganizationResponse {
  role?: string;
}

export interface GetVersionResponse {
  version?: string;
}

export interface GetWorkspaceAPIKeyResponse {
  apiKey?: APIKey;
}

export interface GetWorkspaceCustomRoleResponse {
  customRole?: CustomRole;
  workspaceId?: string;
}

export interface GetWorkspaceMemberResponse {
  workspaceMember?: WorkspaceMember;
}

export interface GetWorkspaceResponse {
  workspace?: Workspace;
}

export interface GoogleWorkspaceSSOOptions {
  clientId?: string;
  clientSecret?: string;
  googleWorkspaceDomain?: string;
  domainAliases?: string[];
}

export interface GroupVersionKind {
  group?: string;
  version?: string;
  kind?: string;
}

export interface HelmChartInstallConfig {
  dependencies?: ChartDependency[];
  description?: string;
  name?: string;
  version?: string;
}

export interface HelmOptions {
  releaseNameTemplate?: string;
  passCredentials?: boolean;
  skipCrds?: boolean;
}

export interface HelmSource {
  values?: Record<string, string>;
  dependencies?: ChartDependency[];
}

export interface HelmValues {
  yamlPaths?: string[];
}

export interface HostAliases {
  ip?: string;
  hostnames?: string[];
}

/**
 * Message that represents an arbitrary HTTP body. It should only be used for
 * payload formats that can't be represented as JSON, such as raw binary or
 * an HTML page.
 *
 *
 * This message can be used both in streaming and non-streaming API methods in
 * the request as well as the response.
 *
 * It can be used as a top-level request field, which is convenient if one
 * wants to extract parameters from either the URL or HTTP template into the
 * request fields and also want access to the raw HTTP body.
 *
 * Example:
 *
 *     message GetResourceRequest {
 *       // A unique request id.
 *       string request_id = 1;
 *
 *       // The raw HTTP body is bound to this field.
 *       google.api.HttpBody http_body = 2;
 *
 *     }
 *
 *     service ResourceService {
 *       rpc GetResource(GetResourceRequest)
 *         returns (google.api.HttpBody);
 *       rpc UpdateResource(google.api.HttpBody)
 *         returns (google.protobuf.Empty);
 *
 *     }
 *
 * Example with streaming methods:
 *
 *     service CaldavService {
 *       rpc GetCalendar(stream google.api.HttpBody)
 *         returns (stream google.api.HttpBody);
 *       rpc UpdateCalendar(stream google.api.HttpBody)
 *         returns (stream google.api.HttpBody);
 *
 *     }
 *
 * Use of this type only changes how the request and response bodies are
 * handled, all other features will continue to work unchanged.
 */
export interface HttpBody {
  /** The HTTP Content-Type header value specifying the content type of the body. */
  contentType?: string;
  /**
   * The HTTP request/response body as raw binary.
   * @format byte
   */
  data?: string;
  /**
   * Application specific response metadata. Must be set in the first response
   * for streaming APIs.
   */
  extensions?: Any[];
}

export interface IDInfo {
  id?: string;
  name?: string;
}

export interface IPAllowListEntry {
  ip?: string;
  description?: string;
}

export interface Image {
  name?: string;
  tag?: string;
  /** @format int64 */
  containerCount?: number;
  digest?: string;
  cveScanResult?: ImageCVEScanResult;
}

export interface ImageCVE {
  vulnerabilityId?: string;
  title?: string;
  description?: string;
  severity?: string;
  primaryUrl?: string;
  status?: string;
  installedVersion?: string;
  fixedVersion?: string;
}

export interface ImageCVEScanResult {
  cves?: ImageCVE[];
  /** @format int64 */
  cveCount?: number;
  /** @format date-time */
  cveLastScanTime?: string;
}

export interface ImageUpdaterDelegate {
  controlPlane?: boolean;
  managedCluster?: ManagedCluster;
}

export interface Incident {
  /** @format date-time */
  resolvedAt?: string;
  summary?: string;
  rootCause?: string;
  resolution?: string;
  runbooks?: string[];
  application?: string;
  namespace?: string;
  instanceId?: string;
  clusterId?: string;
  instanceHostname?: string;
}

export interface IncidentConfig {
  resolved?: boolean;
}

export interface IncidentWebhookConfig {
  name?: string;
  descriptionPath?: string;
  clusterPath?: string;
  k8sNamespacePath?: string;
  argocdApplicationNamePath?: string;
}

export interface IncidentsConfig {
  triggers?: TargetSelector[];
  webhooks?: IncidentWebhookConfig[];
}

export interface InfluxDbRolloutsSecret {
  influxdbAddress?: string;
  authToken?: string;
  org?: string;
}

export interface Instance {
  id?: string;
  name?: string;
  hostname?: string;
  /** @format int64 */
  clusterCount?: number;
  secrets?: Record<string, string>;
  /** @format int64 */
  generation?: number;
  /** @format int64 */
  recentProcessedEventId?: number;
  healthStatus?: HealthV1Status;
  reconciliationStatus?: ReconciliationV1Status;
  /** @format date-time */
  deleteTime?: string;
  ownerOrganizationName?: string;
  description?: string;
  version?: string;
  spec?: InstanceSpec;
  config?: ArgoCDConfigMap;
  rbacConfig?: ArgoCDRBACConfigMap;
  info?: InstanceInfo;
  shard?: string;
  workspaceId?: string;
  /** @format int64 */
  notIntegrationClusterCount?: number;
  unsupportedVersion?: boolean;
}

export interface InstanceAccount {
  name?: string;
  capabilities?: InstanceAccountCapabilities;
  disabled?: boolean;
}

export interface InstanceAccountCapabilities {
  login?: boolean;
  apiKey?: boolean;
}

export interface InstanceInfo {
  applicationsStatus?: ApplicationsStatus;
  certificateStatus?: ArgocdV1CertificateStatus;
}

export interface InstanceSpec {
  ipAllowList?: IPAllowListEntry[];
  subdomain?: string;
  declarativeManagementEnabled?: boolean;
  extensions?: ArgoCDExtensionInstallEntry[];
  clusterCustomizationDefaults?: ClusterCustomization;
  imageUpdaterEnabled?: boolean;
  backendIpAllowListEnabled?: boolean;
  repoServerDelegate?: RepoServerDelegate;
  auditExtensionEnabled?: boolean;
  syncHistoryExtensionEnabled?: boolean;
  crossplaneExtension?: CrossplaneExtension;
  imageUpdaterDelegate?: ImageUpdaterDelegate;
  appSetDelegate?: AppSetDelegate;
  assistantExtensionEnabled?: boolean;
  appsetPolicy?: AppsetPolicy;
  hostAliases?: HostAliases[];
  agentPermissionsRules?: AgentPermissionsRule[];
  fqdn?: string;
  multiClusterK8sDashboardEnabled?: boolean;
  akuityIntelligenceExtension?: AkuityIntelligenceExtension;
  imageUpdaterVersion?: string;
  customDeprecatedApis?: CustomDeprecatedAPI[];
  kubeVisionConfig?: KubeVisionConfig;
  appInAnyNamespaceConfig?: AppInAnyNamespaceConfig;
  basepath?: string;
  appsetProgressiveSyncsEnabled?: boolean;
  secrets?: SecretsManagementConfig;
  appsetPlugins?: AppsetPlugins[];
  applicationSetExtension?: ApplicationSetExtension;
  appReconciliationsRateLimiting?: AppReconciliationsRateLimiting;
}

export interface InstanceVersion {
  version?: string;
  label?: string;
}

export interface InviteMembersBody {
  emails?: string[];
  role?: string;
  info?: InviteMembersInfo;
}

export interface InviteMembersInfo {
  teams?: string[];
  workspaces?: AssignWorkspace[];
}

/** empty */
export type InviteMembersResponse = object;

export interface ItemRateLimiting {
  enabled?: boolean;
  /** @format int64 */
  failureCooldown?: number;
  /** @format int64 */
  baseDelay?: number;
  /** @format int64 */
  maxDelay?: number;
  /** @format float */
  backoffFactor?: number;
}

export interface JoinOrganizationResponse {
  organization?: Organization;
}

export interface K8SNamespaceContext {
  instanceId?: string;
  clusterId?: string;
  name?: string;
}

export interface KargoAgent {
  id?: string;
  name?: string;
  /** Use data.namespace instead */
  namespace?: string;
  description?: string;
  data?: KargoAgentData;
  /** @format date-time */
  deleteTime?: string;
  healthStatus?: HealthV1Status;
  reconciliationStatus?: ReconciliationV1Status;
  agentState?: KargoAgentState;
  /** @format uint64 */
  readonlySettingsChangedGeneration?: string;
  /** @format uint64 */
  observedGeneration?: string;
}

export interface KargoAgentCustomization {
  autoUpgradeDisabled?: boolean;
  kustomization?: object;
}

export interface KargoAgentData {
  size?: KargoAgentSize;
  labels?: Record<string, string>;
  annotations?: Record<string, string>;
  autoUpgradeDisabled?: boolean;
  targetVersion?: string;
  kustomization?: object;
  remoteArgocd?: string;
  akuityManaged?: boolean;
  namespace?: string;
  argocdNamespace?: string;
  selfManagedArgocdUrl?: string;
}

export interface KargoAgentFilter {
  nameLike?: string;
  agentStatus?: HealthV1StatusCode[];
  agentVersion?: string[];
  kargoVersion?: string[];
  /** @format int64 */
  limit?: string;
  /** @format int64 */
  offset?: string;
  excludeAgentVersion?: string;
  outdatedManifest?: boolean;
  namespace?: string[];
  labels?: Record<string, string>;
  remoteArgocdIds?: string[];
  selfManaged?: boolean;
  needReapply?: boolean;
}

export interface KargoAgentState {
  version?: string;
  kargoVersion?: string;
  /** @format date-time */
  observeTime?: string;
  status?: AgentAggregatedHealthResponse;
  agentIds?: string[];
  /** @format uint64 */
  lastUserAppliedGeneration?: string;
  updateStatus?: AgentUpdateStatus;
}

export interface KargoApiCM {
  adminAccountEnabled?: boolean;
  adminAccountTokenTtl?: string;
}

export interface KargoApiSecret {
  adminAccountPasswordHash?: string;
}

/** nothing to store for now */
export type KargoControllerCM = object;

export interface KargoIPAllowListEntry {
  ip?: string;
  description?: string;
}

export interface KargoInstance {
  id?: string;
  name?: string;
  description?: string;
  spec?: KargoInstanceSpec;
  hostname?: string;
  /** @format int64 */
  generation?: number;
  healthStatus?: HealthV1Status;
  reconciliationStatus?: ReconciliationV1Status;
  /** @format date-time */
  deleteTime?: string;
  ownerOrganizationName?: string;
  version?: string;
  controllerCm?: KargoControllerCM;
  webhookCm?: KargoWebhookCM;
  apiCm?: KargoApiCM;
  apiSecret?: KargoApiSecret;
  oidcConfig?: KargoOidcConfig;
  subdomain?: string;
  workspaceId?: string;
  miscellaneousSecrets?: KargoMiscellaneousSecrets;
  fqdn?: string;
  certificateStatus?: KargoV1CertificateStatus;
  unsupportedVersion?: boolean;
}

export interface KargoInstanceSpec {
  backendIpAllowListEnabled?: boolean;
  ipAllowList?: KargoIPAllowListEntry[];
  agentCustomizationDefaults?: KargoAgentCustomization;
  defaultShardAgent?: string;
  globalCredentialsNs?: string[];
  globalServiceAccountNs?: string[];
  akuityIntelligence?: AkuityIntelligence;
}

export interface KargoMiscellaneousSecrets {
  datadogRolloutsSecret?: DataDogRolloutsSecret;
  newrelicRolloutsSecret?: NewRelicRolloutsSecret;
  influxdbRolloutsSecret?: InfluxDbRolloutsSecret;
}

export interface KargoOidcConfig {
  enabled?: boolean;
  dexEnabled?: boolean;
  dexConfig?: string;
  dexConfigSecret?: Record<string, KargoOidcConfigValue>;
  issuerUrl?: string;
  clientId?: string;
  cliClientId?: string;
  adminAccount?: KargoPredefinedAccountData;
  viewerAccount?: KargoPredefinedAccountData;
  additionalScopes?: string[];
  userAccount?: KargoPredefinedAccountData;
}

export interface KargoOidcConfigValue {
  value?: string;
}

export interface KargoPredefinedAccountClaimValue {
  values?: string[];
}

export interface KargoPredefinedAccountData {
  /** Use claims instead; */
  email?: string[];
  /** Use claims instead; */
  sub?: string[];
  /** Use claims instead; */
  groups?: string[];
  claims?: Record<string, KargoPredefinedAccountClaimValue>;
}

export interface KargoProjectContext {
  instanceId?: string;
  name?: string;
}

export interface KargoPromotionAnalysis {
  project?: string;
  stage?: string;
  freight?: string;
  reAnalyze?: boolean;
}

/** nothing to store for now */
export type KargoWebhookCM = object;

export interface KubeVisionConfig {
  cveScanConfig?: CveScanConfig;
  aiConfig?: AIConfig;
}

export interface KubeVisionUsage {
  /** @format date-time */
  timestamp?: string;
  /** @format int64 */
  instanceCount?: number;
  /** @format int64 */
  clusterCount?: number;
  /** @format int64 */
  apiResourceCount?: number;
  /** @format int64 */
  objectCount?: number;
  /** @format int64 */
  nodeCount?: number;
  /** @format int64 */
  podCount?: number;
  /** @format int64 */
  containerCount?: number;
  aiUsage?: AIUsage;
}

export interface KubernetesEventsData {
  uid?: string;
  type?: string;
  reason?: string;
  message?: string;
  /** @format int32 */
  count?: number;
  /** @format date-time */
  firstTimestamp?: string;
  /** @format date-time */
  lastTimestamp?: string;
  /** @format date-time */
  eventTimestamp?: string;
}

export interface KubernetesNode {
  id?: string;
  name?: string;
  /** @format double */
  fillValue?: number;
  groups?: string[];
  hostname?: string;
  cri?: string;
  availabilityZone?: string;
  region?: string;
  platform?: string;
  kubeletVersion?: string;
  clusterName?: string;
  /** @format double */
  usageCpu?: number;
  /** @format double */
  usageMemory?: number;
  /** @format int64 */
  allocatedPods?: string;
  instanceName?: string;
  instanceId?: string;
}

export interface KubernetesPod {
  id?: string;
  name?: string;
  namespace?: string;
  clusterName?: string;
  /** @format double */
  fillValue?: number;
  groups?: string[];
  instanceName?: string;
  instanceId?: string;
}

export interface KubernetesPodDetail {
  id?: string;
  name?: string;
  namespace?: string;
  nodeName?: string;
  clusterName?: string;
  region?: string;
  availabilityZone?: string;
  status?: KubernetesPodStatus;
  /** @format double */
  usageCpu?: number;
  /** @format double */
  usageMemory?: number;
  instanceName?: string;
  instanceId?: string;
}

export interface KustomizeHelmChart {
  name?: string;
  version?: string;
}

export interface KustomizeImage {
  name?: string;
  /** buf:lint:ignore FIELD_LOWER_SNAKE_CASE */
  newTag?: string;
  /** buf:lint:ignore FIELD_LOWER_SNAKE_CASE */
  newName?: string;
  /** buf:lint:ignore FIELD_LOWER_SNAKE_CASE */
  tagSuffix?: string;
  digest?: string;
}

export interface KustomizeOptions {
  namePrefixTemplate?: string;
  nameSuffixTemplate?: string;
}

export interface KustomizeSource {
  images?: KustomizeImage[];
  helmCharts?: KustomizeHelmChart[];
}

/**
 * A label selector requirement is a selector that contains values, a key, and an operator that
 * relates the key and values.
 */
export interface LabelSelectorRequirement {
  /** key is the label key that the selector applies to. */
  key?: string;
  /**
   * operator represents a key's relationship to a set of values.
   * Valid operators are In, NotIn, Exists and DoesNotExist.
   */
  operator?: string;
  /**
   * values is an array of string values. If the operator is In or NotIn,
   * the values array must be non-empty. If the operator is Exists or DoesNotExist,
   * the values array must be empty. This array is replaced during a strategic
   * merge patch.
   */
  values?: string[];
}

export interface LeadTimeData {
  promotionEndTime?: string;
  freightCreationTime?: string;
  /** @format float */
  leadTime?: number;
  stageName?: string;
}

export interface ListAIConversationSuggestionsBody {
  contexts?: AIMessageContext[];
  instanceId?: string;
}

export interface ListAIConversationSuggestionsResponse {
  suggestions?: AIConversationSuggestion[];
}

export interface ListAIConversationsResponse {
  conversations?: AIConversation[];
  /** @format int64 */
  count?: number;
}

export interface ListAddonMarketplaceInstallsBody {
  filter?: AddonMarketplaceInstallFilter;
  /** @format int64 */
  limit?: number;
  /** @format int64 */
  offset?: number;
}

export interface ListAddonMarketplaceInstallsResponse {
  addonInstalls?: V1AddonMarketplaceInstall[];
  /** @format int64 */
  totalCount?: number;
}

export interface ListAgentVersionsResponse {
  agentVersions?: string[];
}

export interface ListArgoCDExtensionsResponse {
  extensions?: ExtensionInfo[];
}

export interface ListArgoCDImageUpadterVersionsResponse {
  versions?: string[];
}

export interface ListArgoCDVersionsResponse {
  argocdVersions?: ComponentVersion[];
}

export interface ListAuditLogsArchivesResponse {
  archives?: AuditLogArchive[];
  /** @format int64 */
  totalArchivesCount?: number;
}

export interface ListAuditRecordForApplicationRequest {
  filters?: AuditFilters;
}

export interface ListAuditRecordForApplicationResponse {
  items?: AuditLog[];
  /** @format int64 */
  totalCount?: number;
}

export interface ListAuditRecordForKargoProjectsRequest {
  filters?: AuditFilters;
  projectName?: string;
}

export interface ListAuditRecordForKargoProjectsResponse {
  items?: AuditLog[];
  /** @format int64 */
  totalCount?: number;
}

export interface ListAuthenticatedUserOrganizationsResponse {
  organizations?: Organization[];
}

export interface ListAvailablePlansResponse {
  plans?: Plan[];
}

export interface ListCustomRolesResponse {
  customRoles?: CustomRole[];
  /** @format int64 */
  totalCount?: string;
}

export interface ListInstanceAccountsResponse {
  accounts?: InstanceAccount[];
}

export interface ListInstanceAddonErrorsResponse {
  errors?: Record<string, AddonErrorList>;
  /** @format int32 */
  totalCount?: number;
}

export interface ListInstanceAddonReposResponse {
  addonRepos?: AddonRepo[];
}

export interface ListInstanceAddonsResponse {
  addons?: Addon[];
  /** @format int32 */
  totalCount?: number;
}

export interface ListInstanceClustersResponse {
  clusters?: Cluster[];
  /** @format int64 */
  totalCount?: string;
}

export interface ListInstanceManagedSecretsResponse {
  managedSecrets?: ManagedSecret[];
}

export interface ListInstanceReposResponse {
  repos?: Repository[];
}

export interface ListInstanceVersionsResponse {
  versions?: InstanceVersion[];
}

export interface ListInstancesResponse {
  instances?: Instance[];
}

export interface ListKargoInstanceAgentsResponse {
  agents?: KargoAgent[];
  /** @format int64 */
  totalCount?: string;
}

export interface ListKargoInstancesResponse {
  instances?: KargoInstance[];
}

export interface ListKargoVersionsResponse {
  kargoVersions?: ComponentVersion[];
}

export interface ListKubernetesAuditLogsResponse {
  items?: AuditLog[];
  /** @format int64 */
  count?: number;
}

export interface ListKubernetesContainersResponse {
  containers?: Container[];
  /** @format int64 */
  count?: number;
}

export interface ListKubernetesDeprecatedAPIsResponse {
  apis?: DeprecatedInfo[];
  /** @format int64 */
  totalCount?: number;
}

export interface ListKubernetesEnabledClustersResponse {
  clusters?: EnabledCluster[];
}

export interface ListKubernetesImagesResponse {
  images?: Image[];
  /** @format int64 */
  count?: number;
}

export interface ListKubernetesNamespacesDetailsResponse {
  namespacesDetails?: NamespaceDetail[];
  fillValueUnit?: FillValueUnit;
  /** @format double */
  minFillValue?: number;
  /** @format double */
  maxFillValue?: number;
}

export interface ListKubernetesNamespacesResponse {
  namespaces?: string[];
}

export interface ListKubernetesNodesResponse {
  nodes?: KubernetesNode[];
  fillValueUnit?: FillValueUnit;
  /** @format double */
  minFillValue?: number;
  /** @format double */
  maxFillValue?: number;
}

export interface ListKubernetesPodsResponse {
  pods?: KubernetesPod[];
  fillValueUnit?: FillValueUnit;
  /** @format double */
  minFillValue?: number;
  /** @format double */
  maxFillValue?: number;
}

export interface ListKubernetesResourceTypesResponse {
  instanceId?: string;
  resourceTypes?: ResourceType[];
}

export interface ListKubernetesResourcesResponse {
  resources?: ClusterResource[];
  /** @format int64 */
  count?: number;
}

export interface ListKubernetesTimelineEventsResponse {
  events?: TimelineEvent[];
}

export interface ListKubernetesTimelineResourcesResponse {
  resources?: TimelineResource[];
  /** @format int64 */
  count?: number;
}

export interface ListNotificationConfigsResponse {
  notificationConfigs?: NotificationConfig[];
}

export interface ListNotificationDeliveryHistoryResponse {
  history?: NotificationDelivery[];
  /** @format uint64 */
  totalHistoryCount?: string;
}

export interface ListNotificationsResponse {
  notifications?: Notification[];
  /** @format int64 */
  count?: number;
  /** @format int64 */
  unreadCount?: number;
}

export interface ListOrganizationAPIKeysResponse {
  apiKeys?: APIKey[];
}

export interface ListOrganizationDomainsResponse {
  domains?: DomainVerification[];
}

export interface ListOrganizationInviteesResponse {
  invitees?: OrganizationInvitee[];
}

export interface ListOrganizationMembersResponse {
  members?: OrganizationMember[];
}

export interface ListTeamMembersResponse {
  teamMembers?: TeamMember[];
  /** @format int64 */
  count?: string;
}

export interface ListTeamsResponse {
  userTeams?: UserTeam[];
  /** @format int64 */
  count?: string;
}

export interface ListUsersMFAStatusResponse {
  userMfaStatus?: UserMFAStatus[];
}

export interface ListValidWebhookEventsResponse {
  events?: string[];
}

export interface ListWorkspaceAPIKeysResponse {
  apiKeys?: APIKey[];
  workspaceId?: string;
}

export interface ListWorkspaceCustomRolesResponse {
  customRoles?: CustomRole[];
  workspaceId?: string;
  /** @format int64 */
  totalCount?: string;
}

export interface ListWorkspaceMembersResponse {
  workspaceMembers?: WorkspaceMember[];
  /** @format int64 */
  teamMemberCount?: number;
  /** @format int64 */
  userMemberCount?: number;
}

export interface ListWorkspacesResponse {
  workspaces?: Workspace[];
}

export interface MFASettings {
  enabled?: boolean;
}

export interface ManagedCluster {
  clusterName?: string;
}

export interface ManagedClusterConfig {
  secretName?: string;
  secretKey?: string;
}

export interface ManagedSecret {
  name?: string;
  labels?: Record<string, string>;
  type?: ManagedSecretType;
}

export interface ManifestSource {
  kustomizeSource?: KustomizeSource;
  helmSource?: HelmSource;
  path?: string;
}

export interface NameConfig {
  /** @format int64 */
  minOrganizationNameLength?: string;
  /** @format int64 */
  minInstanceNameLength?: string;
  /** @format int64 */
  minClusterNameLength?: string;
  /** @format int64 */
  minSubdomainNameLength?: string;
}

export interface NamespaceDetail {
  name?: string;
  clusterId?: string;
  instanceId?: string;
  /** @format double */
  fillValue?: number;
  groups?: string[];
  id?: string;
  /** @format double */
  usageCpu?: number;
  /** @format double */
  usageMemory?: number;
  /** @format int32 */
  podCount?: number;
  /** @format int32 */
  runningPodCount?: number;
  /** @format double */
  requestCpu?: number;
  /** @format double */
  requestMemory?: number;
}

export interface NewRelicRolloutsSecret {
  personalApiKey?: string;
  accountId?: string;
  region?: string;
  baseUrlRest?: string;
  baseUrlNerdgraph?: string;
}

export interface Notification {
  id?: string;
  /** @format date-time */
  createTime?: string;
  title?: string;
  category?: NotificationCategory;
  template?: string;
  isRead?: boolean;
  metadata?: object;
}

export interface NotificationConfig {
  id?: string;
  webhook?: WebhookConfig;
  deliveryMethod?: DeliveryMethod;
  name?: string;
  lastDelivery?: NotificationDeliverySummary;
}

export interface NotificationDelivery {
  id?: string;
  eventId?: string;
  eventType?: string;
  deliveryStatus?: NotificationDeliveryStatus;
  /** @format date-time */
  initialDeliveryTime?: string;
  /** @format uint64 */
  retryCount?: string;
  webhook?: WebhookNotificationDeliveryMetadata;
  redelivered?: boolean;
}

export interface NotificationDeliveryDetail {
  id?: string;
  eventId?: string;
  eventType?: string;
  deliveryStatus?: NotificationDeliveryStatus;
  /** @format date-time */
  initialDeliveryTime?: string;
  /** @format uint64 */
  retryCount?: string;
  webhook?: WebhookNotificationDeliveryDetail;
  redelivered?: boolean;
}

export interface NotificationDeliverySummary {
  id?: string;
  eventId?: string;
  eventType?: string;
  deliveryStatus?: NotificationDeliveryStatus;
  /** @format date-time */
  initialDeliveryTime?: string;
  /** @format uint64 */
  retryCount?: string;
  webhook?: WebhookNotificationDeliveryMetadataSummary;
  redelivered?: boolean;
}

export interface NotificationSettings {
  web?: WebNotificationConfig;
  email?: EmailNotificationConfig;
}

export interface OIDCSSOBackChannel {
  clientSecret?: string;
  issuer?: string;
  authorizationEndpoint?: string;
  tokenEndpoint?: string;
  jwksUri?: string;
}

export interface OIDCSSOFrontChannel {
  issuer?: string;
  authorizationEndpoint?: string;
  jwksUri?: string;
}

export interface OIDCSSOOptions {
  discoveryUrl?: string;
  clientId?: string;
  domain?: string;
  domainAliases?: string[];
  back?: OIDCSSOBackChannel;
  front?: OIDCSSOFrontChannel;
  groupsScopeEnabled?: boolean;
}

export interface ObjectFilter {
  objectName?: string[];
  objectKind?: string[];
  objectGroup?: string[];
  objectParentName?: string[];
  objectParentParentName?: string[];
  objectParentApplicationName?: string[];
  enabled?: boolean;
  objectParentKargoProjectName?: string[];
}

export interface ObjectSelector {
  /**
   * matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
   * map is equivalent to an element of matchExpressions, whose key field is "key", the
   * operator is "In", and the values array contains only "value". The requirements are ANDed.
   * +optional
   */
  matchLabels?: Record<string, string>;
  /** matchExpressions is a list of label selector requirements. The requirements are ANDed. */
  matchExpressions?: LabelSelectorRequirement[];
}

export interface OktaSSOOptions {
  clientId?: string;
  clientSecret?: string;
  oktaDomain?: string;
  domainAlias?: string;
  domainAliases?: string[];
}

export interface Organization {
  id?: string;
  name?: string;
  /**
   * Use quota.max_instances instead
   * @format int64
   */
  maxInstances?: string;
  /**
   * Use quota.max_clusters instead
   * @format int64
   */
  maxClusters?: string;
  /** @format date-time */
  createTime?: string;
  permissions?: Permissions;
  /**
   * Use quota.max_applications instead
   * @format int64
   */
  maxApplications?: string;
  status?: OrganizationStatus;
  /**
   * OrganizationQuota is a quota for the given organization.
   * NOTE: It is encouraged to define quota fields in `double` or `int64` to
   * avoid type cast failure.
   */
  quota?: OrganizationQuota;
  plan?: string;
  /**
   * Optional: Currently only used in GetOrganization()
   * OrganizationUsage is the usage of resources for the given organization.
   */
  usage?: OrganizationUsage;
  mfaSettings?: MFASettings;
  aiSettings?: AISettings;
}

export interface OrganizationInvitee {
  id?: string;
  email?: string;
  role?: string;
  info?: InviteMembersInfo;
}

export interface OrganizationMember {
  id?: string;
  email?: string;
  role?: string;
}

/**
 * OrganizationQuota is a quota for the given organization.
 * NOTE: It is encouraged to define quota fields in `double` or `int64` to
 * avoid type cast failure.
 */
export interface OrganizationQuota {
  /** @format int64 */
  maxInstances?: string;
  /** @format int64 */
  maxClusters?: string;
  /** @format int64 */
  maxApplications?: string;
  /** @format int64 */
  maxKargoInstances?: string;
  /**
   * Deprecated: we use stages now
   * @format int64
   */
  maxKargoProjects?: string;
  /** @format int64 */
  maxKargoAgents?: string;
  /** @format int64 */
  auditRecordMonths?: string;
  /** @format int64 */
  auditRecordArchiveMonths?: string;
  /** @format int64 */
  maxOrgMembers?: string;
  /** @format int64 */
  maxWorkspaces?: string;
  /** @format int64 */
  maxKargoStages?: string;
  /** @format double */
  maxAiCostPerMonth?: number;
}

export interface OrganizationStatus {
  state?: string;
  trial?: boolean;
  /** @format uint64 */
  expiry?: string;
  billingUpdating?: boolean;
}

export interface OrganizationSummary {
  id?: string;
  name?: string;
  role?: string;
  /** @format date-time */
  expirationTime?: string;
  isFreeTrial?: boolean;
  isSsoInferred?: boolean;
  plan?: string;
}

/** OrganizationUsage is the usage of resources for the given organization. */
export interface OrganizationUsage {
  /** @format int64 */
  currentInstances?: string;
  /** @format int64 */
  currentClusters?: string;
  /** @format int64 */
  currentApplications?: string;
  /** @format int64 */
  currentKargoInstances?: string;
  /**
   * Deprecated: we use stages now
   * @format int64
   */
  currentKargoProjects?: string;
  /** @format int64 */
  currentKargoAgents?: string;
  /** @format int64 */
  currentOrgMembers?: string;
  /** @format int64 */
  currentWorkspaces?: string;
  /** @format int64 */
  currentKargoStages?: string;
  /** @format int64 */
  currentAiInputTokensPerMonth?: string;
  /** @format int64 */
  currentAiInputCachedTokensPerMonth?: string;
  /** @format int64 */
  currentAiOutputTokensPerMonth?: string;
  /** @format double */
  currentAiCostPerMonth?: number;
}

export interface OverlayPolicy {
  name?: string;
  policy?: string;
}

export interface ParameterAnnouncement {
  /** name is the name identifying a parameter. */
  name?: string;
  /** title is a human-readable text of the parameter name. */
  title?: string;
  /** tooltip is a human-readable description of the parameter. */
  tooltip?: string;
  /** required defines if this given parameter is mandatory. */
  required?: boolean;
  /**
   * itemType determines the primitive data type represented by the parameter.
   * Parameters are always encoded as strings, but this field lets them be
   * interpreted as other primitive types. Use camel case to match the
   * ConfigManagementPlugin definition of argocd buf:lint:ignore
   * buf:lint:ignore FIELD_LOWER_SNAKE_CASE
   */
  itemType?: string;
  /**
   * collectionType is the type of value this parameter holds - either a single
   * value (a string) or a collection (array or map). If collectionType is set,
   * only the field with that type will be used. If collectionType is not set,
   * `string` is the default. If collectionType is set to an invalid value, a
   * validation error is thrown.
   * buf:lint:ignore FIELD_LOWER_SNAKE_CASE
   */
  collectionType?: string;
  /** string is the default value of the parameter if the parameter is a string. */
  string?: string;
  /** array is the default value of the parameter if the parameter is an array. */
  array?: string[];
  /** map is the default value of the parameter if the parameter is a map. */
  map?: Record<string, string>;
}

export interface PatchCustomization {
  clusterSelector?: ClusterSelector;
  patch?: object;
  description?: string;
}

export interface PatchInstanceAddonResponse {
  addon?: Addon;
}

export interface PatchInstanceAppsetSecretBody {
  secret?: Record<string, PatchInstanceAppsetSecretRequestValueField>;
}

export interface PatchInstanceAppsetSecretRequestValueField {
  value?: string;
}

/** void */
export type PatchInstanceAppsetSecretResponse = object;

export interface PatchInstanceImageUpdaterSecretBody {
  secret?: Record<string, PatchInstanceImageUpdaterSecretRequestValueField>;
}

export interface PatchInstanceImageUpdaterSecretRequestValueField {
  value?: string;
}

/** void */
export type PatchInstanceImageUpdaterSecretResponse = object;

export interface PatchInstanceNotificationSecretBody {
  secret?: Record<string, PatchInstanceNotificationSecretRequestValueField>;
}

export interface PatchInstanceNotificationSecretRequestValueField {
  value?: string;
}

/** void */
export type PatchInstanceNotificationSecretResponse = object;

export interface PatchInstanceResponse {
  instance?: Instance;
}

export interface PatchInstanceSecretBody {
  secret?: Record<string, PatchInstanceSecretRequestValueField>;
}

export interface PatchInstanceSecretRequestValueField {
  value?: string;
}

/** void */
export type PatchInstanceSecretResponse = object;

export interface PatchKargoInstanceResponse {
  instance?: KargoInstance;
}

export interface Permission {
  role?: string;
  object?: string;
  action?: string;
  resource?: string;
}

export interface Permissions {
  actions?: string[];
  roles?: string[];
  customRoles?: string[];
}

export type PingNotificationConfigBody = object;

/** explicitly empty */
export type PingNotificationConfigResponse = object;

export interface Plan {
  name?: string;
  deprecated?: boolean;
}

export interface PluginSpec {
  version?: string;
  init?: Command;
  generate?: Command;
  discover?: Discover;
  parameters?: V1Parameters;
  /**
   * Use camel case to match the ConfigManagementPlugin definition of argocd
   * buf:lint:ignore FIELD_LOWER_SNAKE_CASE
   */
  preserveFileMode?: boolean;
}

export interface PodInfo {
  pod?: IDInfo;
  containers?: IDInfo[];
}

export interface PromotionAnalysis {
  /** @format date-time */
  finishTime?: string;
  project?: string;
  instanceId?: string;
  freight?: string;
  stage?: string;
  summary?: string;
  riskLevel?: string;
  decision?: string;
  commitDiffUrl?: string;
  currentFreight?: string;
}

export interface PromotionEvent {
  id?: string;
  instanceId?: string;
  promotionName?: string;
  startTime?: string;
  endTime?: string;
  resultPhase?: string;
  resultMessage?: string;
  details?: PromotionEventDetails;
}

export interface PromotionEventDetails {
  project?: string;
  stage?: string;
  initiatedBy?: KargoV1OperationInitiator;
  promotionStatus?: string;
  freightName?: string;
  freightAlias?: string;
  freightCreationTime?: string;
  verificationStartTime?: string;
  verificationEndTime?: string;
  verificationStatus?: string;
  miscellaneous?: object;
}

export interface PromotionFilter {
  startTime?: string;
  endTime?: string;
  /** stage_name is a list of stages to be filtered. Stage name allows `*` to match multiple stages (e.g. 'test-*') */
  stageName?: string[];
  projects?: string[];
  promotionName?: string[];
  instanceId?: string[];
  initiatedBy?: string[];
  instanceNames?: string[];
}

export interface PromotionStat {
  intervalStart?: string;
  countMap?: Record<string, number>;
  averageMap?: Record<string, number>;
}

export interface ReadNotificationsRequest {
  notificationIds?: string[];
}

/** empty */
export type ReadNotificationsResponse = object;

export interface RecoveryTimeData {
  phase?: string;
  phaseChangeTime?: string;
  stageName?: string;
}

export type RedeliverNotificationBody = object;

/** explicitly empty */
export type RedeliverNotificationResponse = object;

/** explicitly empty */
export type RefreshAccessTokenRequest = object;

/** explicitly empty */
export type RefreshAccessTokenResponse = object;

export type RefreshInstanceAddonBody = object;

export type RefreshInstanceAddonRepoBody = object;

export interface RefreshInstanceAddonRepoResponse {
  addonRepo?: AddonRepo;
}

export interface RefreshInstanceAddonResponse {
  addon?: Addon;
}

export interface RegenerateAPIKeySecretBody {
  expiry?: string;
}

export interface RegenerateAPIKeySecretResponse {
  apiKey?: APIKey;
}

export type RegenerateInstanceAccountPasswordBody = object;

export interface RegenerateInstanceAccountPasswordResponse {
  password?: string;
}

export interface RegenerateWorkspaceAPIKeySecretBody {
  expiry?: string;
}

export interface RegenerateWorkspaceAPIKeySecretResponse {
  apiKey?: APIKey;
}

/** empty */
export type RejectOrganizationResponse = object;

/** empty */
export type RemoveOrganizationMemberResponse = object;

/** explicitly empty */
export type RemoveTeamMemberResponse = object;

/** explicitly empty */
export type RemoveWorkspaceMemberResponse = object;

export interface RepoServerAutoScalingConfig {
  resourceMinimum?: Resources;
  resourceMaximum?: Resources;
  /** @format int32 */
  replicaMaximum?: number;
  /** @format int32 */
  replicaMinimum?: number;
}

export interface RepoServerDelegate {
  controlPlane?: boolean;
  managedCluster?: ManagedCluster;
}

export interface RepoServerResources {
  requests?: Resources;
  limits?: Resources;
  /** @format int64 */
  replicas?: number;
}

export interface RepoSpec {
  repoUrl?: string;
  revision?: string;
}

export interface RepoStatus {
  lastSyncTime?: string;
  lastSyncCommit?: string;
  /** @format int64 */
  addonCount?: number;
  /** @format int64 */
  processedGeneration?: number;
  reconciliationStatus?: ReconciliationV1Status;
}

export interface Repository {
  repo?: string;
  type?: string;
  project?: string;
}

export type RequestMFAResetBody = object;

/** explicitly empty */
export type RequestMFAResetResponse = object;

/** Explicitly empty */
export type ResetPasswordRequest = object;

/** Explicitly empty */
export type ResetPasswordResponse = object;

export interface ResolveKubernetesAssistantConversationBody {
  instanceId?: string;
  clusterId?: string;
  resolved?: boolean;
  state?: string;
}

export interface ResolveKubernetesAssistantConversationResponse {
  state?: string;
}

export interface ResourceCustomizationConfig {
  group?: string;
  kind?: string;
  health?: string;
  actions?: string;
  ignoreDifferences?: string;
  knownTypeFields?: string;
  useOpenLibs?: boolean;
}

export interface ResourceID {
  apiVersion?: string;
  kind?: string;
  namespace?: string;
  name?: string;
}

export interface ResourceReferenceInfo {
  id?: string;
  name?: string;
  namespace?: string;
  groupVersionKind?: GroupVersionKind;
}

export interface ResourceType {
  groupVersionKind?: GroupVersionKind;
  columns?: ColumnInfo[];
  category?: ResourceCategory;
  deprecatedInfo?: DeprecatedInfo;
  clusterScoped?: boolean;
}

export interface Resources {
  mem?: string;
  cpu?: string;
}

export interface RotateInstanceAgentCredentialsBody {
  agentNames?: string[];
}

export interface RotateInstanceAgentCredentialsResponse {
  skippedAgents?: string[];
}

export interface RotateInstanceClusterCredentialsBody {
  clusterNames?: string[];
  allClusters?: boolean;
}

export interface RotateInstanceClusterCredentialsResponse {
  skippedClusters?: string[];
}

export interface Runbook {
  name?: string;
  content?: string;
  appliedTo?: TargetSelector;
  stored?: boolean;
}

export interface SAMLSSOConnectionDetails {
  signInEndpoint?: string;
  disableSignOut?: boolean;
  signOutEndpoint?: string;
  base64EncodedSigningCert?: string;
  signRequest?: boolean;
  signatureAlgorithm?: SAMLSignatureAlgorithm;
  digestAlgorithm?: SAMLDigestAlgorithm;
  protocolBinding?: SAMLProtocolBinding;
}

export interface SAMLSSOOptions {
  domain?: string;
  domainAliases?: string[];
  connectionDetails?: SAMLSSOConnectionDetails;
  metadataXml?: string;
}

export interface SecretsManagementConfig {
  sources?: ClusterSecretMapping[];
  destinations?: ClusterSecretMapping[];
}

export interface SecurityAdvisory {
  cveId?: string;
}

export interface Selector {
  key?: string;
  selectorOperator?: SelectorOperator;
  values?: string[];
}

export interface SourceInfo {
  defaultManifest?: ManifestSource;
  clusterOverrides?: Record<string, ManifestSource>;
  envOverrides?: Record<string, ManifestSource>;
}

export interface SourceUpdateResult {
  /** @format date-time */
  startTimestamp?: string;
  /** @format date-time */
  completedTimestamp?: string;
  /** @format int32 */
  attempts?: number;
  cancelled?: boolean;
  error?: string;
  commitSha?: string;
  changes?: object;
  initiator?: string;
}

export interface SpotlightSearchKubernetesResourcesResponse {
  resources?: ClusterResource[];
  /** @format int64 */
  count?: number;
}

export interface StatusOperation {
  clusterAddonStatusOperation?: ClusterAddonStatusOperation;
  clusterSelector?: ClusterSelector;
}

export interface StatusSourceUpdate {
  /** @format date-time */
  startTimestamp?: string;
  cancelled?: boolean;
  sources?: SourceInfo;
  initiator?: string;
}

export interface SubscriptionAddon {
  name?: string;
  /** @format int64 */
  quantity?: number;
  /** @format int64 */
  includedQuantity?: number;
  displayName?: string;
  /** @format int64 */
  unitPrice?: number;
  /** @format int64 */
  minimumQuantity?: number;
  /** @format int64 */
  maximumQuantity?: number;
  description?: string;
}

export interface SyncOperationEvent {
  id?: string;
  instanceId?: string;
  applicationName?: string;
  startTime?: string;
  endTime?: string;
  resultPhase?: string;
  resultMessage?: string;
  details?: SyncOperationEventDetails;
  /** @format int64 */
  count?: number;
  lastOccurredTimestamp?: string;
  /** @format int64 */
  duration?: number;
}

export interface SyncOperationEventDetails {
  labels?: Record<string, string>;
  project?: string;
  repository?: string;
  revision?: string;
  prune?: boolean;
  dryRun?: boolean;
  syncOptions?: string[];
  initiatedBy?: ArgocdV1OperationInitiator;
}

export interface SyncOperationFilter {
  startTime?: string;
  endTime?: string;
  appName?: string[];
  projects?: string[];
  labels?: Record<string, string>;
  repo?: string[];
  instanceId?: string[];
  initiatedBy?: string[];
  instanceNames?: string[];
}

export interface SyncOperationStat {
  intervalStart?: string;
  countMap?: Record<string, number>;
  averageMap?: Record<string, number>;
}

export interface TargetSelector {
  argocdApplications?: string[];
  k8sNamespaces?: string[];
  clusters?: string[];
}

export interface Team {
  name?: string;
  description?: string;
  /** @format date-time */
  createTime?: string;
  /** @format int64 */
  memberCount?: string;
}

export interface TeamMember {
  id?: string;
  email?: string;
}

export interface TimelineEvent {
  id?: string;
  timelineResourceId?: string;
  clusterId?: string;
  instanceId?: string;
  eventType?: TimelineEventType;
  severity?: TimelineEventSeverity;
  reason?: string;
  message?: string;
  /** @format date-time */
  timestamp?: string;
  /** @format date-time */
  lastTimestamp?: string;
  /** @format int64 */
  count?: number;
  argocdApplicationLink?: string;
  oldValue?: string;
  newValue?: string;
}

export interface TimelineResource {
  /** group|kind|namespace|name */
  instanceId?: string;
  clusterId?: string;
  timelineResourceId?: string;
}

export interface UninviteOrganizationMemberBody {
  email?: string;
}

/** empty */
export type UninviteOrganizationMemberResponse = object;

export interface UnreadNotificationsRequest {
  notificationIds?: string[];
}

/** empty */
export type UnreadNotificationsResponse = object;

export interface UpdateAIConversationBody {
  title?: string;
  instanceId?: string;
  public?: boolean;
  incident?: IncidentConfig;
  contexts?: AIMessageContext[];
}

export interface UpdateAIConversationFeedbackBody {
  instanceId?: string;
  feedback?: string;
}

/** explicitly empty */
export type UpdateAIConversationFeedbackResponse = object;

/** explicitly empty */
export type UpdateAIConversationResponse = object;

export interface UpdateAIMessageFeedbackBody {
  messageId?: string;
  isUseful?: boolean;
  instanceId?: string;
}

/** explicitly empty */
export type UpdateAIMessageFeedbackResponse = object;

export interface UpdateAddonMarketplaceInstallBody {
  refresh?: boolean;
  dependencies?: ChartDependency[];
  force?: boolean;
}

export interface UpdateAddonMarketplaceInstallResponse {
  addonInstall?: V1AddonMarketplaceInstall;
}

export interface UpdateBillingDetailsBody {
  billingDetails?: BillingDetails;
}

/** empty */
export type UpdateBillingDetailsResponse = object;

export interface UpdateCustomRoleBody {
  name?: string;
  description?: string;
  policy?: string;
}

export interface UpdateCustomRoleResponse {
  customRole?: CustomRole;
}

export interface UpdateInstanceAccountPasswordBody {
  password?: string;
}

/** explicitly empty */
export type UpdateInstanceAccountPasswordResponse = object;

export interface UpdateInstanceAddonBody {
  addon?: Addon;
}

export interface UpdateInstanceAddonResponse {
  addon?: Addon;
}

export interface UpdateInstanceAgentVersionBody {
  agentNames?: string[];
  newVersion?: string;
}

/** explicitly empty */
export type UpdateInstanceAgentVersionResponse = object;

export interface UpdateInstanceBody {
  instance?: Instance;
}

export interface UpdateInstanceCSSBody {
  css?: string;
}

export interface UpdateInstanceCSSResponse {
  css?: string;
}

export interface UpdateInstanceClusterBody {
  description?: string;
  data?: ClusterData;
  force?: boolean;
}

export interface UpdateInstanceClusterResponse {
  cluster?: Cluster;
}

export interface UpdateInstanceClustersAgentVersionBody {
  clusterNames?: string[];
  newVersion?: string;
  allClusters?: boolean;
}

export interface UpdateInstanceClustersBody {
  clusterCustomizations?: ClusterCustomization;
  multiClusterK8sDashboardEnabled?: boolean;
}

export interface UpdateInstanceClustersResponse {
  skippedClusters?: string[];
}

export interface UpdateInstanceConfigManagementPluginsBody {
  plugins?: ConfigManagementPlugin[];
}

/** empty */
export type UpdateInstanceConfigManagementPluginsResponse = object;

export interface UpdateInstanceImageUpdaterConfigBody {
  config?: Record<string, string>;
  version?: string;
}

/** explicitly empty */
export type UpdateInstanceImageUpdaterConfigResponse = object;

export interface UpdateInstanceImageUpdaterSSHConfigBody {
  config?: Record<string, string>;
}

/** explicitly empty */
export type UpdateInstanceImageUpdaterSSHConfigResponse = object;

export interface UpdateInstanceNotificationConfigBody {
  config?: Record<string, string>;
}

/** explicitly empty */
export type UpdateInstanceNotificationConfigResponse = object;

export interface UpdateInstanceResourceCustomizationsBody {
  resources?: ResourceCustomizationConfig[];
}

/** explicitly empty */
export type UpdateInstanceResourceCustomizationsResponse = object;

export interface UpdateInstanceResponse {
  instance?: Instance;
}

export interface UpdateInstanceWorkspaceBody {
  newWorkspaceId?: string;
}

/** empty */
export type UpdateInstanceWorkspaceResponse = object;

export interface UpdateKargoInstanceAgentBody {
  description?: string;
  data?: KargoAgentData;
}

export interface UpdateKargoInstanceAgentResponse {
  agent?: KargoAgent;
}

export interface UpdateKargoInstanceAgentsBody {
  agentCustomizations?: KargoAgentCustomization;
}

/** empty */
export type UpdateKargoInstanceAgentsResponse = object;

export interface UpdateKargoInstanceWorkspaceBody {
  newWorkspaceId?: string;
}

export interface UpdateKargoInstanceWorkspaceResponse {
  instance?: KargoInstance;
}

export interface UpdateManagedSecretBody {
  managedSecret?: ManagedSecret;
  managedSecretData?: Record<string, string>;
}

/** explicitly empty */
export type UpdateManagedSecretResponse = object;

export interface UpdateNotificationConfigBody {
  webhook?: WebhookNotificationUpdatePayload;
  name?: string;
}

export interface UpdateNotificationConfigResponse {
  notificationConfig?: NotificationConfig;
}

export interface UpdateNotificationSettingsRequest {
  settings?: NotificationSettings;
}

/** empty */
export type UpdateNotificationSettingsResponse = object;

export interface UpdateOIDCMapBody {
  entries?: Record<string, string>;
}

/** explicitly empty */
export type UpdateOIDCMapResponse = object;

export interface UpdateOrganizationBody {
  name?: string;
  mfa?: MFASettings;
  ai?: AISettings;
}

export interface UpdateOrganizationMemberRoleBody {
  role?: string;
}

/** empty */
export type UpdateOrganizationMemberRoleResponse = object;

/** empty */
export type UpdateOrganizationResponse = object;

export interface UpdateSubscriptionBody {
  plan?: string;
  addons?: SubscriptionAddon[];
}

/** empty */
export type UpdateSubscriptionResponse = object;

export interface UpdateTeamBody {
  description?: string;
  customRoles?: string[];
}

export interface UpdateTeamOIDCMapBody {
  entries?: Record<string, string>;
}

/** explicitly empty */
export type UpdateTeamOIDCMapResponse = object;

export interface UpdateTeamResponse {
  userTeam?: UserTeam;
}

export interface UpdateUserUIPreferencesRequest {
  uiPreferences?: object;
}

/** empty */
export type UpdateUserUIPreferencesResponse = object;

export interface UpdateWorkspaceBody {
  name?: string;
  description?: string;
}

export interface UpdateWorkspaceCustomRoleBody {
  name?: string;
  description?: string;
  policy?: string;
}

export interface UpdateWorkspaceCustomRoleResponse {
  customRole?: CustomRole;
  workspaceId?: string;
}

export interface UpdateWorkspaceMemberBody {
  role?: WorkspaceMemberRole;
}

export interface UpdateWorkspaceMemberResponse {
  workspaceMember?: WorkspaceMember;
}

export interface UpdateWorkspaceMembersBody {
  memberRefs?: WorkspaceMemberRef[];
}

export interface UpdateWorkspaceMembersResponse {
  workspaceMembers?: WorkspaceMember[];
}

export interface UpdateWorkspaceResponse {
  workspace?: Workspace;
}

export interface UpsertInstanceAccountBody {
  capabilities?: InstanceAccountCapabilities;
  disabled?: boolean;
}

export interface UpsertInstanceAccountResponse {
  account?: InstanceAccount;
}

export interface User {
  id?: string;
  email?: string;
  organizations?: OrganizationSummary[];
  invitations?: OrganizationSummary[];
  unrestricted?: boolean;
  userInfo?: UserInfo;
  uiPreferences?: object;
  initialSignup?: boolean;
}

export interface UserInfo {
  givenName?: string;
  familyName?: string;
}

export interface UserMFAStatus {
  email?: string;
  enabled?: boolean;
  role?: string;
}

export interface UserTeam {
  team?: Team;
  isMember?: boolean;
  customRoles?: string[];
}

export interface VerifyOrganizationDomainsBody {
  domains?: string[];
}

export interface VerifyOrganizationDomainsResponse {
  domains?: DomainVerification[];
}

export interface WatchAddonMarketplaceInstallsResponse {
  item?: V1AddonMarketplaceInstall;
  type?: EventType;
}

export interface WatchInstanceAddonReposResponse {
  item?: AddonRepo;
  type?: EventType;
}

export interface WatchInstanceAddonsResponse {
  item?: Addon;
  type?: EventType;
}

export interface WatchInstanceClustersResponse {
  item?: Cluster;
  type?: EventType;
}

export interface WatchInstancesResponse {
  item?: Instance;
  type?: EventType;
}

export interface WatchKargoInstanceAgentsResponse {
  item?: KargoAgent;
  type?: EventType;
}

export interface WatchKargoInstancesResponse {
  item?: KargoInstance;
  type?: EventType;
}

export interface WatchNotificationsResponse {
  item?: Notification;
  type?: EventType;
}

export interface WebNotificationConfig {
  disabled?: boolean;
  disabledCategories?: NotificationCategory[];
}

export interface WebhookConfig {
  url?: string;
  /** secret will have dummy value (********) for security if set, or empty if not set */
  secret?: string;
  /** Use filter.events instead */
  events?: string[];
  active?: boolean;
  filter?: WebhookNotificationFilter;
}

export interface WebhookNotificationCreatePayload {
  url?: string;
  secret?: string;
  /** Use filter.events instead */
  events?: string[];
  filter?: WebhookNotificationFilter;
}

export interface WebhookNotificationDeliveryDetail {
  webhook?: WebhookNotificationDeliveryMetadata[];
}

export interface WebhookNotificationDeliveryMetadata {
  /** @format int64 */
  statusCode?: string;
  response?: string;
  error?: string;
  /** @format date-time */
  startTime?: string;
  /** @format date-time */
  endTime?: string;
  responseHeaders?: Record<string, string>;
  requestHeaders?: Record<string, string>;
  request?: string;
}

export interface WebhookNotificationDeliveryMetadataSummary {
  /** @format int64 */
  statusCode?: string;
  error?: string;
  /** @format date-time */
  startTime?: string;
  /** @format date-time */
  endTime?: string;
}

export interface WebhookNotificationFilter {
  events?: string[];
  filterArgocdInstanceNames?: boolean;
  argocdInstanceNames?: string[];
  filterKargoInstanceNames?: boolean;
  kargoInstanceNames?: string[];
}

export interface WebhookNotificationUpdatePayload {
  url?: string;
  usePreviousSecret?: boolean;
  /** secret is updated only if user_previous_secret is set to true. */
  secret?: string;
  /** Use filter.events instead */
  events?: string[];
  active?: boolean;
  filter?: WebhookNotificationFilter;
}

export interface Workspace {
  id?: string;
  name?: string;
  description?: string;
  /** @format date-time */
  createTime?: string;
  argocdInstances?: WorkspaceArgoCDInstance[];
  kargoInstances?: WorkspaceKargoInstance[];
  /** @format int64 */
  teamMemberCount?: number;
  /** @format int64 */
  userMemberCount?: number;
  isDefault?: boolean;
}

export interface WorkspaceArgoCDInstance {
  id?: string;
  name?: string;
}

export interface WorkspaceKargoInstance {
  id?: string;
  name?: string;
}

export interface WorkspaceMember {
  id?: string;
  role?: WorkspaceMemberRole;
  user?: WorkspaceUserMember;
  team?: WorkspaceTeamMember;
}

export interface WorkspaceMemberRef {
  role?: WorkspaceMemberRole;
  userId?: string;
  userEmail?: string;
  teamName?: string;
}

export interface WorkspaceTeamMember {
  id?: string;
  name?: string;
  description?: string;
  /** @format date-time */
  createTime?: string;
  /** @format int64 */
  memberCount?: string;
}

export interface WorkspaceUserMember {
  id?: string;
  email?: string;
}

export interface ArgocdV1CertificateStatus {
  isCnameSet?: boolean;
  isIssued?: boolean;
  message?: string;
}

/** explicitly empty */
export type ArgocdV1DeleteInstanceResponse = object;

export interface ArgocdV1OperationInitiator {
  username?: string;
  automated?: boolean;
}

export interface HealthV1Status {
  code?: HealthV1StatusCode;
  message?: string;
}

export interface KargoV1CertificateStatus {
  isCnameSet?: boolean;
  isIssued?: boolean;
  message?: string;
}

/** explicitly empty */
export type KargoV1DeleteInstanceResponse = object;

export interface KargoV1OperationInitiator {
  username?: string;
  automated?: boolean;
}

export interface ReconciliationV1Status {
  code?: ReconciliationV1StatusCode;
  message?: string;
}

export interface RpcStatus {
  /** @format int32 */
  code?: number;
  message?: string;
  details?: Any[];
}

export interface V1AddonMarketplaceInstall {
  id?: string;
  organizationId?: string;
  instanceId?: string;
  config?: AddonMarketplaceInstallConfig;
  statusInfo?: AddonMarketplaceStatus;
  addonFound?: boolean;
  checksumMatched?: boolean;
  /** @format date-time */
  deleteTime?: string;
}

export interface V1Parameters {
  /**
   * Use camel case to match the ConfigManagementPlugin definition of argocd
   * buf:lint:ignore FIELD_LOWER_SNAKE_CASE
   */
  static?: ParameterAnnouncement[];
  dynamic?: Dynamic;
}
