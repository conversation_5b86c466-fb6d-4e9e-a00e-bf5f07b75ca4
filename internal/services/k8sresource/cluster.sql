SELECT 
  acc.id AS id,
  acc.name AS name,
  aci.shard AS shard,
  aci.id AS instance_id,
  aci.name AS instance_name,
  COALESCE(acc.status_k8s_info, '{}'::jsonb) AS k8s_info,
  COALESCE(acic.spec->>'custom_deprecated_apis', '') AS custom_deprecated_apis,
  COALESCE(acc.status_agent_state->>'version', '') AS agent_version,
  COALESCE(acc.status_agent_state#>'{status,degraded,kubevision}', '{}'::jsonb) != '{}'::jsonb AS is_degraded,
  COALESCE(acc.spec->>'directClusterSpec' IS NULL AND acc.spec->>'multiClusterK8SDashboardEnabled' = 'true', false) AS is_enabled,
  COALESCE(aci.status_hostname, '') AS instance_hostname
FROM
  argo_cd_cluster acc
JOIN
  argo_cd_instance aci ON aci.id = acc.instance_id
JOIN
  argo_cd_instance_config acic ON aci.id = acic.instance_id
WHERE 
  aci.organization_owner = $1
  AND ($2::text IS NULL OR $2 = '' OR aci.id = $2) 
  AND ($3::text[] IS NULL OR $3 = '{}' OR acc.id = ANY($3)) 
