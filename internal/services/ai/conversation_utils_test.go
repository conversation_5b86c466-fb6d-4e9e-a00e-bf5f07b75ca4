package ai

import (
	"testing"
)

func TestIsValidResourceName(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected bool
	}{
		// Valid cases
		{
			name:     "valid lowercase name",
			input:    "argocd-redis",
			expected: true,
		},
		{
			name:     "valid name with numbers",
			input:    "argocd-redis-123",
			expected: true,
		},
		{
			name:     "valid name with hyphens",
			input:    "argocd-redis-ha",
			expected: true,
		},
		{
			name:     "valid name with single character",
			input:    "a",
			expected: true,
		},
		{
			name:     "valid name with maximum length",
			input:    "a" + string(make([]byte, 252)) + "z",
			expected: false,
		},

		// Invalid cases
		{
			name:     "empty name",
			input:    "",
			expected: false,
		},
		{
			name:     "name starting with number",
			input:    "123-argocd-redis",
			expected: true,
		},
		{
			name:     "name starting with hyphen",
			input:    "-argocd-redis",
			expected: false,
		},
		{
			name:     "name ending with hyphen",
			input:    "argocd-redis-",
			expected: false,
		},
		{
			name:     "name with uppercase letters",
			input:    "ArgoCD-Redis",
			expected: false,
		},
		{
			name:     "name with special characters",
			input:    "argocd-redis@ha",
			expected: false,
		},
		{
			name:     "name with spaces",
			input:    "argocd redis",
			expected: false,
		},
		{
			name:     "name with consecutive hyphens",
			input:    "argocd--redis",
			expected: true,
		},
		{
			name:     "name exceeding maximum length",
			input:    "a" + string(make([]byte, 253)) + "z",
			expected: false,
		},
		{
			name:     "name with dots",
			input:    "argocd.redis",
			expected: true,
		},
		{
			name:     "name with underscores",
			input:    "argocd_redis",
			expected: false,
		},
		{
			name:     "name with wildcard",
			input:    "argocd-redis-*",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := isValidResourceName(tt.input)
			if result != tt.expected {
				t.Errorf("isValidResourceName(%q) = %v, want %v", tt.input, result, tt.expected)
			}
		})
	}
}

func TestSetRunbooks_MergeAndDeduplicate(t *testing.T) {
	// Create a mock aiConversationUtils with existing runbooks
	utils := &aiConversationUtils{
		runbooks: []string{"existing-runbook-1", "existing-runbook-2"},
	}

	// Test case 1: Add new runbooks that don't exist
	newRunbooks := []string{"new-runbook-1", "new-runbook-2"}
	utils.setRunbooks(newRunbooks)

	expected := []string{"existing-runbook-1", "existing-runbook-2", "new-runbook-1", "new-runbook-2"}
	if len(utils.runbooks) != len(expected) {
		t.Errorf("Expected %d runbooks, got %d", len(expected), len(utils.runbooks))
	}

	// Check all expected runbooks are present
	runbookMap := make(map[string]bool)
	for _, rb := range utils.runbooks {
		runbookMap[rb] = true
	}
	for _, exp := range expected {
		if !runbookMap[exp] {
			t.Errorf("Expected runbook %s not found", exp)
		}
	}

	// Test case 2: Add runbooks with duplicates
	utils.runbooks = []string{"runbook-1", "runbook-2"}
	newRunbooks = []string{"runbook-2", "runbook-3", "runbook-1"}
	utils.setRunbooks(newRunbooks)

	expected = []string{"runbook-1", "runbook-2", "runbook-3"}
	if len(utils.runbooks) != len(expected) {
		t.Errorf("Expected %d runbooks after deduplication, got %d", len(expected), len(utils.runbooks))
	}

	// Test case 3: Empty new runbooks list should preserve existing
	utils.runbooks = []string{"existing-1", "existing-2"}
	originalRunbooks := make([]string, len(utils.runbooks))
	copy(originalRunbooks, utils.runbooks)

	newRunbooks = []string{}
	utils.setRunbooks(newRunbooks)

	if len(utils.runbooks) != len(originalRunbooks) {
		t.Errorf("Expected to preserve %d existing runbooks, got %d", len(originalRunbooks), len(utils.runbooks))
	}
}
