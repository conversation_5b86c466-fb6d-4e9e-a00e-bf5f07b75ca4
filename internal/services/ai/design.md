基于我对代码的理解，如果用户选择了runbooks并想在当前conversation中执行，我认为流程应该是这样的：

## 🔄 **Runbook执行流程设计**

### **1. 用户触发阶段**
- 用户在UI中选择一个或多个runbooks
- 前端调用 `CreateAIMessage` API，在 `runbooks` 字段中传入选中的runbook名称
- 用户可能会说类似 "Execute runbook pod-oom-fix" 或 "Run these runbooks" 的消息

### **2. 系统接收和存储阶段**
- `CreateMessage` 方法接收到runbooks参数
- 由于runbooks不为空，调用 `convUtil.setRunbooks(runbooks)`
- 新的runbooks与现有的合并去重，存入数据库
- `mergeRunbooks` 方法检测到变化，自动添加系统消息：`"New runbooks: [...]"`

### **3. AI处理阶段**
- AI收到用户消息和系统的runbooks更新消息
- AI识别到这是一个runbook执行请求
- AI调用 `get-runbooks-by-names` 工具函数获取runbook的完整内容
- AI分析runbook的Content，特别是：
  - **Symptoms** 部分：判断是否适用于当前情况
  - **Root cause** 部分：理解问题原因
  - **Solution** 部分：获取具体的执行步骤

### **4. 执行决策阶段**
- AI根据当前conversation的context（应用、命名空间等）判断runbook是否适用
- 如果有多个runbooks，AI会：
  - 分析每个runbook的症状匹配度
  - 确定执行优先级
  - 可能建议并行执行或顺序执行

### **5. 实际执行阶段**
- AI按照runbook的Solution步骤逐一执行
- 可能涉及调用各种工具函数：
  - `get-manifests` 获取资源配置
  - `get-logs` 查看日志
  - `get-events` 检查事件
  - 其他诊断和修复工具
- 每个步骤都会询问用户确认（除非runbook明确说明可以自动执行）

### **6. 结果反馈阶段**
- AI报告每个步骤的执行结果
- 如果遇到问题，AI会：
  - 解释失败原因
  - 建议替代方案
  - 可能建议执行其他相关runbooks
- 成功完成后，AI会总结执行结果

### **7. 后续监控阶段**
- AI可能建议监控应用状态
- 如果是incident场景，可能会更新incident状态
- 建议用户验证问题是否真正解决

## 🎯 **关键设计考虑**

### **安全性**
- 所有修改操作都需要用户确认
- 敏感操作（如删除、重启）需要明确授权
- 记录所有执行步骤用于审计

### **智能化**
- AI应该能够根据当前context自动适配runbook中的通用步骤
- 例如，runbook说"检查pod状态"，AI应该知道检查哪个具体的pod

### **错误处理**
- 如果runbook步骤失败，AI应该能够：
  - 回滚已执行的步骤（如果可能）
  - 提供故障排除建议
  - 建议联系人工支持

### **状态管理**
- 跟踪runbook执行进度
- 支持暂停和恢复执行
- 记录执行历史

这个流程的核心是让AI成为一个智能的runbook执行器，既能自动化执行标准化的修复步骤，又能在需要时寻求人工干预和确认。
