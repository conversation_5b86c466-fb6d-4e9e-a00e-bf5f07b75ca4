package organization

import (
	"context"
	"fmt"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"k8s.io/apimachinery/pkg/runtime/schema"

	"github.com/akuityio/akuity-platform/internal/portalapi/shared"
	"github.com/akuityio/akuity-platform/internal/services/accesscontrol"
	"github.com/akuityio/akuity-platform/internal/services/instances"
	"github.com/akuityio/akuity-platform/internal/services/k8sresource"
	ctxutil "github.com/akuityio/akuity-platform/internal/utils/context"
	"github.com/akuityio/akuity-platform/internal/utils/grpc/logging"
	"github.com/akuityio/akuity-platform/models/client"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
)

func (s *OrganizationV1Server) ListKubernetesEnabledClusters(
	ctx context.Context,
	req *organizationv1.ListKubernetesEnabledClustersRequest,
) (*organizationv1.ListKubernetesEnabledClustersResponse, error) {
	actor := ctxutil.GetActor(ctx)
	if actor == nil {
		return nil, shared.ErrUnauthenticated
	}

	// if actor is AKP user, we need to check the permission with enforcer
	// if actor is ArgoCD, we skip the permission check since ArgoCD user permissions are incompatible with the akp permission model.
	// ArgoCD users can only access resource from their own organization/instance
	if actor.Type != accesscontrol.ActorTypeArgoCD {
		if _, _, err := shared.EnforceOrganizationActions(ctx, s.Db, s.acs, req.GetOrganizationId(),
			accesscontrol.NewActionGetKubernetesDashboard(req.GetOrganizationId())); err != nil {
			return nil, err
		}
	} else {
		if actor.Extras["organizationID"] != req.GetOrganizationId() || actor.Extras["instanceID"] != req.GetInstanceId() {
			return nil, status.Errorf(codes.PermissionDenied, "organization or instance mismatch")
		}
	}

	if actor.Type != accesscontrol.ActorTypeArgoCD {
		// if actor is AKP user, we need to check if KubeVision is enabled (API is used by KubeVision and AI Assistant)
		orgID := req.GetOrganizationId()
		if !s.featSvc.GetFeatureStatuses(ctx, &orgID).GetMultiClusterK8SDashboard().Enabled() {
			return nil, status.Error(codes.PermissionDenied, "multi-cluster k8s dashboard feature is not enabled")
		}
	} else {
		// if actor is ArgoCD user, we need to check if KubeVision extension or AI Assistant extension is enabled
		instanceSvc := instances.NewServiceWithOptions(s.Db,
			s.Cfg.DomainSuffix,
			s.featSvc,
			instances.WithOrganizationScope(req.GetOrganizationId()),
			instances.WithLogger(logging.Extract(ctx)),
		)
		instanceConfig, err := instanceSvc.ArgoCDInstanceConfigs().GetByID(ctx, req.GetInstanceId())
		if err != nil {
			return nil, fmt.Errorf("get instance config: %w", err)
		}
		instanceSpec, err := instanceConfig.GetSpec()
		if err != nil {
			return nil, fmt.Errorf("get instance spec: %w", err)
		}
		if !instanceSpec.AkuityIntelligenceExtension.Enabled && !instanceSpec.AssistantExtensionEnabled {
			return nil, status.Error(codes.PermissionDenied, "kubevision or ai assistant extension is not enabled for this organization")
		}
	}

	rs := client.NewRepoSet(s.Db)
	resSvc := k8sresource.NewServiceWithOptions(rs, s.Db, req.GetOrganizationId(), k8sresource.WithLogger(logging.Extract(ctx)))
	clusterInfo, err := resSvc.GetEnabledClustersInfo(ctx, req.GetInstanceId(), true)
	if err != nil {
		return nil, err
	}
	clusters := clusterInfo.GetClusters()
	deprecatedGVKMap, err := resSvc.GetDeprecatedGVKs(clusters)
	if err != nil {
		return nil, err
	}
	enabledClusters := make([]*organizationv1.EnabledCluster, 0, len(clusters))
	for _, cluster := range clusters {
		info, err := cluster.GetK8sInfo()
		if err != nil {
			return nil, err
		}
		if req.HasDeprecatedApis != nil {
			hasDeprecatedApis := false
			for _, resourceType := range info.ResourceTypes {
				deprecatedInfo, ok := deprecatedGVKMap.GetDeprecatedGVK(cluster.InstanceID, schema.GroupVersionKind{
					Group:   resourceType.GetGroupVersionKind().GetGroup(),
					Version: resourceType.GetGroupVersionKind().GetVersion(),
					Kind:    resourceType.GetGroupVersionKind().GetKind(),
				})
				if !ok {
					continue
				}
				if k8sresource.DeprecatedInUpcomingVersion(deprecatedInfo, info.KubernetesVersion) {
					hasDeprecatedApis = true
					break
				}
			}
			if req.GetHasDeprecatedApis() == hasDeprecatedApis {
				enabledClusters = append(enabledClusters, &organizationv1.EnabledCluster{
					Shard:                   cluster.Shard,
					InstanceId:              cluster.InstanceID,
					InstanceName:            cluster.InstanceName,
					ClusterId:               cluster.ID,
					ClusterName:             cluster.Name,
					IsDegraded:              cluster.IsDegraded,
					MetricServerUnavailable: info.MetricServerUnavailable,
					IsEnabled:               cluster.IsEnabled,
					LastRefreshTime:         info.LastRefreshTime,
				})
			}
		} else {
			enabledClusters = append(enabledClusters, &organizationv1.EnabledCluster{
				Shard:                   cluster.Shard,
				InstanceId:              cluster.InstanceID,
				InstanceName:            cluster.InstanceName,
				ClusterId:               cluster.ID,
				ClusterName:             cluster.Name,
				IsDegraded:              cluster.IsDegraded,
				MetricServerUnavailable: info.MetricServerUnavailable,
				IsEnabled:               cluster.IsEnabled,
				LastRefreshTime:         info.LastRefreshTime,
			})
		}
	}

	return &organizationv1.ListKubernetesEnabledClustersResponse{
		Clusters: enabledClusters,
	}, nil
}
