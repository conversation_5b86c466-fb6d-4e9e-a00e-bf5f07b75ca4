    1  <NAME_EMAIL>:akuityio/agent.git
    2  git clone https://github.com/akuityio/infra-terraform.git
    3  <NAME_EMAIL>:akuityio/cloud-addons-deploy.git
    4  <NAME_EMAIL>:akuityio/infra-terraform.git
    5  <NAME_EMAIL>:akuityio/argocd-mcp-server.git
    6  <NAME_EMAIL>:akuityio/kubevision-scripts.git
    7  git
    8  mkdir MyPro
    9  ls -al ~/.ssh\n
   10  ssh-keygen -t ed25519 -C "<EMAIL>"
   11  eval "$(ssh-agent -s)"\nssh-add ~/.ssh/id_ed25519\n
   12  cat ~/.ssh/id_ed25519.pub\n
   13  <NAME_EMAIL>:akuityio/akuity-platform.git
   14  mkdir akuity
   15  mv akuity-platform akuity
   16  kubectl run nginx --image=nginx
   17  kubectl get pods
   18  kubectl get pods -A
   19  ls /usr/local/go/bin
   20  vim 
   21  echo My shell is $SHELL
   22  echo My dotfiles go in ${ZDOTDIR:-$HOME}
   23  bash --noprofile --norc
   24  make run-tools
   25  brew 
   26  brew
   27  brew install node\n
   28  npm 
   29  make install-tools-arm64
   30  go version
   31  sh -c "$(curl -fsSL https://raw.githubusercontent.com/ohmyzsh/ohmyzsh/master/tools/install.sh)"\n
   32  git config --global url."ssh://**************/".insteadOf "https://github.com/"\ngo env -w GOPRIVATE=github.com/akuityio
   33  kubectl -v
   34  kubectl version
   35  brew install kubectx
   36  cp .env.example .env
   37  chmod +x start-akp.sh
   38  ls .env
   39  cat .gh_token
   40  vim .gh_token
   41  cp .gh_token /Users/<USER>/MyPro/akuity/akuity-platform/
   42  helm
   43  brew install helm
   44  echo $DOCKER_PASSWORD
   45  kubectl get po -A |wc -l
   46  grep kube ~/.zshrc
   47  k9d
   48  cat /host
   49  cat /hosts
   50  alias kubectl="kubectl --context k3d-akuity-customer"
   51  TOKEN="eyJhbGciOiJSUzI1NiIsImtpZCI6IjFmM2RhNmRhNTQ0ZGI0Zjc0NjkxZjc3NzkzM2JhYzg3YmEzMmUxZmEifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.TrMHvz-mEY0jNm3Ja5LZC_qtm7ZGxZF_1XPzP4ppLE70vOy-EbHhRvCxEpQupGyUWImS8gT8g-fZ0IFWi1F-lK1rNMXYzI6R2KDjTz9kNW-A09Rln9tf_7chLXeV2g5WXw1nR2pAQ_DY6Of2_nw7RLhsjiKbRjgS5PeZpf_G2w9lk8bA1dJZSwB5iQvrU6MW4H50tYzVQSKQLFelpEuJAz7YsSfZ1_WHS8omn5gcHByNSexp9PSScCvOcwTrt9Lxn6rMphmDPcWO9H9JiUGcfWQfCvzF3kdNtWj0qNA-NYUBV724RpQghFiGRMEexHjH_tw8sQZMM0TiJk9dPMamkQ" && curl -s -H "Authorization: Bearer $TOKEN" "https://portal-server.akuity-platform/api/v1/orgs/7ilygwd9qpf19yx3/argocd/instances/pnld1cbqcp5hxp32/clusters/7erhrsagm77uvm0r/manifests" | kubectl apply -f -
   52  TOKEN="eyJhbGciOiJSUzI1NiIsImtpZCI6IjFmM2RhNmRhNTQ0ZGI0Zjc0NjkxZjc3NzkzM2JhYzg3YmEzMmUxZmEifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.TrMHvz-mEY0jNm3Ja5LZC_qtm7ZGxZF_1XPzP4ppLE70vOy-EbHhRvCxEpQupGyUWImS8gT8g-fZ0IFWi1F-lK1rNMXYzI6R2KDjTz9kNW-A09Rln9tf_7chLXeV2g5WXw1nR2pAQ_DY6Of2_nw7RLhsjiKbRjgS5PeZpf_G2w9lk8bA1dJZSwB5iQvrU6MW4H50tYzVQSKQLFelpEuJAz7YsSfZ1_WHS8omn5gcHByNSexp9PSScCvOcwTrt9Lxn6rMphmDPcWO9H9JiUGcfWQfCvzF3kdNtWj0qNA-NYUBV724RpQghFiGRMEexHjH_tw8sQZMM0TiJk9dPMamkQ" && curl -s -k -H "Authorization: Bearer $TOKEN" "https://portal-server.akuity-platform/api/v1/orgs/7ilygwd9qpf19yx3/argocd/instances/pnld1cbqcp5hxp32/clusters/7erhrsagm77uvm0r/manifests" | kubectl apply -f -
   53  kubectl -nakuity logs argocd-application-controller-84b99976f9-8ljjm
   54  kubectl -nakuity logs argocd-application-controller-84b99976f9-8ljjm -c application-controller
   55  kubectl -nakuity get po argocd-application-controller-84b99976f9-8ljjm
   56  kubectl -nakuity get po argocd-application-controller-84b99976f9-8ljjm -oyaml
   57  kubectl -nakuity logs argocd-application-controller-84b99976f9-8ljjm -c argocd-application-controller
   58  kubectl get po
   59  kubectl -nargocd-pnld1cbqcp5hxp32 get po
   60  kubectl -nargocd-pnld1cbqcp5hxp32 logs argocd-application-controller-67d45c99f6-cx5tf
   61  kubectl -nakuity delete po argocd-application-controller-67c548df5-26plf
   62  kubectl -nakuity logs argocd-application-controller-67c548df5-4vclj -c argocd-application-controller
   63  kubectl -n akuity-platform get po
   64  kubectl -n argocd-pnld1cbqcp5hxp32 logs k3s-f64595bf4-kwqxr
   65  kubectl -n argocd-pnld1cbqcp5hxp32 logs agent-server-6f4476698-8ppnf
   66  kubectl -nakuity logs akuity-agent-5789dc8cd4-5g7pj
   67  kubectl get svc -A
   68  kubectl -nakuity get po
   69  kubectl -n argocd-pnld1cbqcp5hxp32 get po
   70  kubectl -n argocd-pnld1cbqcp5hxp32 logs argocd-server-5478c87fc4-74p6d
   71  brew install derailed/k9s/k9s
   72  kubect -h
   73  kubectl context -h
   74  kubectl 
   75  kubectl cluster-info
   76  kubectl get po -A --context k3d-akuity-customer
   77  kubectl get ns
   78  kubect get po -ndefault
   79  kubectl get po -ndefault
   80  brew install argocd
   81  argocd 
   82  argocd login
   83  kubectl -n akuity-platform get secret
   84  kubectl -n akuity-platform edit akuity-platform
   85  kubectl -nakuity-platform delete po portal-server-85bb7686d6-jp24b
   86  kubectl -nakuity-platform get po
   87  kubectl config get-contexts\n
   88  TOKEN="eyJhbGciOiJSUzI1NiIsImtpZCI6IjFmM2RhNmRhNTQ0ZGI0Zjc0NjkxZjc3NzkzM2JhYzg3YmEzMmUxZmEifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.TrMHvz-mEY0jNm3Ja5LZC_qtm7ZGxZF_1XPzP4ppLE70vOy-EbHhRvCxEpQupGyUWImS8gT8g-fZ0IFWi1F-lK1rNMXYzI6R2KDjTz9kNW-A09Rln9tf_7chLXeV2g5WXw1nR2pAQ_DY6Of2_nw7RLhsjiKbRjgS5PeZpf_G2w9lk8bA1dJZSwB5iQvrU6MW4H50tYzVQSKQLFelpEuJAz7YsSfZ1_WHS8omn5gcHByNSexp9PSScCvOcwTrt9Lxn6rMphmDPcWO9H9JiUGcfWQfCvzF3kdNtWj0qNA-NYUBV724RpQghFiGRMEexHjH_tw8sQZMM0TiJk9dPMamkQ" && curl -s -H "Authorization: Bearer $TOKEN" "https://portal-server.akuity-platform/api/v1/orgs/7ilygwd9qpf19yx3/argocd/instances/gyaerd9nyvieq8fg/clusters/wjvtoe181jv6kem7/manifests" -k | kubectl apply -f -
   89  kubectl-ns
   90  kubectl-ctx
   91  kubectl-ctx k3d-akuity-customer
   92  TOKEN="eyJhbGciOiJSUzI1NiIsImtpZCI6IjFmM2RhNmRhNTQ0ZGI0Zjc0NjkxZjc3NzkzM2JhYzg3YmEzMmUxZmEifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.TrMHvz-mEY0jNm3Ja5LZC_qtm7ZGxZF_1XPzP4ppLE70vOy-EbHhRvCxEpQupGyUWImS8gT8g-fZ0IFWi1F-lK1rNMXYzI6R2KDjTz9kNW-A09Rln9tf_7chLXeV2g5WXw1nR2pAQ_DY6Of2_nw7RLhsjiKbRjgS5PeZpf_G2w9lk8bA1dJZSwB5iQvrU6MW4H50tYzVQSKQLFelpEuJAz7YsSfZ1_WHS8omn5gcHByNSexp9PSScCvOcwTrt9Lxn6rMphmDPcWO9H9JiUGcfWQfCvzF3kdNtWj0qNA-NYUBV724RpQghFiGRMEexHjH_tw8sQZMM0TiJk9dPMamkQ" && curl -s -H "Authorization: Bearer $TOKEN" "https://portal-server.akuity-platform/api/v1/orgs/7ilygwd9qpf19yx3/argocd/instances/gyaerd9nyvieq8fg/clusters/pz0x138eg1y9r6ee/manifests" -k | kubectl apply -f -
   93  kubect logs -nakuity argocd-application-controller-5f969b5747-m5cgq
   94  kubectl logs -nakuity argocd-application-controller-5f969b5747-m5cgq
   95  kubectl logs -nakuity akuity-agent-6d58b575cf-wnwr9
   96  kubectl logs -nakuity akuity-agent-6d58b575cf-jlqv8
   97  kubectx k3d-akuity-customer1
   98  kubectl get po -A -w
   99  kubectl get po -A 
  100  docker list
  101  k describe po -n akuity-platform kubevision-event-cleaner-29089660-h6hjg
  102  kubect delete po -n akuity-platform kubevision-event-cleaner-29089680-6fxbf kubevision-event-cleaner-29088740-55q9g
  103  kubectl delete po -n akuity-platform kubevision-event-cleaner-29089680-6fxbf kubevision-event-cleaner-29088740-55q9g
  104  kubectl delete po -nakuity-platform kubevision-event-cleaner-29088740-t96qt kubevision-event-cleaner-29088740-fsphn 
  105  k ge po -n akuity akuity-agent-6d58b575cf-jlqv8 -oyaml
  106  k get po -n akuity akuity-agent-6d58b575cf-jlqv8 -oyaml
  107  ping 81.71.159.177
  108  sudo xattr -dr com.apple.quarantine /Applications/Hidden\ Bar.app
  109  k3s 
  110  k3d clust list
  111  k3d cluster delete akuity-customer akuity-customer2
  112  k describe po -n akuity-platform portaldb-update-dkvl6
  113  k logs -n akuity-platform portaldb-update-dkvl6
  114  k delete po -n akuity-platform instances-upgrader-nfflz portaldb-update-dkvl6
  115  TOKEN="******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" && curl -s -H "Authorization: Bearer $TOKEN" "https://portal-server.akuity-platform/api/v1/orgs/71ylx29kv8uvbqui/argocd/instances/jqfeatoz5rxz5qxb/clusters/9ss3e1263o9qsaf5/manifests" -k | kubectl apply -f -
  116  k get po -nakuity argocd-application-controller-7df454c5dd-jxn7d -oyaml
  117  k get po -nakuity akuity-agent-5b6d488469-s5bf8 -oyaml
  118  k get po -nargocd-jqfeatoz5rxz5qxb agent-server-6f4476698-wkqx6 -oyaml
  119  k logs -n akuity akuity-agent-5b6d488469-s5bf8
  120  kubectl get po -nakuity akuity-agent-5b6d488469-4gcmr -oyaml
  121  kubectl get po -nakuity akuity-agent-5b6d488469-4gcmr -oyaml|grep AKUITY_SERVER_URL
  122  kubect get po -A -oyaml|grep AKUITY_SERVER_URL
  123  kubectl get po -A -oyaml|grep AKUITY_SERVER_URL
  124  kubectl get po -A -oyaml
  125  kubectl get po -A -oyaml > customer.yaml
  126  kubectl get po -A -oyaml > mgm.yaml
  127  grep AKUITY_SERVER_URL mgm.yaml
  128  grep URL mgm.yaml
  129  grep URL customer.yaml
  130  vim mgm.yaml
  131  grep AKUITY *.yaml
  132  grep config *.yaml
  133  grep URL *.yaml
  134  cd argocd-mcp-server
  135  mkdir -p /Users/<USER>/MyPro/Xcode/Xmusic/Xmusic/Models /Users/<USER>/MyPro/Xcode/Xmusic/Xmusic/Services /Users/<USER>/MyPro/Xcode/Xmusic/Xmusic/ViewModels /Users/<USER>/MyPro/Xcode/Xmusic/Xmusic/Views /Users/<USER>/MyPro/Xcode/Xmusic/Xmusic/Utilities
  136  cd /Users/<USER>/MyPro/Xcode/Xmusic && xcodebuild -project Xmusic.xcodeproj -scheme Xmusic -sdk appletvsimulator -destination "platform=tvOS Simulator,name=Apple TV" -quiet 2>&1 | grep -B 2 -A 5 "error:"
  137  cd /Users/<USER>/MyPro/Xcode/Xmusic && xcodebuild -project Xmusic.xcodeproj -scheme Xmusic -sdk appletvsimulator -destination "platform=tvOS Simulator,name=Apple TV" clean build 2>&1 | grep "error:"
  138  cd /Users/<USER>/MyPro/Xcode/Xmusic && xcodebuild -project Xmusic.xcodeproj -scheme Xmusic -sdk appletvsimulator -destination "platform=tvOS Simulator,name=Apple TV" clean build
  139  cd /Users/<USER>/MyPro/Xcode/Xmusic && xcodebuild -project Xmusic.xcodeproj -scheme Xmusic -sdk appletvsimulator -destination "platform=tvOS Simulator,name=Apple TV" clean build 2>&1 | grep "error:" || echo "没有错误，构建成功！"
  140  cd /Users/<USER>/MyPro/Xcode
  141  cd /Users/<USER>/MyPro/Xcode/Xmusic && xcodebuild -project Xmusic.xcodeproj -scheme Xmusic -sdk appletvsimulator -destination "platform=tvOS Simulator,name=Apple TV" build
  142  cd /Users/<USER>/MyPro/Xcode/Xmusic && xcodebuild -project Xmusic.xcodeproj -scheme Xmusic -sdk appletvsimulator -destination "platform=tvOS Simulator,name=Apple TV" -configuration Debug | grep -E "error|warning"
  143  cd /Users/<USER>/MyPro/Xcode/Xmusic && xcodebuild -project Xmusic.xcodeproj -scheme Xmusic -sdk appletvsimulator -destination "platform=tvOS Simulator,name=Apple TV" -configuration Debug | grep -E "error|warning" | head -10
  144  cd /Users/<USER>/MyPro/Xcode/Xmusic && xcodebuild -project Xmusic.xcodeproj -scheme Xmusic -sdk appletvsimulator -destination "platform=tvOS Simulator,name=Apple TV" -configuration Debug
  145  cd /Users/<USER>/MyPro/Xcode/Xmusic && xcodebuild -project Xmusic.xcodeproj -scheme Xmusic -sdk appletvsimulator -destination "platform=tvOS Simulator,name=Apple TV" -configuration Debug | grep -E "error|warning" | head -15
  146  cd /Users/<USER>/MyPro/Xcode/Xmusic && xcodebuild -project Xmusic.xcodeproj -scheme Xmusic -configuration Debug -sdk iphonesimulator -verbose -list
  147  cd /Users/<USER>/MyPro/Xcode/Xmusic && xcodebuild -project Xmusic.xcodeproj -scheme Xmusic -configuration Debug -sdk iphonesimulator clean build
  148  cd /Users/<USER>/MyPro/Xcode/Xmusic && xcodebuild -project Xmusic.xcodeproj -scheme Xmusic -list
  149  cd Xmusic && xcodebuild -project Xmusic.xcodeproj -scheme Xmusic -configuration Debug
  150  xcodebuild -project Xmusic.xcodeproj -scheme Xmusic -configuration Debug
  151  cd .. && xcodebuild -project Xmusic.xcodeproj -scheme Xmusic -configuration Debug
  152  cd /Users/<USER>/MyPro/Xcode/Xmusic && xcodebuild -project Xmusic.xcodeproj -scheme Xmusic -destination "platform=tvOS Simulator,name=Apple TV" | cat
  153  cd /Users/<USER>/MyPro/Xcode/Xmusic && xcodebuild -scheme Xmusic -configuration Debug -destination "platform=iOS Simulator,name=iPhone 14" build
  154  chmod +x CursorFreeVIP_1.11.01_mac_arm64
  155  cd "${WORKSPACE_DIR}" && xcodebuild -project Xmusic.xcodeproj -scheme Xmusic | cat
  156  cd "${WORKSPACE_DIR}" && xcodebuild -project Xmusic.xcodeproj -scheme Xmusic -sdk appletvsimulator -allowProvisioningUpdates | cat
  157  cd "${WORKSPACE_DIR}" && swift --version | cat
  158  cd "${WORKSPACE_DIR}" && xcodebuild -project Xmusic.xcodeproj -scheme Xmusic -sdk appletvsimulator -allowProvisioningUpdates | grep "error:" | head -20
  159  cd "${WORKSPACE_DIR}" && xcodebuild -project Xmusic.xcodeproj -scheme Xmusic -sdk appletvsimulator -allowProvisioningUpdates | grep "error:" | head -10
  160  cd "${WORKSPACE_DIR}" && xcodebuild -project Xmusic.xcodeproj -scheme Xmusic -sdk appletvsimulator -allowProvisioningUpdates -quiet | grep "error:" | head -10
  161  cd "${WORKSPACE_DIR}" && xcodebuild -project Xmusic.xcodeproj -scheme Xmusic -sdk appletvsimulator -allowProvisioningUpdates -destination "platform=tvOS Simulator,name=Apple TV" build
  162  cd /Users/<USER>/MyPro/Xcode/Xmusic && xcodebuild -showBuildSettings
  163  cd /Users/<USER>/MyPro/Xcode/Xmusic && xcodebuild -scheme Xmusic -destination "platform=tvOS Simulator,name=Apple TV" clean build | grep -i error
  164  cd /Users/<USER>/MyPro/Xcode/Xmusic && xcodebuild -project Xmusic.xcodeproj -scheme Xmusic -destination 'platform=iOS Simulator,name=iPhone 15' clean build
  165  cat /Users/<USER>/MyPro/Xcode/Xmusic/Xmusic/Services/MusicAPIService.swift | grep -A 35 "extension APIConstants"
  166  cd /Users/<USER>/MyPro/Xcode/Xmusic && grep -n "extension APIConstants" Xmusic/Services/MusicAPIService.swift
  167  cd /Users/<USER>/MyPro/Xcode/Xmusic && sed -i '' -e '243,$d' Xmusic/Services/MusicAPIService.swift
  168  cd /Users/<USER>/MyPro/Xcode/Xmusic && xcodebuild -project Xmusic.xcodeproj -scheme Xmusic -destination 'platform=tvOS Simulator,name=Apple TV' clean build
  169  cd /Users/<USER>/MyPro/Xcode/Xmusic && xcodebuild -scheme Xmusic build -sdk iphonesimulator
  170  cd /Users/<USER>/MyPro/Xcode/Xmusic && grep -A 3 "import" Xmusic/Services/MusicAPIService.swift
  171  cd /Users/<USER>/MyPro/Xcode/Xmusic && grep -r "^import" --include="*.swift" .
  172  mkdir SlackUp
  173  /Users/<USER>/MyPro/SlackUp/SlackUp; exit
  174  rew install cliclick
  175  brew install cliclick
  176  mkdir -p TVMusic/Services TVMusic/ViewModels TVMusic/Views
  177  xcodebuild -list
  178  find /Users/<USER>/MyPro/Xcode/Xmusic -name "Info.plist"
  179  xcodebuild clean build -scheme TVMusic -destination 'platform=tvOS Simulator,name=Apple TV 4K (3rd generation)' | grep -E 'error:|warning:' || echo "Build succeeded"
  180  cd /Users/<USER>/MyPro/Xcode/Xmusic && xcodebuild -scheme Xmusic build -sdk appletvsimulator
  181  cd /Users/<USER>/MyPro/Xcode/Xmusic && xcodebuild -list
  182  cd /Users/<USER>/MyPro/Xcode/Xmusic && xcodebuild -project Xmusic.xcodeproj -target Xmusic -destination "platform=tvOS Simulator,name=Apple TV" -configuration Debug clean build
  183  plutil -p /Users/<USER>/MyPro/Xcode/TVMusic/TVMusic.xcodeproj/project.pbxproj | grep "INFOPLIST_FILE"
  184  cd /Users/<USER>/MyPro/Xcode/Xmusic && ls -la Xmusic/Views
  185  find . -name "*.plist" | grep -v "project.pbxproj" | xargs rm -f
  186  plutil -convert xml1 TVMusic.xcodeproj/project.pbxproj
  187  cat TVMusic.xcodeproj/project.pbxproj | grep -i "INFOPLIST"
  188  rm -rf ~/Library/Developer/Xcode/DerivedData/TVMusic-* && echo "已清除DerivedData"
  189  find . -name "*.pbxproj" -exec grep -l "Copy Bundle Resources" {} \;
  190  find . -name "Info.plist"
  191  rm -rf ~/Library/Developer/Xcode/DerivedData/TVMusic-*
  192  kubectx orbstack
  193  KK
  194  kk 
  195  k get po -A |grep portal
  196  k get svc -A cluster-my-cluster
  197  k get svc -n argocd-jqfeatoz5rxz5qxb cluster-my-cluster -oyaml
  198  k logs -n akuity-platform portal-server-5bc8d84898-mnlfd -f
  199  k get po -n argocd-jqfeatoz5rxz5qxb agent-server-6f4476698-zhh2x
  200  k logs -n argocd-jqfeatoz5rxz5qxb agent-server-6f4476698-zhh2x
  201  k logs -n argocd-jqfeatoz5rxz5qxb agent-server-6f4476698-wkqx6
  202  k logs -n akuity akuity-agent-5b6d488469-4gcmr
  203  kubect delete po -n akuity akuity-agent-5b6d488469-4gcmr akuity-agent-5b6d488469-s5bf8
  204  k delete po -n akuity akuity-agent-5b6d488469-4gcmr akuity-agent-5b6d488469-s5bf8
  205  k logs -nakuity akuity-agent-5b6d488469-cnwn5
  206  k get po -nakuity akuity-agent-5b6d488469-cnwn5
  207  k get po -nakuity akuity-agent-5b6d488469-cnwn5 -oyaml|grep URL
  208  k get po -nakuity akuity-agent-5b6d488469-cnwn5 -oyaml|grep url
  209  k get po -nakuity akuity-agent-5b6d488469-cnwn5 -oyaml
  210  k get po -nakuity akuity-agent-5b6d488469-cnwn5 -oyaml|grep akuity-agent
  211  k get po -nakuity akuity-agent-5b6d488469-cnwn5 -oyaml > a.yaml
  212  k0
  213  k logs -n akuity-platform portal-server-5bc8d84898-mnlfd |grep duplicatesDetails
  214  k logs -nakuity-platform agent-server-6f4476698-wkqx6 |grep duplicatesDetails
  215  k logs -nakuity-platform agent-server-6f4476698-zhh2x |grep duplicatesDetails
  216  k logs -nargocd-jqfeatoz5rxz5qxb agent-server-6f4476698-wkqx6 |grep duplicatesDetails
  217  k logs -nargocd-jqfeatoz5rxz5qxb agent-server-6f4476698-wkqx6 
  218  k logs -n akuity-platform portal-server-5bc8d84898-mnlfd
  219  k logs -n akuity-platform portal-server-5bc8d84898-mnlfd|grep resource
  220  k logs -n akuity-platform portal-server-5bc8d84898-mnlfd|grep resources
  221  wget
  222  brew install wget
  223  brew install openjdk@21
  224  java -version
  225  brew install kustomize
  226  brew install brew install kustomize
  227  brew install protoc
  228  brew install protobuf
  229  protoc
  230  brew install protoc-gen-doc
  231  git diff brew install protoc-gen-doc
  232  cd MyPro/
  233  git checkout models/models/argo_cd_cluster_k8s_object.gen.go
  234  git diff models/models/argo_cd_cluster_k8s_object.gen.go
  235  git commit -m "Add update timestamp" -s
  236  TOKEN="eyJhbGciOiJSUzI1NiIsImtpZCI6IjljY2E4MmE0N2E1ZWI3OGQ2NzZiMTMxMmJkYWI2MzM4ZTA3OWM3YjMifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.cU9t10B9yoUD9yDEkR8_s5LzBdxpUiS3TL_up2oMWXgim2X2qGXElmvpQqXOIB864U69n5V8vZOX4t6ne972qAsdimuF5lJKuNVgsXR2apCYiv-XOK8ExylF0Oykiyre4i10biPF2FqZEgIYQD7-EAUAUaQ6W5AT71qykA8QnYrZ0Y_1U51qFf3UJGY0pYsPc16Xf-TVuW2HHjQ-pIvawgnrMZ2Sx8BOpn4bsp6PGPUmr2u4oFd1fhC0qcqF5vOw1G-CBYTIXP8I0N32WS9IN1uKmIMtkE-0Zj324eG3BHr8n3u_4ZJ6mvMdCmB306K8_7mnsz7nsPb53hrgLs9g7g" && curl -s -H "Authorization: Bearer $TOKEN" "https://portal-server.akuity-platform/api/v1/orgs/p07sex8y3zltb98b/argocd/instances/30o203m14c7u8rqh/clusters/mz2n0bmsz84euar3/manifests" -k | kubectl apply -f -
  237  cleat
  238  kubectx ls
  239  k3d -h
  240  TOKEN="eyJhbGciOiJSUzI1NiIsImtpZCI6ImIxNDRjZTEzNWY5MTJmNDdhYjJlNTU4OWQ4MzEyZjdmY2ZmMjBlMzMifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.aFveITWchM141AanoDp9JLLpbc7ErSNCEo9gNNjM_w9HeRuIJsJ8d4Lyy6zTSIBczWyBDn3TFaha_6x-OdYrvdh8R_JfkZIQrwVnXF-qDhw9AeKv4JUsRDbk3pS6cm1mRaTbP1D8Fk6z9dyCnoisMEi2iYF8MRe5JCKZDYSRyHSr_23z9QZglaSis1qCXclWa0Nr4ZMGTE2yAoa9M___pPGUtLdIO2gJ1EUjQ91UJWgODPMJEVrwdx4xbycnKfEEbOC2YXFfxuMFxV_KaCFuR2aWbPXhw3t2gkkJInTcxry7HJzjS45PXssGOrCg4VYk5bjyvFNZZHcp1uTYKw7kqw" && curl -s -H "Authorization: Bearer $TOKEN" "https://portal-server.akuity-platform/api/v1/orgs/cd2ivwdp19l7m8k6/argocd/instances/sxdxh34t9hcxgek5/clusters/rr2rnmi3s997qh17/manifests" -k | kubectl apply -f -
  241  git checkout ./*
  242  git checkout aims api docs internal models pkg portal
  243  git diff agent/api/proto/agent/v1/agent.proto
  244  git checkout internal/services/k8sresource/k8sinfo.go agent/api/proto/agent/v1/agent.proto
  245  go get github.com/volatiletech/null
  246  k get po -A -oyaml|grep akuity-platform:latest
  247  k get po -A -oyaml|grep akuity-platform
  248  k get po -n akuity-platform addon-controller-677467744f-nv9gv -oyaml|grep "akuity-platform:latest"
  249  k delete po -n akuity-platform addon-controller-677467744f-nv9gv dex-5b75bfd8cb-vnqx5 platform-controller-77df5f8465-z4zvz portal-server-7cfbd9d799-rx27b postgres-7dc7d7d699-9r92w
  250  kubect get po -nakuity-platform -w
  251  k logs -n akuity-platform portal-server-7cfbd9d799-rx27b
  252  k logs -n akuity-platform portal-server-7cfbd9d799-9fs9g
  253  k get po -nakuity-platform
  254  git dff internal/agentapi/update_cluster_kubernetes_resources_v1.go
  255  git diff internal/agentapi/update_cluster_kubernetes_resources_v1.go
  256  git config --global user.name "Ming Qiu"\n
  257  git config --global user.email "<EMAIL>"\n
  258  git commit -m "fix:add refresh time in KubeVision" -s
  259  git push --set-upstream origin refresh-time-kubevision
  260  mkdir golang
  261  mkdir FilesShareServer
  262  mkdir FileServer
  263  go rum main.go --dir=./
  264  go run main.go -dir=./ -port=8080
  265  hostname 
  266  hostname -i
  267  hostname -I
  268  k delete po -n akuity-platform kubevision-event-cleaner-29097340-64x6j kubevision-event-cleaner-29097340-b56sd kubevision-event-cleaner-29097340-w86rg
  269  cat Makefile
  270  history|grep make
  271  git diff ../docs/generated/swagger/apidocs.swagger.yaml
  272  git checkut main
  273  git cherry-pick 530964dfcd69b5ed00c7720d02c22fc6d262322a
  274  ping  *************
  275  git config --global url."https://github.com/".insteadOf **************:\n
  276  ssh -T **************\n
  277  GO111MODULE=on go get github.com/akuityio/akuity-platform@main\n
  278  k logs -nakuity-platform portal-server-7cfbd9d799-plls8 
  279  k logs -nakuity-platform portal-server-7cfbd9d799-plls8 |grep vae
  280  k logs -nakuity-platform portal-server-7cfbd9d799-plls8 -f|grep vae
  281  chmod +x SlackUp
  282  rm -rf SlackUp
  283  which cliclick
  284  cliclick
  285  git checkout -b refresh-time-kubevision
  286  ./CursorFreeVIP_1.11.01_mac_arm64
  287  git clone https://github.com/argoproj/argo-cd.git
  288  kubectl get po -nakuity-platform addon-controller-677467744f-j9gx2 -oyaml
  289  n)
  290  brew install export PATH="/opt/homebrew/opt/openjdk@21/bin:$PATH"\nexport JAVA_HOME="/opt/homebrew/opt/openjdk@21"
  291  git diff internal/services/k8sresource/k8sinfo.go
  292  git add agent internal
  293  k logs -nakuity-platform portal-server-7cfbd9d799-plls8
  294  k logs -n akuity-platform portal-server-7cfbd9d799-hgpzv
  295  k logs -n akuity-platform portal-server-7cfbd9d799-hgpzv|grep error
  296  k logs -n akuity-platform portal-server-7cfbd9d799-7lbm2 
  297  k logs -n akuity-platform portal-server-7cfbd9d799-7lbm2 |grep last_refresh_time
  298  k logs -n akuity-platform portal-server-7cfbd9d799-4vpk5 
  299  k logs -n akuity-platform portal-server-7cfbd9d799-4vpk5 |grep last_refresh_time
  300  k logs -n akuity-platform portal-server-7cfbd9d799-4vpk5 |grep status_k8s_info
  301  k logs -n akuity-platform portal-server-7cfbd9d799-wx8kn
  302  k logs -n akuity-platform portal-server-7cfbd9d799-wx8kn|grep refresh
  303  k logs -n akuity-platform portal-server-7cfbd9d799-wx8kn -f|grep refresh
  304  k logs -n akuity-platform portal-server-7cfbd9d799-qgcvb -f
  305  k logs -n akuity-platform portal-server-7cfbd9d799-qgcvb -f|grep refresh
  306  k logs -n akuity-platform portal-server-7cfbd9d799-qgcvb|grep error
  307  k logs -n akuity-platform portal-server-7cfbd9d799-qgcvb|grep status_k8s_info
  308  k logs -n akuity-platform portal-server-7cfbd9d799-qgcvb|grep refresh
  309  k logs -n akuity-platform portal-server-7cfbd9d799-qgcvb|grep k8sinfo
  310  k logs -n akuity-platform portal-server-7cfbd9d799-tbk46
  311  k logs -n akuity-platform portal-server-7cfbd9d799-tbk46|grep refresh
  312  git diff internal/agentapi/update_cluster_kubernetes_resources_v1.go internal/services/k8sresource/k8sinfo.go
  313  k logs -n akuity-platform portal-server-7cfbd9d799-dc68j|grep k8s
  314  k logs -n akuity-platform portal-server-7cfbd9d799-dc68j|grep k8sinfo
  315  k logs -n akuity-platform portal-server-7cfbd9d799-dc68j
  316  k logs -n akuity-platform portal-server-7cfbd9d799-dc68j|grep refresh
  317  k logs -n akuity-platform portal-server-7cfbd9d799-qqkrs|grep refresh
  318  k logs -n akuity-platform portal-server-7cfbd9d799-qqkrs
  319  git diff update_cluster_kubernetes_info_v1.go
  320  git status|grep update_cluster_kubernetes_info_v1.go
  321  TOKEN="******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" && curl -s -H "Authorization: Bearer $TOKEN" "https://portal-server.akuity-platform/api/v1/orgs/zoc58il35sml3p7i/argocd/instances/5gsadp5phdzd02et/clusters/9k5ts9fxq3v5k1ef/manifests" -f | kubectl apply -f -
  322  TOKEN="******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" && curl -s -H "Authorization: Bearer $TOKEN" "https://portal-server.akuity-platform/api/v1/orgs/zoc58il35sml3p7i/argocd/instances/5gsadp5phdzd02et/clusters/9k5ts9fxq3v5k1ef/manifests" -k | kubectl apply -f -
  323  k logs -nakuity-platform portal-server-db8755495-8z99k |
  324  k logs -nakuity-platform portal-server-db8755495-8z99k
  325  k logs -nakuity-platform portal-server-db8755495-8z99k |grep k8sinfo
  326  k logs -nakuity-platform portal-server-db8755495-8z99k |grep refresh
  327  k logs -nakuity-platform portal-server-db8755495-8z99k 
  328  k logs -nakuity-platform portal-server-db8755495-8z99k |grep error
  329  k logs -nakuity-platform portal-server-db8755495-s2dhg |grep error
  330  k logs -nakuity-platform portal-server-db8755495-s2dhg -f
  331  git add agent api aims docs internal pkg portal
  332  k logs -nakuity-platform portal-server-db8755495-pf2sx |grep refresh
  333  k logs -nakuity-platform portal-server-db8755495-pf2sx -f |grep refresh
  334  k logs -nakuity-platform portal-server-db8755495-pf2sx -f
  335  k logs -nakuity-platform portal-server-db8755495-pf2sx -f |grep error
  336  k logs -nakuity-platform portal-server-db8755495-pf2sx -f 
  337  k logs -n akuity-platform portal-server-db8755495-5zqtc 
  338  k logs -n akuity-platform portal-server-db8755495-5zqtc |grep refresh
  339  k delete po -n akuity-platform kubevision-event-cleaner-29099660-k85mq
  340  k logs -nakuity-platform portal-server-db8755495-wrsg6 |grep refresh
  341  pod
  342  cat customer.yaml
  343  rm -rf customer.yaml
  344  rm -rf mgm.yaml
  345  cd MyPro/l
  346  cd -
  347  TOKEN="eyJhbGciOiJSUzI1NiIsImtpZCI6IjQ1ZTMzNmJjMzljYmZkMWI0Yjc4MDI5ZThmNTAxN2I0MjY3ZDUwMmUifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.aInQuJpZpvPXTEG6mlMlpj2SrJZqX9HAwDXD7CjABUIQGajwmOmYAWz4qcOO7SStMajKp28wE1LtXVwmqeqYRiunmixxAp2Zm5BzN3w-3DyKRgkZGF4aDp7vWeDTKmkeSUVu6zVmz9t6VinEF7AznLu1BAQtHvyxggpCeBFxFpAH4XPJUF5ICivRkQzv7ft2oHRw8n44GMejIjPSIKqT5oZs1RhKaNjMKgnjBF82rGkHu1LEMkDOqyFrqKanWh-1bEXQzJZJctopvFv0wdnE1Vv0F-4aMDRgNRk-rpuY8cP8vKTCWUFmPcD5Nqzv_ChUukLyUH3vpZ7vbcZx0UA_DA" && curl -s -H "Authorization: Bearer $TOKEN" "https://portal-server.akuity-platform/api/v1/orgs/ka6f38y0wwzqf69j/argocd/instances/cra8dfax9791ijvr/clusters/s9rjp3x4trbufpvp/manifests" -k| kubectl apply -f -
  348  k get po -n akuity-platform portal-server-8f768fc8b-dqnxg -oyaml
  349  k get po -A akuity-agent-8545bd579-597sx
  350  k get po -n akuity akuity-agent-8545bd579-597sx
  351  k get po -n akuity akuity-agent-8545bd579-597sx -oyalm
  352  k get po -n akuity akuity-agent-8545bd579-597sx -oyaml
  353  wget https://cra8dfax9791ijvr-agentsvr.cdsvcs.portal-server.akuity-platform/k8s-resources
  354  k logs -n akuity-platform portal-server-8f768fc8b-dqnxg 
  355  k logs -n akuity-platform portal-server-8f768fc8b-dqnxg |grep -v Error
  356  k log -n akuity akuity-agent-8545bd579-zd54t
  357  k logs -n akuity akuity-agent-8545bd579-zd54t
  358  k logs -n akuity-platform portal-server-8f768fc8b-dqnxg |grep -v ERROR
  359  k logs -n akuity-platform portal-server-8f768fc8b-dqnxg |grep refresh
  360  k get po -nakuity argocd-application-controller-94945f854-jghw9 -oyaml
  361  git commit -m "keep refresh time record when update cluster status k8s info" -s
  362  git checkout refresh-time-kubevision
  363  git push --force origin refresh-time-kubevision\n
  364  k logs -n akuity-platform           portal-server-8f768fc8b-t2qln   
  365  k delete po -nakuity-platform --all
  366  k logs -n akuity-platform portal-server-8f768fc8b-f5m7c
  367  k logs -n akuity-platform portal-server-8f768fc8b-f5m7c -f
  368  k logs -n akuity-platform portal-server-8f768fc8b-f5m7c|grep refresh
  369  k logs -n akuity-platform portal-server-8f768fc8b-f5m7c -f|grep refresh
  370  git commit -m "update the refresh time when cluster info" -s
  371  vim a.json
  372  git branch -D refresh-time-kubevision
  373  which kkubectx
  374  TOKEN="******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" && curl -s -H "Authorization: Bearer $TOKEN" "https://portal-server.akuity-platform/api/v1/orgs/8zgo3bpnrvoi200r/argocd/instances/p964sy0bze9pnst0/clusters/tyymzo4ljvfeo4lj/manifests" -k| kubectl apply -f -
  375  k get po -A|grep metric
  376  history|grep add-cust
  377  CUSTOMER=customer2 ./hack/add-customer.sh customer2
  378  kubectx k3d-akuity-customer2
  379  TOKEN="******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" && curl -s -H "Authorization: Bearer $TOKEN" "https://portal-server.akuity-platform/api/v1/orgs/8zgo3bpnrvoi200r/argocd/instances/p964sy0bze9pnst0/clusters/y6hxwljoawy6un92/manifests" -k| kubectl apply -f -
  380  k get svc -n k3s
  381  k get serverstransporttcps.traefik.io
  382  k get addons.k3s.cattle.io -A
  383  k get all -n argocd-p964sy0bze9pnst0
  384  kubectl get ns -o jsonpath='{.items[*].metadata.name}' | tr ' ' '\n' | grep '^argocd-' | sed 's/^argocd-//'
  385  chmod +x add-host-entry.sh
  386  vim add-host-entry.sh
  387  cat add-host-entry.sh
  388  echo $KUBECONFIG
  389  cat refresh-agent-image.sh
  390  vim stop-akp.sh
  391  rm -rf argo-cd
  392  git clone -b akuity-master https://github.com/akuityio/argo-cd.git
  393  kubect delete po -n akuity-platform           portal-server-59677684df-5wmbq
  394  kubectl delete po -n akuity-platform           portal-server-59677684df-5wmbq
  395  brew install telnet
  396  k get cm -nAnswer questions and provide the best practices about Kubernetes, ArgoCD, and Akuity Platformz
  397  k get cm -n akuity-platform           postgres
  398  k get cm -n akuity-platform           postgres -oyaml
  399  mkdir python
  400  git clone https://github.com/openai/openai-python.git
  401  vscode
  402  code
  403  git clone https://github.com/openai/openai-go.git
  404  https://github.com/sashabaranov/go-openai.git
  405  git clone https://github.com/sashabaranov/go-openai.git
  406  git add aims api internal docs portal
  407  git commit -m "feat add AI web search function" -s
  408  git checkout -b ai-web-search
  409  git diff akuity-platform/internal/utils/ai/constant.go
  410  k exec -it portal-server-7db7c79d48-nqdss -n akuity-platform bash
  411  k exec -it portal-server-7db7c79d48-nqdss -n akuity-platform sh
  412  kubectl exec -h
  413  mkdir AI
  414  go mod init ai
  415  git diff internal/utils/ai/assistant.go
  416  git diff akuity-platform/internal/services/ai/assistant.go
  417  git diff internal/services/ai/assistant.go
  418  k logs -n akuity-platform portal-server-97d54f4bd-gbztt
  419  k get po -n akuity-platform -w
  420  cat buf.yaml
  421  git ad aims api docs internal
  422  git add models pkg portal
  423  git checkout -b AI-web-search
  424  git checkout -b add-ai-web-search
  425  git checkout ai-web-search
  426  git cherry-pick a90e521a4c9b0ef20bf870ac9958e7529f7cc038
  427  git push origin --delete ai-web-search
  428  git branch -D ai-web-search
  429  k get po -nakuity-platform -w
  430  orb -m ubuntu -u root
  431  orb
  432  cd Sk
  433  open Pictures
  434  git diff portal/ui/src/feature/ai-support-engineer/components/ai-support-engineer-drawer/ai-support-engineer-drawer.tsx
  435  git add internal models pkg portal
  436  git commit -m "set isWebSearch optional" -s
  437  ./dist/akuity-cli/v0.22.1-0.20250512022903-206f8f350448/akuity
  438  ./dist/akuity-cli/v0.22.1-0.20250512022903-206f8f350448/akuity instance
  439  ./dist/akuity-cli/v0.22.1-0.20250512022903-206f8f350448/akuity argocd instance
  440  ./dist/akuity-cli/v0.22.1-0.20250512022903-206f8f350448/akuity argocd instance list
  441  ./dist/akuity-cli/v0.22.1-0.20250512022903-206f8f350448/akuity argocd 
  442  export AKUITY_API_KEY_ID=hq6p59bt61dtbkon\nexport AKUITY_API_KEY_SECRET=yr28m76k2mgaz8kokiv1yv0bzv3kqpwd\nexport AKUITY_SERVER_URL=https://portal-server.akuity-platform\n\nakuity argocd instance list --organization-id 8zgo3bpnrvoi200r\n
  443  export AKUITY_API_KEY_ID=hq6p59bt61dtbkon\nexport AKUITY_API_KEY_SECRET=yr28m76k2mgaz8kokiv1yv0bzv3kqpwd\nexport AKUITY_SERVER_URL=http://portal-server.akuity-platform\n\nakuity argocd instance list --organization-id 8zgo3bpnrvoi200r\n
  444  export AKUITY_API_KEY_ID=ujbomfrmizchz4nj\nexport AKUITY_API_KEY_SECRET=0rn70shw258iv4zbxil89tzee3gyghlb\nexport AKUITY_SERVER_URL=http://localhost:3001\n\nakuity argocd instance list --organization-id 8zgo3bpnrvoi200r\n
  445  akuity argocd export my-instance
  446  akuity argocd export my-instance --organization-id 8zgo3bpnrvoi200r
  447  akuity argocd export my-instance --organization-id 8zgo3bpnrvoi200r > /tmp/x
  448  akuity argocd apply -f /tmp/x
  449  akuity argocd apply -f /tmp/x --organization-id 8zgo3bpnrvoi200r
  450  open /tmp/x
  451  cp /tmp/x ./
  452  git commit -m "fix portal ui test error" -s
  453  freetype-config
  454  ls CursorFreeVIP_1.11.0*
  455  rm -rf CursorFreeVIP_1.11.01_mac_arm64 CursorFreeVIP_1.11.02_mac_arm64
  456  chmod +x CursorFreeVIP_1.11.03_mac_arm64
  457  alias akuity="./dist/akuity-cli/v0.22.1-0.20250512022903-206f8f350448/akuity"
  458  akuity login
  459  akuity argocd instance
  460  akuity argocd instance -h
  461  akuity argocd instance get
  462  akuity argocd instance list --organization-id 8zgo3bpnrvoi200r
  463  export AKUITY_API_KEY_ID=hq6p59bt61dtbkon\nexport AKUITY_API_KEY_SECRET=yr28m76k2mgaz8kokiv1yv0bzv3kqpwd\nexport AKUITY_SERVER_URL=https://portal-server.akuity-platform\n\nakuity argocd instance list --organization-id 8zgo3bpnrvoi200r
  464  export AKUITY_API_KEY_ID=hq6p59bt61dtbkon\nexport AKUITY_API_KEY_SECRET=yr28m76k2mgaz8kokiv1yv0bzv3kqpwd\nexport AKUITY_SERVER_URL=https://portal-server.akuity-platform\n\nakuity argocd instance list --organization-id=8zgo3bpnrvoi200r
  465  export AKUITY_API_KEY_ID=hq6p59bt61dtbkon
  466  export AKUITY_API_KEY_SECRET=yr28m76k2mgaz8kokiv1yv0bzv3kqpwd
  467  export AKUITY_SERVER_URL=https://portal-server.akuity-platform
  468  hisotry
  469  AKUITY_SERVER_URL=http://localhost:3001 pnpm run dev
  470  env|grep proxy
  471  akuity argocd instance list --organization-id=8zgo3bpnrvoi200r
  472  akuity config -h
  473  akuity argocd export -h
  474  akuity argocd export --organization-id 8zgo3bpnrvoi200r my-instance
  475  alias akuity="/Users/<USER>/MyPro/akuity/akuity-platform/dist/akuity-cli/v0.22.1-0.20250512022903-206f8f350448/akuity"
  476  akuity argocd --organization-id 8zgo3bpnrvoi200r -h
  477  akuity argocd -h
  478  akuity argocd apply -h
  479  vim .gitignore
  480  cat .vscode/settings.json
  481  vim ~/.cursor/argv.json
  482  ls ~/.cursor
  483  ls ~/.cursor -a
  484  ls -a ~/.cursor 
  485  cd ~/.cursor
  486  cat mcp.json
  487  cd extensions
  488  vim .vscode/settings.json
  489  open ~/.vscode
  490  oc
  491  kubectx k3d-akuity-customer
  492  k get po -A|grep argocd-redis-55b9994bc-rwqf
  493  k get po -A|grep argocd-redis-55b9994bc-rwqfs
  494  k logs -nakuity argocd-redis-55b9994bc-rwqfs
  495  k logs -n akuity akuity-agent-6df59478c9-6z6rm
  496  k get po -A |grep p964sy0bze9pnst0
  497  brew install fzf
  498  k logs -n akuity akuity-agent-59b8c675dc-hnx8d
  499  k delete po -nakuity akuity-agent-59b8c675dc-hnx8d
  500  k logs -n akuity akuity-agent-59b8c675dc-7sqjk
  501  k delete po -n akuity akuity-agent-59b8c675dc-7sqjk
  502  k logs -nakuity akuity-agent-59b8c675dc-848vh
  503  kubectx -h
  504  history|grep context|grep delete
  505  history|grep context
  506  k3s
  507  k3d cluster
  508  k3d 
  509  k3d config
  510  k3d cluster 
  511  kubect get nodes
  512  kc
  513  k config delete-context k3d-akuity-customer2
  514  k config context
  515  sh add-host-entry.sh
  516  TOKEN="eyJhbGciOiJSUzI1NiIsImtpZCI6ImVkN2Y1N2I4OGY1MTk4ZTA5ZTAzZTZjODVjMmFjMmY1YjgyMjYzMTEifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.SHeL-D_gS4E86eIt3HtwLYfAD0gywDh7aBM6j5ZWlRY9xm-P3WgbdS9_oNLkAQNpaPrSIR3EU962VLgBWaq7IO6A-YYlOXeCej9sntgbHLyilgDe8Ao66v2IKP2P4n6wF3MXhbzTPhW5eVhc2wlSKdgxf65Y8rAlqaBPogL-3aF25PaziBoVrHD9BOiXwIE-l8i9Z6Uf6zXmmYg5k_xA8USBsQwyRIZTYASZ4I9UaBaJv8mqbjE14ACni7Om8jKI6fhRMhWLbU8ixv8L_-hJl1UFH0uHW79FM1_hCWYHF2kv5ATbxtnRvg6DrBvXiTtgqMPf2Q4MFn30ZifTmEpkpw" && curl -s -H "Authorization: Bearer $TOKEN" "https://portal-server.akuity-platform/api/v1/orgs/mygcbvoxgoujd8j7/argocd/instances/28ehsgjn7gcg0qf9/clusters/qyhwpaun7tsvawrd/manifests" -k | kubectl apply -f -
  517  echo "************************************************************************************************************************************************************************************************" |base64 -d
  518  kubect get po -A
  519  k logs -n akuity-platform portal-server-557c656465-sct8b
  520  k logs -n akuity-platform portal-server-557c656465-h9v4f
  521  k logs -nakuity-platform portal-server-557c656465-xzxld -f
  522  k get po -A |grep qtcw2
  523  k logs -n akuity argocd-redis-59486b8d8d-qtcw2
  524  mkdir dist
  525  mv ../../akuity-platform/dist/akuity-cli/v0.22.1-0.20250512022903-206f8f350448/akuity /usr/bin
  526  sudo mv ../../akuity-platform/dist/akuity-cli/v0.22.1-0.20250512022903-206f8f350448/akuity /usr/bin
  527  sudo mv ../../akuity-platform/dist/akuity-cli/v0.22.1-0.20250512022903-206f8f350448/akuity /usr/local/bin
  528  akuity
  529  export AKUITY_API_KEY_ID=m11ztnn6fbfs7n3i\nexport AKUITY_API_KEY_SECRET=fys9kmi7ez27tyms7369peygkij4t9e2\nexport AKUITY_SERVER_URL=https://akuity.cloud\n\nakuity argocd instance list --organization-id 7nfpt3kqvd0vclx0\n
  530  terraform 
  531  cd /Users/<USER>/MyPro/akuity/terraform-provider-akp/dist
  532  export AKUITY_API_KEY_ID=m11ztnn6fbfs7n3i\nexport AKUITY_API_KEY_SECRET=fys9kmi7ez27tyms7369peygkij4t9e2\nexport AKUITY_SERVER_URL=https://akuity.cloud
  533  terraform refresh -h
  534  df -h ./
  535  akuity argocd export --organization-id 8zgo3bpnrvoi200r my-instance > a.yaml
  536  cat ~/x
  537  cat x
  538  mv x a.yaml
  539  git clone https://github.com/FoundationAgents/OpenManus.git
  540  git clone https://github.com/FoundationAgents/MetaGPT.git
  541  mkdir AI 
  542  mv MetaGPT AI
  543  mv OpenManus AI
  544  conda create -n open_manus python=3.12
  545  brew install conda
  546  conda create -n open_manus python=3.12\nconda activate open_manus
  547  anaconda  create -n open_manus python=3.12
  548  cd MyPro/AI
  549  playwright install
  550  cd AI/OpenManus
  551  cp config/config.example.toml config/config.toml
  552  echo '************************************************************************************************************************************************************************************************'|base64 -d
  553  vim config/config.toml
  554  No module named 'pydantic
  555  sudo pip3 install pydantic
  556  python3 -m venv path/to/venv\n    source path/to/venv/bin/activate\n    python3 -m pip install pydantic
  557  python3 -m venv path/to/venv\n    source path/to/venv/bin/activate\n    python3 -m pip install tiktoken
  558  python3 -m venv path/to/venv\n    source path/to/venv/bin/activate\n    python3 -m pip install openai
  559  python3 -m venv path/to/venv\n    source path/to/venv/bin/activate\n    python3 -m pip install tenaciy
  560  python3 -m venv path/to/venv\n    source path/to/venv/bin/activate\n    python3 -m pip install tenacity
  561  python3 -m venv path/to/venv\n    source path/to/venv/bin/activate\n    python3 -m pip install boto3
  562  python3 -m venv path/to/venv\n    source path/to/venv/bin/activate\n    python3 -m pip install tomllib
  563  /Users/<USER>/MyPro/AI/OpenManus/path/to/venv/bin/python3 -m pip install --upgrade pip
  564  /Users/<USER>/MyPro/AI/OpenManus/path/to/venv/bin/python3 -m pip install toml
  565  /Users/<USER>/MyPro/AI/OpenManus/path/to/venv/bin/python3 -m pip install tomllib
  566  python3 main.py
  567  python -v
  568  brew install python@3.12
  569  python3 -m venv path/to/venv\n    source path/to/venv/bin/activate\n    python3 -m pip install toml
  570  python3 -m venv path/to/venv\n    source path/to/venv/bin/activate
  571  curl -LsSf https://astral.sh/uv/install.sh | sh
  572  uvx pip install -r requirements.txt
  573  uv venv --python 3.12\nsource .venv/bin/activate  # Unix/macOS 系统\n# Windows 系统使用：\n# .venv\Scripts\activate
  574  source .venv/bin/activate
  575  uv pip install -r requirements.txt
  576  python main.py
  577  cd workspace
  578  cat example.txt
  579  open index.html
  580  cat script.js
  581  cd /Users/<USER>/MyPro/akuity/terraform-provider-akp && git log -n 2 --oneline | cat
  582  cd terraform-provider-akp && git log -2 --oneline
  583  cd terraform-provider-akp && git show 4490167
  584  cd terraform-provider-akp && git log -n 2 --oneline | cat
  585  git add akp docs
  586  git commit -m "feat: support applying argo resources in argo _instance" -s
  587  git checkout -b argo-resources
  588  git cherry-pick 60d9b45d2f383eca8d1e18fec8531ac1f0269e07
  589  gom mod tidy
  590  git add apk go.mod
  591  git add go.mod
  592  git checkout kargo-resources
  593  ls -lhst
  594  echo $GO_PATH
  595  env|grep GO
  596  go env |grep GO
  597  git remove -v
  598  cd MyPro/akuity
  599  git remote set-url origin https://github.com/akuity/terraform-provider-akp.git\n
  600  git checkout argo-resources 
  601  git push --set-upstream origin argo-resources
  602  git remote set-url own https://github.com/qiuming-best/terraform-provider-akp.git
  603  git remote add own https://github.com/qiuming-best/terraform-provider-akp.git
  604  git push --set-upstream own argo-resources
  605  akuity argocd instance list
  606  history |grep --organization-id
  607  akuity argocd export --organization-id 8zgo3bpnrvoi200r
  608  history|grep mygcbvoxgoujd8j7
  609  history |grep organization-id
  610  akuity argocd export --organization-id mygcbvoxgoujd8j7
  611  akuity argocd instance list --organization-id=mygcbvoxgoujd8j7
  612  export AKUITY_API_KEY_ID=kydhirwd6f5v1803\nexport AKUITY_API_KEY_SECRET=6fnrix3aqfb3bq44e9lbks405jgmf8i4\nexport AKUITY_SERVER_URL=https://portal-server.akuity-platform\n\nakuity argocd instance list --organization-id mygcbvoxgoujd8j7\n
  613  akuity argocd export --organization-id mygcbvoxgoujd8j7 --insecure-skip-tls-verify my-instance > argo-cd.yaml
  614  cat argo-cd.yaml
  615  history |grep terraform 
  616  terraform plan -h
  617  sudo /usr/local/vanta/vanta-cli show
  618  akuity argocd instance list --organization-id mygcbvoxgoujd8j7 --insecure-skip-tls-verify
  619  export AKUITY_SERVER_URL=http://localhost:3001
  620  history|grep AKUITY_SERVER_URL
  621  export AKUITY_API_KEY_ID=1xvhbl2jmat11b36\nexport AKUITY_API_KEY_SECRET=bn0ei15rdwfa2okx02yjkrv4122ltuiy\nexport AKUITY_SERVER_URL=http://localhost:3001\n\nakuity argocd instance list --organization-id mygcbvoxgoujd8j7\n
  622  vim test.tf
  623  git add .
  624  cd terraform-provider-akp/examples/resources/akp_instance/resource.tf
  625  cd terraform-provider-akp/examples/resources/akp_instance/
  626  cd examples/resources/akp_instance
  627  rm -rf akp-manifests
  628  touch argo.yaml
  629  ls examples/resources/akp_instance/resource.tf
  630  ls terraform-provider-akp/examples/resources/akp_instance/resource.tf
  631  ls resource.tf
  632  cd  examples/resources/akp_instance/argo-manifests/
  633  cd /Users/<USER>/MyPro/akuity/terraform-provider-akp/examples/resources/akp_cluster/basic.tf
  634  cd examples/resources/
  635  cd akp_cluster
  636  ls argo_manifests
  637  rm -rf argo_manifests
  638  mv tmp/argo.yaml argo_manifests
  639  mv argo_manifests argo-manifests
  640  akuity argocd instance list --organization-id mygcbvoxgoujd8j7
  641  source  ~/.terraformrc
  642  terraform providers
  643  mv tmp /Users/<USER>/MyPro/akuity/terraform-provider-akp/dist
  644  cd /Users/<USER>/MyPro/akuity/terraform-provider-akp/dist 
  645  ls -a
  646  cd .terraform
  647  cd providers
  648  cd registry.terraform.io
  649  cat test.tf
  650  k get po -n argocd-et0lsyuyqlobxqj1 -w
  651  k get po -n argocd-et0lsyuyqlobxqj1 
  652  k get po -A |grep argocd-dex-server
  653  k get po -A -
  654  kubect delete po -n akuity-platform --all
  655  k get po -A |wc -l
  656  kubect delete -n akuity-platform kubevision-event-cleaner-29120940-jl59n kubevision-event-cleaner-29120940-psbqn kubevision-event-cleaner-29120960-v4fnn kubevision-event-cleaner-29121020-tn7sf 
  657  k delete job -n akuity-platform kubevision-event-cleaner-29121500
  658  kubect delete po -n akuity-platform --all --force --grace-periods 0
  659  kubectl delete po -n akuity-platform --all --force --grace-periods 0
  660  kubectl delete po -n akuity-platform --all --force --grace-period 0
  661  kubectl delete po -n akuity-platform kubevision-event-cleaner-29121520-trvrj --force --grace-period 0
  662  terraform destroy -h
  663  k get po -n argocd-et0lsyuyqlobxqj1
  664  k get po -n argocd-pppm15j9a7f7u6fi -w
  665  terraform destroy
  666  cd tm
  667  mv cluster.tf ./
  668  mv deploy/cluster.tf ./
  669  rm -rf .terraform
  670  k get po -n argocd-pppm15j9a7f7u6fi
  671  k delete po -n argocd-44bz760a34yznw7w --all
  672  k get po -n argocd-44bz760a34yznw7w -w
  673  k get po -n argocd-44bz760a34yznw7w 
  674  ls -lsth /Users/<USER>/go/bin/terraform-provider-akp
  675  sl
  676  history|grep build
  677  go env|grep GO
  678  go env|grep GOR
  679  go env|grep GOPATH
  680  ls -lsth terraform-provider-akp
  681  history |grep terraform-provider-akp
  682  cd /Users/<USER>/MyPro/akuity/terraform-provider-akp/dist/deploy
  683  mv terraform-provider-akp /Users/<USER>/go/bin/
  684  TF_LOG=debug terraform apply
  685  git diff  akp/resource_akp_instance.go
  686  k get po -A -n argocd-44bz760a34yznw7w
  687  k get po -n argocd-44bz760a34yznw7w
  688  TF_LOG=info terraform apply
  689  k get po -n argocd-7mf5q5ylu4npfebx -w
  690  k get po -n argocd-7mf5q5ylu4npfebx
  691  # Remove the resource from state and recreate\nterraform state rm akp_instance.example\nterraform apply
  692  TOKEN="eyJhbGciOiJSUzI1NiIsImtpZCI6IjFiN2FlNTU4ZmNiZjlkNzc3MzQ0NmFlMzZhMzYyZjhiZTVjMWMwY2MifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.We04ncgfLXuF39obUQB5FKMCbRLf8bQ6LdypZg-YFgS5ryGv3JTcDEwcPpCRkFQQSKBb4i2xVAoWDZXk087Afs8cnbnMrN0OTgVgx06S60WtSAIXQ1ct3BaIemk7K7eh6QlYItiIGsEYju0nb339zSGYU6vDDBEhv_a00cnQQGsSkKV6giAevtw4bkTc4ELFUsM7cV48bw-EkuTTuQERkQvlju4hY651vfGq48Mhy_6sEo6LwU4IOCnRaYgtKzolt0xu6Z-ThUSUX-peTr7vR6MjzKe6xAbwCXOGFYMKFDUj2A35TImjWa_zvGXyy_ifp4R5eig0PlEqvV8hJiCa8w" && curl -s -H "Authorization: Bearer $TOKEN" "http://localhost:3001/api/v1/orgs/mygcbvoxgoujd8j7/argocd/instances/h7spgi565uppcr70/clusters/2g2xdtu5lesya8va/manifests" -k | kubectl apply -f -
  693  k ge po -A
  694  k delete po -n akuity-platform addon-controller-677467744f-fkljd instances-upgrader-jbzpc instances-upgrader-v62fx platform-controller-5965d7bc97-pdcsp portal-server-5c9c79cc6-m2wzw portaldb-update-2jt6c portaldb-update-4v2fl portaldb-update-tpcj9 
  695  k3d cluster delete k3d-akuity-customer1
  696  terraform state rm akp_instance.example
  697  TOKEN="******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" && curl -s -H "Authorization: Bearer $TOKEN" "http://localhost:3001/api/v1/orgs/kqo3l9txlqhtoxco/argocd/instances/5847hn3pn0u5fzvx/clusters/8rlrib56bl25bbt3/manifests" -k | kubectl apply -f -
  698  k delete po -n akuity argocd-notifications-controller-548545b886-pgm8m
  699  k delete po -n akuity argocd-repo-server-7bf6654d45-vsn72
  700  vim cm.yaml
  701  kubectl create -f cm.yaml
  702  k describe po -n akuity argocd-notifications-controller-548545b886-n44gj
  703  k delete po n akuity argocd-notifications-controller-548545b886-n44gj
  704  k describe po -n akuity argocd-repo-server-7bf6654d45-hrqnn
  705  rm -rf cm.yaml
  706  cat basic.tf |grep   argocd_ssh_known_hosts_cm = {\n
  707  cat basic.tf |grep   argocd_ssh_known_hosts_cm \n
  708  TOKEN="******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" && curl -s -H "Authorization: Bearer $TOKEN" "http://localhost:3001/api/v1/orgs/kqo3l9txlqhtoxco/argocd/instances/5847hn3pn0u5fzvx/clusters/8rlrib56bl25bbt3/manifests" | kubectl apply -f -
  709  k describe po -n akuity        argocd-repo-server-7bf6654d45-sj5rr
  710  CUSTOMER=customer5 ./hack/add-customer.sh customer5
  711  TOKEN="******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" && curl -s -H "Authorization: Bearer $TOKEN" "http://localhost:3001/api/v1/orgs/r4hmbhiuc0vnpbfp/argocd/instances/xe4iwi1x2k9s7py0/clusters/8j4gqhfqvctb0w1k/manifests" | kubectl apply -f -
  712  k describe po -n akuity argocd-repo-server-8fd7db98b-5fbkh
  713  k describe po -n akuity app2-helm-guestbook-fcb795d8c-m7dsw
  714  k describe po app2-helm-guestbook-fcb795d8c-m7dsw
  715  git checkout -b add-ai-web-search-tmp
  716  docker pull library/postgres
  717  history|grep make |grep contain
  718  ping mirror.gcr.io
  719  docker pull postgres:15.7
  720  git add amis
  721  git add aims api docs internal
  722  git add models pkg portal 
  723  git add go.mod go.sum
  724  rm -rf akuity-platform/internal/services/ai/assistant.go
  725  git commit -m "change the way of passing `s web search` option" -s
  726  git fetch origan
  727  rm -rf argo-cd.yaml
  728  rm -rf argo_cd.yaml
  729  git diff docs/resources/instance.md
  730  k3d cluster delete k3d-akuity-custome2
  731  k3d cluster delete k3d-akuity-custome1
  732  k3d cluster delete k3d-akuity-custome5
  733  kubectl config delete-context k3d-akuity-custome5
  734  kubectl config delete-context k3d-akuity-customer5
  735  ./start-akp.sh 3
  736  gofumpt -w -extra /Users/<USER>/MyPro/akuity/akuity-platform/internal/services/ai/service.go
  737  ls $HOME/go/bin/gofumpt
  738  /Users/<USER>/go/bin/gofumpt -w -extra /Users/<USER>/MyPro/akuity/akuity-platform/internal/services/ai/service.go
  739  ./add
  740  ./add-host-entry.sh
  741  kubectl describe po -n argocd-ka9e61mln2kryh7z argocd-repo-server-df9f7d46d-w8jzj
  742  k describe po -n akuity-platform kubevision-event-cleaner-29122920-5vcmt
  743  TOKEN="******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" && curl -s -H "Authorization: Bearer $TOKEN" "https://portal-server.akuity-platform/api/v1/orgs/xf10bttohxxsop7n/argocd/instances/ka9e61mln2kryh7z/clusters/oeol3nzpl2ygrm04/manifests" | kubectl apply -f -
  744  TOKEN="******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" && curl -s -H "Authorization: Bearer $TOKEN" "https://portal-server.akuity-platform/api/v1/orgs/xf10bttohxxsop7n/argocd/instances/ka9e61mln2kryh7z/clusters/oeol3nzpl2ygrm04/manifests" -k | kubectl apply -f -
  745  kubectx 
  746  k delete po -n akuity-platform portal-server-c594dccd8-sphxh
  747  k logs -n akuity-platform           portal-server-c594dccd8-s2v6z
  748  git add internal/services/ai/service.go
  749  git checkout portal/ui/src/feature/ai-support-engineer/components/ai-support-engineer-drawer/ai-support-engineer-drawer.tsx
  750  cd util
  751  k logs -n akuity-platform portal-server-669b56d74-fpvtd
  752  k logs -n akuity-platform portal-server-669b56d74-fpvtd |grep error
  753  k logs -n akuity-platform agent-server-5f7cf5f848-p6s6d 
  754  k logs -n akuity-platform platform-controller-6844775f4c-cldsm
  755  k logs -n akuity-platform platform-controller-6844775f4c-cldsm -f
  756  k logs -n akuity-platform postgres-7dc7d7d699-frxvj -f
  757  k logs -n akuity-platform portal-server-679658d6f5-c589g
  758  ./ai
  759  k logs -n akuity-platform postgres-7dc7d7d699-frxvj
  760  git add go.mod go.sum internal
  761  git commit -m "do not use tools when using web search" -s
  762  git commit -m "remove unsed functions" -s
  763  git diff examples/resources/akp_kargo_instance/resource.tf
  764  git checkout examples/resources/akp_kargo_instance/resource.tf
  765  rm -rf  examples/resources/akp_instance/argo.yaml
  766  git checkout examples/resources/akp_instance/resource.tf
  767  git diff docs
  768  git add examples
  769  git commit -m "add argocd terraform files config" -s
  770  mkdir -p akp/utils
  771  git commit -m "reuse some functions" -s
  772  git checkout -b tmp
  773  git pull origin argo-resources
  774  git pull own argo-resources
  775  git pull --rebase own argo-resources
  776  git rebase --abort
  777  git push -f own argo-resources
  778  git show
  779  git log -1 -p | cat
  780  git commit -m "adjust some functions" -s
  781  git push own
  782  git remote -h
  783  git remote remove origin
  784  go build
  785  k get po -n argocd-23kbx538gdu2auax
  786  k get po -n argocd-23kbx538gdu2auax -w
  787  open argo.yaml
  788  rm -rf ../resource.tf
  789  git add docs examples
  790  git commit -m "update documents" -s
  791  git commit -m "fix syncResources" -s
  792  git checkout akp
  793  Downloads
  794  ls /Applications/CursorPro.app/Contents/MacOS/
  795  ls /Applications/
  796  mv ~/Downloads/Mac版本/CursorPro.app /Applications
  797  mkdir PTE
  798  /Users/<USER>/Downloads/●\ a\ wide\ choice\ of\ options_●\ working\ understanding.md 
  799  npx create-next-app@latest . --typescript --tailwind --eslint --app --src-dir --import-alias "@/*"
  800  npx create-next-app@latest pte-app --typescript --tailwind --eslint --app --src-dir --import-alias "@/*"
  801  cd pte-app && npm install framer-motion @heroicons/react
  802  mkdir -p pte-app/public && cp sentences.md pte-app/public/
  803  cd pte-app && npm install
  804  cd pte-app && npm install framer-motion
  805  cd pte-app && npm install next@latest
  806  npm install next@latest
  807  cd pte-app && npm install && npm run dev
  808  cd pte-app && rm -rf node_modules .next && npm install && npm run dev
  809  npm install && npm run dev
  810  pte-app
  811  cd pte-app && npm run dev
  812  ipconfig
  813  cd MyPro/PTE
  814  git clone https://github.com/qiuming-best/words-memorize.git
  815  npn run
  816  nmp run dev
  817  npm run
  818  num start
  819  npm run start
  820  mongod --version
  821  brew tap mongodb/brew && brew install mongodb-community
  822  mongosh
  823  mongosh --eval 'use words; db.createUser({user: "jump-tiger", pwd: "123", roles: [{role: "readWrite", db: "words"}]})'
  824  brew services restart mongodb/brew/mongodb-community
  825  cd src/backend && npm install
  826  echo 'security:\n  authorization: enabled' | sudo tee /usr/local/etc/mongod.conf
  827  mongosh admin --eval 'db.createUser({user: "jump-tiger", pwd: "123", roles: [{role: "readWrite", db: "words"}]})'
  828  kill 25767
  829  brew services stop mongodb/brew/mongodb-community
  830  cd ../.. && npm start
  831  lsof -i :3001 | grep LISTEN
  832  kill 27485
  833  mongosh words --eval 'db.words.find()'
  834  curl -X POST -H "Content-Type: application/json" -d '{"library_name":"test","words":["hello","world"]}' http://localhost:3001/api/importWords
  835  curl http://localhost:3001/api/getLibraries
  836  git diff controller server.js
  837  rm -rf ../../README.md
  838  git diff *.js
  839  vim words.txt
  840  cd src/backend && npm start
  841  cd backend
  842  cd src/backend && npm install mongoose@latest
  843  gitstatus
  844  git add README.md
  845  git commit -m "Optimise performance and adjust color" -s
  846  cd mkdir PET
  847  mkdir PET
  848  mkdir -p data/original_cat data/found_cats src/models src/preprocessing src/feature_extraction src/matching src/utils
  849  mkdir -p src/models/advanced src/preprocessing/advanced src/feature_extraction/advanced src/matching/advanced src/utils/advanced
  850  python3 -m venv venv && source venv/bin/activate
  851  pip install --upgrade pip
  852  pip install numpy opencv-python pillow scikit-learn matplotlib tqdm albumentations tensorboard pandas seaborn scipy timm torchmetrics
  853  pip3 install torch torchvision --index-url https://download.pytorch.org/whl/cpu
  854  source venv/bin/activate && pip install numpy opencv-python pillow scikit-learn matplotlib tqdm albumentations tensorboard pandas seaborn scipy timm torchmetrics
  855  source venv/bin/activate && python3 -c "import torch; import torchvision; import cv2; import numpy; print('PyTorch version:', torch.__version__); print('CUDA available:', torch.backends.mps.is_available())"
  856  source venv/bin/activate && python3 -c "import torch; import torchvision; import cv2; import numpy; print('PyTorch version:', torch.__version__); print('MPS available:', torch.backends.mps.is_available())"
  857  source venv/bin/activate && python3 -c "import torchvision; import cv2; import numpy; import timm; print('torchvision version:', torchvision.__version__); print('OpenCV version:', cv2.__version__); print('NumPy version:', numpy.__version__); print('timm version:', timm.__version__)"
  858  source venv/bin/activate
  859  touch src/models/__init__.py src/models/advanced/__init__.py src/feature_extraction/__init__.py src/feature_extraction/advanced/__init__.py src/matching/__init__.py src/matching/advanced/__init__.py src/utils/__init__.py src/utils/advanced/__init__.py src/preprocessing/__init__.py src/preprocessing/advanced/__init__.py
  860  mkdir -p data/original_cat data/found_cats output
  861  python src/main.py
  862  source venv/bin/activate && python -m src.main
  863  source venv/bin/activate && python -m src.main --reference_dir data/original_cat --query_dir data/found_cats --output_dir output
  864  pip install scikit-image
  865  source venv/bin/activate && pip install scikit-image
  866  cp -rf found/IMG_6145.jpeg found/IMG_6373.jpeg found/IMG_6571.jpeg found_cats
  867  mkdir original 
  868  mv original_cat/IMG_* original
  869  mv original_cat/sd166* original
  870  ls original_cat
  871  mv original/* original_cat
  872  rm -rf original
  873  ls cache/features
  874  cd cache
  875  cd features
  876  mv found_cats found
  877  mkdir found_cats
  878  cd found
  879  mv download.jpg images.jpg ../found_cats
  880  sed -i '' 's/^    query_path/query_path/' src/main.py
  881  mv ./* ../found_cats
  882  rm -rf found
  883  source venv/bin/activate && PYTHONPATH=. python -m src.main
  884  python3 -c "import re; f=open('src/main.py', 'r'); content=f.read(); f.close(); content=re.sub(r'^    query_path', 'query_path', content, flags=re.MULTILINE); f=open('src/main.py', 'w'); f.write(content); f.close()"
  885  PYTHONPATH=. python -m src.main
  886  open  Truman 2025-05-05 14.37.10.mp4
  887  rm -rf cache/features/*
  888  rm -rf cache/features
  889  source venv/bin/activate && PYTHONPATH=. python -m src.main --reference_dir data/original_cat --query_dir data/found_cats --output_dir output
  890  PYTHONPATH=. python -m src.main --reference_dir data/original_cat --query_dir data/found_cats --output_dir output
  891  k3d cluster delete k3d-akuity-customer-3
  892  k3d cluster delete k3d-akuity-customer
  893  k3d cluster delete k3d-akuity-customer-2
  894  rm -rf kubeconfig.akuity-customer*
  895  dc kubevision-scripts
  896  TOKEN="eyJhbGciOiJSUzI1NiIsImtpZCI6IjIyZTA4NTE4ZWM0ZmY0ODcxNzQ0MjgyOTM3ZWJkM2JhNWIyYTY4ODMifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ADYzB0oHHQgTs5VgqdjEcer0U_gjAdq9e6zc1Pfc4k0zRv0KOHHHRFRiisxlHX9xB14ku9b1Q2cgsmI_T7Qb-4O6-7NLv8QOa3e1Ef0iwvIHxF4z7ldyv7TISXC_7XUi4iRCoVMJ-SWpV3VWRcJJSAWZ8R1948_Fri857skg4BIRvP72WXmFRXuyQhom5spNNE9GdVhKZxNEeChQGPG3QokmOidXwHL0cOEYFmKrBxES17GlA7rpwkY-PvCV8dO-GrJAYHnrSBsE9EbJlCTePkZ_h5tDhrJea9_HbJUXhpe2Ujh_emugpXOy1CN3DGFw3NtEabRTlPvAHEuBRBvABQ" && curl -s -H "Authorization: Bearer $TOKEN" "http://localhost:3001/api/v1/orgs/ecmjxxdfcdfu20uh/argocd/instances/23kbx538gdu2auax/clusters/5sblm0i9ej8qb1gs/manifests" | kubectl apply -f -
  897  cd cd ..
  898  vim argo.yaml
  899  git diff ../../basic.tf
  900  git add akp_instance/argo-manifests/argo.yaml
  901  git commit -m "not install all applications in example for application set" -s
  902  vim examples/resources/akp_kargo_instance/basic.tf
  903  vim examples/resources/akp_kargo_instance/kargo-manifests/kargo.yaml
  904  open examples/resources/akp_kargo_instance/kargo-manifests/
  905  k logs -n akuity-platform portal-server-78f6588867-mbjqj
  906  k logs -n akuity-platform portal-server-78f6588867-mbjqj -f
  907  cd /Users/<USER>/MyPro/akuity/terraform-provider-akp/examples/resources/akp_kargo_instance
  908  git pull own main
  909  cd /Users/<USER>/MyPro/akuity/terraform-provider-akp
  910  git checkout argo-resources
  911  git stuat
  912  kubectl describe po -n k3s-6c648d4db-4m7tt
  913  kubect describe po -n kargo-j6v4cw539so73rt8 k3s-6c648d4db-4m7tt
  914  kubectl describe po -n kargo-j6v4cw539so73rt8 k3s-6c648d4db-4m7tt
  915  kubectl delete ns kargo-j6v4cw539so73rt8
  916  kubect get ns
  917  k get po -n kargo-j6v4cw539so73rt8
  918  git checkout  internal/portalapi/kargo/apply_kargo_instance_v1.go
  919  git commit -m "change the way of passing web search value" -s
  920  git commit -m "change interface into generics" -s
  921  git add apk
  922  git commit -m "modify build argocd kargocd build function" -s
  923  ping registry.docker.io
  924  git diff internal/services/ai/doc_search_prompt.md
  925  git diff internal/*.md
  926  git diff internal/*/.*.md
  927  git diff internal/*/*.*.md
  928  git diff internal/*.*.md
  929  git diff internal/services/ai/
  930  git diff internal/services/ai/web_search_prompt.md
  931  git commit -m "change is_web_search to is_doc_search" -s
  932  /usr/bin/python3 /Users/<USER>/.cursor/extensions/ms-python.python-2024.12.3-darwin-arm64/python_files/printEnvVariablesToFile.py /Users/<USER>/.cursor/extensions/ms-python.python-2024.12.3-darwin-arm64/python_files/deactivate/zsh/envVars.txt
  933  cd pte-app
  934  cd /Users/<USER>/MyPro/PTE/pte-app && npm run dev
  935  ip 
  936  kubectl delete po -n akuity-platform portal-server-78f6588867-mbjqj
  937  k logs -n akuity-platform portal-server-78f6588867-xcqps
  938  k logs -n akuity-platform portal-server-78f6588867-xcqps -f
  939  k delete po -n akuity-platform portal-server-78f6588867-xcqps 
  940  k get po -n akuity-platform|grep por
  941  k logs -n akuity-platform portal-server-78f6588867-rlfbv
  942  k logs -n akuity-platform portal-server-78f6588867-rlfbv -f 
  943  k logs -n akuity-platform portal-server-78f6588867-rlfbv |grep Doc
  944  k logs -n akuity-platform portal-server-78f6588867-rlfbv |grep error 
  945  k logs -n akuity-platform platform-controller-666d5d8448-rkqnv
  946  k logs -n akuity-platform platform-controller-666d5d8448-rkqnv|grep error
  947  k logs -n akuity-platform platform-controller-666d5d8448-rkqnv|grep "OpenAI tokens limit"
  948  k logs -n akuity-platform platform-controller-666d5d8448-rkqnv -f|grep "OpenAI tokens limit"
  949  k get edit -n akuity-platform
  950  k get edit -n akuity-platform portal-server
  951  k edit deploy -n akuity-platform portal-server
  952  kubectl set env deployment/portal-server AI_SERVICE_IS_DOC_SEARCH=true
  953  k logs -n akuity-platform portal-server-5b8bd6776-rwkjf 
  954  k logs -n akuity-platform portal-server-5b8bd6776-rwkjf -f 
  955  k logs -n akuity-platform portal-server-5b8bd6776-rwkjf -f |grep Doc
  956  k logs -n akuity-platform portal-server-5b8bd6776-rwkjf |grep Doc
  957  k get deploy -n akuity-platform portal-server -oyaml
  958  k logs -n akuity-platform portal-server-5b8bd6776-rwkjf |grep isDocSearch
  959  kubectl get pod -n akuity-platform --no-headers -o custom-columns=":metadata.name" | grep ^portal-server\n
  960  kubectl delete pod -n akuity-platform $(kubectl get pod -n akuity-platform --no-headers -o custom-columns=":metadata.name" | grep ^portal-server)\n
  961  kubectl logs -n akuity-platform $(kubectl get pod -n akuity-platform --no-headers -o custom-columns=":metadata.name" | grep ^portal-server)\n
  962  vim portal-log.sh
  963  sh portal-log.sh |grep isDoc
  964  vim restart-portal.sh
  965  sh restart-portal.sh
  966  sh portal-log.sh|grep isDoc
  967  sh portal-log.sh|grep isDocSearch
  968  k logs -n akuity-platform platform-controller-666d5d8448-rkqnv 
  969  k logs -n akuity-platform platform-controller-666d5d8448-rkqnv |grep isDocSearch
  970  k logs -n akuity-platform postgres-7dc7d7d699-frxvj |grep isDocSearch
  971  k logs -n portal-server-b8d6c78ff-6xztl |grep isDocSearch
  972  mkdir 1
  973  mv akuity-platform 1
  974  mv tmp/akuity-platform ./
  975  rm -rf tmp
  976  npm install
  977  npnm build
  978  hisotry|grep pnpm
  979  pnpm build
  980  pnpm install
  981  mv 1/akuity-platform ./
  982  rm -rf 1
  983  mv internal/portalapi/organization/create_ai_message_v1.go ../
  984  mv internal/services/ai/service.go ../
  985  log
  986  git diff akp
  987  k get po -A |grep agent
  988  git merge origin
  989  kk
  990  k logs -n akuity-platform portal-server-5c76445957-jmmd9
  991  k logs -n akuity-platform portal-server-5c76445957-jmmd9 |grep isDoc
  992  k logs -n akuity-platform portal-server-5c76445957-jmmd9 
  993  kubectl logs -n akuity-platform portal-server-5c76445957-jmmd9 
  994  k logs -n akuity-platform portal-server-7d47666796-j2n96  -f
  995  k logs -n akuity-platform portal-server-7d47666796-j2n96  |grep isDoc
  996  k logs -n akuity-platform portal-server-7d47666796-j2n96 
  997  k logs -n akuity-platform portal-server-7d47666796-j2n96 |grep isDocSearch
  998  k logs -n akuity-platform portal-server-5c76445957-4vlpr 
  999  k logs -n akuity-platform portal-server-5c76445957-4vlpr  -f
 1000  k logs -n akuity-platform portal-server-b8d6c78ff-6xztl |grep isDocSearch
 1001  k get po -n akuity-platform portal-server-5c76445957-4vlpr |grep doc_search
 1002  k get po -n akuity-platform portal-server-5c76445957-4vlpr -oyaml|grep doc_search
 1003  k get po -n akuity-platform portal-server-5c76445957-4vlpr -oyaml|grep doc
 1004  k get po -n akuity-platform portal-server-5c76445957-4vlpr -oyaml
 1005  k logs -n akuity-platform portal-server-74d6bf8b84-7w2wj -f 
 1006  kubectl -n akuity-platform set env deployment/portal-server AI_SERVICE_IS_DOC_SEARCH=false
 1007  k logs -n akuity-platform portal-server-f9cd65f95-982h9
 1008  k logs -n akuity-platform portal-server-f9cd65f95-982h9 -f
 1009  kubectl -n akuity-platform set env deployment/portal-server AI_SERVICE_IS_DOC_SEARCH=true
 1010  k logs -n akuity-platform portal-server-74d6bf8b84-mlqck -f
 1011  k logs -n akuity-platform platform-controller-54656b76c4-hcpvx -f
 1012  k logs -n akuity-platform portal-server-865cb788fd-tjm26 -f 
 1013  k get -n akuity-platform po
 1014  k logs -n akuity-platform portal-server-669f65c9cc-dglhk -f 
 1015  k get po -n akuity-platform i
 1016  k logs -n akuity-platform I'm sorry,
 1017  k logs -n akuity-platform portal-server-665c5dccbd-cgr9h
 1018  k logs -n akuity-platform portal-server-665c5dccbd-cgr9h -f
 1019  git diff  internal/portalapi/organization/create_ai_message_v1.go
 1020  git checkout  internal/portalapi/organization/create_ai_message_v1.go
 1021  git commit -m "fix web search mode would fall into an infinite loop of question-and-answer interactions" -s
 1022  git diff internal/utils/ai/
 1023  PWD
 1024  rm -rf create_ai_message_v1.go service.go
 1025  vim argo-manifests/argo.yaml
 1026  git add internal/utils/ai/constant.go
 1027  git commit -m "change web search mode to gpt-4o-mini-search-preview" -s
 1028  env | grep -i "akp\|kargo"
 1029  cllear
 1030  git commit -m "add test for argo and kargo" -s
 1031  git remote delete upstream
 1032  git remote add upstream https://github.com/akuity/terraform-provider-akp.git
 1033  vim ~/.vscode/argv.json
 1034  env |grep AKP
 1035  echo $AKUITY_API_KEY_ID
 1036  echo $AKUITY_API_KEY_SECRET
 1037  go test -v ./akp/... -tags=integration
 1038  go test -v ./akp/... -tags=integration |grep TestAccInstanceDataSource
 1039  go test -timeout 30s -run ^TestAccInstanceDataSource$ github.com/akuity/terraform-provider-akp/akp --help
 1040  go test -timeout 30s -run ^TestAccInstanceDataSource$ github.com/akuity/terraform-provider-akp/akp -h
 1041  go test -h
 1042  go test help
 1043  go help teset
 1044  go help test
 1045  go test -timeout 30s -run ^TestAccInstanceDataSource$ github.com/akuity/terraform-provider-akp/akp -count=1
 1046  terraform show
 1047  go test -timeout 30s -run ^TestAccInstanceDataSource$ github.com/akuity/terraform-provider-akp/akp
 1048  make acc-test
 1049  make acc-test TestAccInstanceDataSource
 1050  make acc-test TESTARGS TestAccInstanceDataSource
 1051  make acc-test TESTARGS=TestAccInstanceDataSource
 1052  make acc-test TESTARGS="-run TestAccKargoInstanceDataSource/TestAccKargoInstanceDataSource"
 1053  vim /Users/<USER>/MyPro/akuity/terraform-provider-akp/dist/deploy/basic.tf
 1054  terraform output
 1055  terraform plan -out
 1056  open basic.tf
 1057  export AKUITY_API_KEY_ID=pr331q54ja7isr5j\nexport AKUITY_API_KEY_SECRET=iyf6ecijnza73pxgoqo2ecohjj4j4xvi\nexport AKUITY_SERVER_URL=https://akuity.cloud\n\nakuity argocd instance list --organization-id 7nfpt3kqvd0vclx0\n
 1058  vim resource.tf
 1059  vim dist/deploy/basic.tf
 1060  export AKUITY_API_KEY_ID=on40go7ku35i6knz\nexport AKUITY_API_KEY_SECRET=xzxrua4x0udc8hzhnaq58vsw0kropl20\nexport AKUITY_SERVER_URL=https://akuity.cloud\n\nakuity argocd instance list --organization-id dk8efu1dc89pcl9v\n
 1061  history |grep akuity
 1062  export AKUITY_API_KEY_ID=aznxv9cjns0y2bty\nexport AKUITY_API_KEY_SECRET=7ds1oe7azhuifqmj15duha4trofa3zlw\nexport AKUITY_SERVER_URL=https://akuity.cloud\n\nakuity argocd instance list --organization-id dk8efu1dc89pcl9v\n
 1063  akuity argocd export --organization-id mygcbvoxgoujd8j7 --insecure-skip-tls-verify my-instance
 1064  akuity argocd export --organization-id dk8efu1dc89pcl9v --insecure-skip-tls-verify my-instance
 1065  history |grep akuity|grep export
 1066  akuity argocd export --organization-id dk8efu1dc89pcl9v my-instance
 1067  akuity argocd export --organization-id dk8efu1dc89pcl9v test-cluster
 1068  mv a.yaml a.log
 1069  vscode argo.yaml
 1070  code argo.yaml
 1071  akuity argocd apply --organization-id dk8efu1dc89pcl9v test-cluster argo\ copy.yaml.yml
 1072  akuity argocd apply --organization-id dk8efu1dc89pcl9v  argo\ copy.yaml.yml
 1073  hisotry |grep apply |grep akuity
 1074  history |grep apply |grep akuity
 1075  history |grep apply |grep akuity |grep -v kubectl
 1076  akuity argocd apply --organization-id dk8efu1dc89pcl9v  -f argo\ copy.yaml.yml
 1077  mv argo\ copy.yaml.yml argo1.yaml
 1078  akuity argocd apply --organization-id dk8efu1dc89pcl9v  -f argo1.yaml
 1079  akuity argocd apply --organization-id dk8efu1dc89pcl9v  -f argo1.yaml --name test
 1080  cd PET
 1081  cd data
 1082  cd original_cat
 1083  cd found_cats
 1084  theme = iTerm2 Solarized Light
 1085  theme = dark:catppuccin-frappe,light:catppuccin-latte\n
 1086  ghostty theme = dark:catppuccin-frappe,light:catppuccin-latte\n
 1087  ghostty +theme = dark:catppuccin-frappe,light:catppuccin-latte\n
 1088  ghostty + theme = dark:catppuccin-frappe,light:catppuccin-latte\n
 1089  ghostty +theme=tokyo-night
 1090  ghostty +list-themes\n
 1091  l
 1092  ls /Users/<USER>/Library/Application Support/com.mitchellh.ghostty
 1093  ls ~/Library/Application\ Support/com.mitchellh.ghostty/config
 1094  vim  ~/Library/Application\ Support/com.mitchellh.ghostty/config
 1095  vim ~/.config/ghostty/config
 1096  sudo vim ~/.config/ghostty/config
 1097  ghostty +list-keybinds --default\n
 1098  sudo vim /Users/<USER>/Library/Application\ Support/com.mitchellh.ghostty/config
 1099  cat argo.yaml
 1100  cd argo-manifests
 1101  cat argo1.yaml
 1102  vim argo1.yaml
 1103  vim tmp.yaml
 1104  akuity argocd apply --organization-id dk8efu1dc89pcl9v  -f tmp.yaml
 1105  akuity argocd apply --organization-id dk8efu1dc89pcl9v  -f tmp.yaml --name test
 1106  history |grep export |grep akuity
 1107  akuity argocd export --organization-id dk8efu1dc89pcl9v test-cluster > a.yaml
 1108  akuity argocd apply --organization-id dk8efu1dc89pcl9v  -f a.yaml
 1109  akuity argocd instance list --organization-id dk8efu1dc89pcl9v
 1110  terraform state list
 1111  akp --version
 1112  akuity argocd export --organization-id dk8efu1dc89pcl9v test-cluster > b.yaml
 1113  vim sharefile.py
 1114  python sharefile.py
 1115  python3 sharefile.py --dir ~/.config/clash/
 1116  git rebase --conntinue
 1117  git add aims api internal models pkg portal
 1118  git add aims api docs models pkg portal
 1119  git rebase --continue 
 1120  git log --oneline --graph --all
 1121  git clone https://github.com/akuityio/akuity-platform.git
 1122  git checkout tmp-mq
 1123  git checkout -b mq-tmp
 1124  mv tmp ../
 1125  git add aims docs internal pkg portal
 1126  git commit -m "merge main codes" -s
 1127  git add amis api docs pkg portal
 1128  git commit -m "merge upstream codes" -s
 1129  git checkout add-ai-web-search
 1130  git cherry-pick --abort
 1131  git cherry-pick fc2ea38c89141fb090e696d4bd44dd3ffc5831c5
 1132  git cherry-pick f36c2322647452e4ee1b32a4c07cbb0fac904eef
 1133  brew install golangci-lint
 1134  golangci-lint run
 1135  rm terraform.tfstate*
 1136  cat terraform.log
 1137  git diff *.go
 1138  git diff ../../akp/types/instance.go
 1139  export TF_LOG=DEBUG\nexport TF_LOG_PATH=terraform.log\nterraform plan
 1140  vim /Users/<USER>/MyPro/akuity/terraform-provider-akp/dist/deploy/terraform.log
 1141  export TF_LOG=DEBUG\nexport TF_LOG_PATH=terraform.log\nterraform apply
 1142  rm -rf terraform.logexport TF_LOG=DEBUG\nexport TF_LOG_PATH=terraform.log\nterraform apply
 1143  cd ee
 1144  rm -rf akuity-platform
 1145  git clone https://github.com/akuity/terraform-provider-akp.git
 1146  git diff ../../akp/types/kargo_instance.go
 1147  make acc-test TESTARGS="-run TestAccInstanceDataSource"
 1148  rm -rf terraform.*
 1149  vim terraform.log
 1150  grep syncResources terraform.log
 1151  rm -rf terraform.log&export TF_LOG=DEBUG\nexport TF_LOG_PATH=terraform.log\nterraform apply
 1152  git commit -m "modify build chat message" -s
 1153  make acc-test TESTARGS="-run TestAccKargoInstanceDataSource" 
 1154  git diff akp/types
 1155  git commit -m "add testcase" -s
 1156  akuity kargocd export --organization-id dk8efu1dc89pcl9v test-instance
 1157  akuity kargo export --organization-id dk8efu1dc89pcl9v test-instance
 1158  akuity kargo export --organization-id dk8efu1dc89pcl9v test-instance > kargo.yaml
 1159  akuity kargo apply --organization-id dk8efu1dc89pcl9v kargo.yaml
 1160  akuity kargo list instance
 1161  akuity kargo list
 1162  akuity kargo instance
 1163  akuity kargo instance list
 1164  akuity kargo instance list --organization-id dk8efu1dc89pcl9v
 1165  akuity kargo apply -h
 1166  git commit -m "update buildChatMessages" -s
 1167  terraform-provider-akp
 1168  akuity kargo apply --organization-id dk8efu1dc89pcl9v -f kargo.yaml
 1169  akuity version
 1170  vim kargo1.yaml
 1171  cd resources/akp_kargo_instance
 1172  mv kargo1.yaml kargo.yaml
 1173  git diff kargo.yaml
 1174  open l
 1175  akuity kargo export --organization-id dk8efu1dc89pcl9v test-instance > kargo1.yaml
 1176  git checkout kargo.yaml
 1177  akuity kargo apply --organization-id dk8efu1dc89pcl9v -f kargo1.yaml
 1178  git commit -m "adjust kargo provider test case" -s
 1179  make acc-test TESTARGS="-run TestAccKargoDataSource" 
 1180  git push upstream
 1181  git diff akp/data_source_akp_kargo_test.go
 1182  git diff akp/data_source_akp_instance_test.go
 1183  make acc-test TESTARGS="-run TestAccInstanceDataSource" 
 1184  git checkout akp/data_source_akp_instance_test.go
 1185  git add akp
 1186  git commit -m "adjust test case" -s
 1187  git push upstream 
 1188  cd ~/Applications
 1189  git diff .golangci.yml
 1190  rm -rf .golangci.yml
 1191  k logs -n akuity-platform platform-controller-bc77ddd98-jmpk5 
 1192  k logs -n akuity-platform portal-server-644bbf6f9d-c6r9g 
 1193  k logs -n akuity-platform portal-server-c85c5876c-bn6r2 
 1194  k logs -n akuity-platform portal-server-c85c5876c-bn6r2 |grep document
 1195  k logs -n akuity-platform platform-controller-56cff4755f-5jk9x 
 1196  k logs -n akuity-platform platform-controller-56cff4755f-5jk9x |grep document
 1197  k logs -n akuity-platform platform-controller-56cff4755f-5jk9x |grep documentation
 1198  k logs -n akuity-platform portal-server-c85c5876c-bn6r2|grep documentation
 1199  k logs -n akuity-platform portal-server-c85c5876c-bn6r2
 1200  k logs -n akuity-platform portal-server-d4488887b-6vbdh
 1201  k logs -n akuity-platform portal-server-d4488887b-6vbdh -f
 1202  k logs -n akuity-platform portal-server-d4488887b-6vbdh |grep getArgoCDApplicationEvents
 1203  ka logs platform-controller-8dccddb88-npqxj
 1204  ka logs platform-controller-8dccddb88-npqxj |grep akuity-platform
 1205  ka logs platform-controller-8dccddb88-npqxj |grep getArgoCDApplicationEvents
 1206  ka logs portal-server-d4488887b-6vbdh |grep getArgoCDApplicationEvents
 1207  ka logs platform-controller-7fd9878-xj9gv 
 1208  ka logs platform-controller-7fd9878-xj9gv |grep addFunction
 1209  ka logs portal-server-6f6b9bb59d-plmk2 |grep addFunction
 1210  ka logs portal-server-6f6b9bb59d-plmk2 |grep error
 1211  ka logs portal-server-6f6b9bb59d-plmk2 |grep Error
 1212  ka logs portal-server-6f6b9bb59d-plmk2 |grep getArgoCDApplicationEvents
 1213  k get po -A|grep coredns
 1214  ka logs postgres-7dc7d7d699-frxvj
 1215  ka logs portal-server-6f6b9bb59d-plmk2
 1216  ka logs portal-server-6f6b9bb59d-plmk2|grep -v fError |grep error
 1217  ka logs portal-server-6f6b9bb59d-plmk2|grep -v fError |grep Error
 1218  ka logs portal-server-6f6b9bb59d-plmk2|grep -v fError
 1219  ka logs portal-server-6f6b9bb59d-plmk2|grep -v fError|grep GIN
 1220  ka logs portal-server-6f6b9bb59d-plmk2|grep -v fError|grep -v GIN
 1221  ka logs portal-server-75b58ffb99-vnpv7 |grep error
 1222  ka logs portal-server-75b58ffb99-vnpv7 |grep Failed
 1223  ka logs portal-server-75b58ffb99-vnpv7 |grep getArgoCDApplicationEvents
 1224  ka logs platform-controller-77d46bb56c-gf8sm 
 1225  ka logs platform-controller-77d46bb56c-gf8sm |grep Fail
 1226  ka logs platform-controller-77d46bb56c-gf8sm |grep Failed
 1227  ka logs platform-controller-77d46bb56c-gf8sm |grep "Failed to get ArgoCD client"
 1228  ka logs portal-server-75b58ffb99-vnpv7 
 1229  ka logs portal-server-57456f5554-v6xp9
 1230  ka logs portal-server-647d9f54c5-kj7cd
 1231  ka logs portal-server-647d9f54c5-kj7cd |grep fError
 1232  ka logs portal-server-647d9f54c5-kj7cd |grep error
 1233  ka logs portal-server-647d9f54c5-kj7cd 
 1234  ka logs portal-server-78f4bd465d-wpwjx |grep Documentation
 1235  ka logs portal-server-78f4bd465d-wpwjx
 1236  ka logs portal-server-78f4bd465d-wpwjx |grep runAppAction
 1237  ka logs portal-server-78f4bd465d-wpwjx 
 1238  ka logs portal-server-78f4bd465d-wpwjx -f
 1239  ka logs portal-server-7479fb8bc5-96m58 -f
 1240  ka logs portal-server-7479fb8bc5-96m58 -f |grep -v addFunction
 1241  ka logs portal-server-f7594b9dd-p67hn
 1242  ka logs portal-server-f7594b9dd-p67hn -f
 1243  ka logs portal-server-f7594b9dd-p67hn -f |grep -v addFunction
 1244  ka logs portal-server-
 1245  ka logs portal-server-55cbb65fdf-vz5ms
 1246  ka logs portal-server-55cbb65fdf-vz5ms -f |grep -v addFun
 1247  ka logs portal-server-55cbb65fdf-vz5ms -f 
 1248  ka logs portal-server-6d66645f9c-7lf7k
 1249  ka logs portal-server-6d66645f9c-7lf7k |grep addFunction1
 1250  ka logs portal-server-6d66645f9c-7lf7k -f 
 1251  ka logs portal-server-5ff57dd889-n8r4t 
 1252  ka logs portal-server-5ff57dd889-n8r4t  -f 
 1253  ka logs portal-server-5ff57dd889-n8r4t  |grep addFunction1
 1254  ka logs portal-server-56998bdf44-pgjpf 
 1255  ka logs portal-server-56998bdf44-pgjpf  -f
 1256  ka logs portal-server-56998bdf44-pgjpf  |grep -v 144
 1257  ka logs portal-server-6bcf445f6-x5ldn -f 
 1258  ka logs portal-server-6bcf445f6-x5ldn -f  |grep -v 144
 1259  k get jobs -A
 1260  k delete po -n akuity-platform kubevision-event-cleaner-29127860-ljpz7 kubevision-event-cleaner-29127880-99j9v kubevision-event-cleaner-29127900-2rlbl kubevision-event-cleaner-29127920-j5tc2 kubevision-event-cleaner-29127940-lfkth
 1261  k delete po -n akuity-platform kubevision-event-cleaner-29127860-ljpz7 kubevision-event-cleaner-29127880-99j9v kubevision-event-cleaner-29127900-2rlbl kubevision-event-cleaner-29127920-j5tc2 kubevision-event-cleaner-29127940-lfkth --force --grace-periods 0
 1262  k delete po -n akuity-platform kubevision-event-cleaner-29127860-ljpz7 kubevision-event-cleaner-29127880-99j9v kubevision-event-cleaner-29127900-2rlbl kubevision-event-cleaner-29127920-j5tc2 kubevision-event-cleaner-29127940-lfkth --force --grace-period 0
 1263  k logs -n platform-controller-d9fb58b58-gwcjn |grep service.go
 1264  k logs -n akuity-platform platform-controller-d9fb58b58-gwcjn |grep service.go
 1265  k logs -n akuity-platform platform-controller-d9fb58b58-gwcjn 
 1266  k logs -n portal-server-c88d75477-8msxq |grep msgs
 1267  k logs -n akuity-platform portal-server-c88d75477-8msxq |grep msgs
 1268  k logs -n akuity-platform portal-server-c88d75477-8msxq 
 1269  k logs -n akuity-platform portal-server-c88d75477-8msxq |grep tools
 1270  k logs -n akuity-platform portal-server-c88d75477-8msxq |grep msg
 1271  k logs -n akuity-platform portal-server-c88d75477-8msxq |grep searchDocumentation
 1272  k logs -n akuity-platform platform-controller-d9fb58b58-gwcjn|grep searchDocumentation
 1273  gi tdiff  internal/services/ai/functions/argocd.go
 1274  git diff  internal/services/ai/functions/argocd.go
 1275  git checkout internal/services/ai/functions/argocd.go
 1276  git checkout -b ai-doc-search
 1277  git checkout internal/services/ai/client.go
 1278  ka get po 
 1279  k logs -n akuity-platform portal-server-7c4d8ff7fc-nxml5 
 1280  k logs -n akuity-platform portal-server-7c4d8ff7fc-nxml5 |grep search.go
 1281  k logs -n akuity-platform portal-server-7c4d8ff7fc-nxml5 |grep Documentation
 1282  k logs -n akuity-platform portal-server-7c4d8ff7fc-nxml5 |grep documentation
 1283  ka logs platform-controller-6644fcc849-ffj9n |grep search.go
 1284  ka logs platform-controller-6644fcc849-ffj9n |grep ocumentation
 1285  ka logs platform-controller-6644fcc849-ffj9n |grep documentation
 1286  ka logs platform-controller-6644fcc849-ffj9n 
 1287  cd /Users/<USER>/MyPro/PTE/pte-app && npm install
 1288  cd words-memorize/
 1289  cd words-memorize
 1290  cd Mac\ \(1\)/
 1291  cp -rf CursorPro.app /
 1292  cd src
 1293  npm dev run
 1294  npm run dev
 1295  npm start
 1296  cd /Users/<USER>/MyPro/PTE/words-memorize && npm start
 1297  cd .
 1298  vim  /Users/<USER>/Library/Application Support/k9s/config.yaml
 1299  k9s config
 1300  k9s Config
 1301  cat /k9s/config.yaml
 1302  vim ~/Library/Application\ Support/k9s/config.yaml
 1303  9s
 1304  cd ~/Library/Application\ Support/k9s/
 1305  cd ks
 1306  vim tokyonight.yml
 1307  k9s --help
 1308  curl -I http://localhost:2345/
 1309  curl -I http://localhost:2345/metrics
 1310  kubedlv connect --port-forward platform-controller-7b9b4cc676-f4jvv
 1311  brew install kubedlv
 1312  brew install go-delve/kubedlv/kubedlv\n
 1313  brew install delve
 1314  dlv version
 1315  dlv connect --port-forward platform-controller-7b9b4cc676-f4jvv
 1316  dlv connect http://localhost:2345/
 1317  dlv connect localhost:2345
 1318  dlv connect localhost:234
 1319  git commit -m "add ai doc search" -s
 1320  cealr
 1321  git commit -m "modify doc research tools" -s
 1322  git checkout -b fix-argo-apply origin/fix-argo-apply
 1323  make akuity-platform
 1324  cd ./cmd/akuity-platform
 1325  cd dist/build/
 1326  history|grep akuity
 1327  ls akuity-platform/dist/akuity-cli/
 1328  cd dist/akuity-cli/v0.22.1-0.20250512022903-206f8f350448
 1329  history|grep akuity |grep mv
 1330  history
 1331  history|2174
 1332  history:2174
 1333  ls dist/akuity-cli/v0.22.1-0.20250512022903-206f8f350448
 1334  rm -rf dist/akuity-cli/v0.22.1-0.20250512022903-206f8f350448
 1335  ls dist
 1336  ls dist/akuity-cli
 1337  ls dist/akuity-cli/v0.22.3-0.20250523080454-0f4861093ae3.dirty
 1338  ls dist/akuity-cli/v0.22.3-0.20250523080454-0f4861093ae3.dirty/akuity /usr/local/bin/
 1339  mv dist/akuity-cli/v0.22.3-0.20250523080454-0f4861093ae3.dirty/akuity /usr/local/bin/
 1340  sudo mv dist/akuity-cli/v0.22.3-0.20250523080454-0f4861093ae3.dirty/akuity /usr/local/bin/
 1341  history|grep pnm
 1342  history|grep pnpm
 1343  cd terraform-provider-akp_0.9.0-rc.0_darwin_arm64
 1344  ./terraform-provider-akp_v0.9.0-rc.0
 1345  mv terraform-provider-akp_v0.9.0-rc.0 /opt/homebrew/bin/terraform
 1346  export AKUITY_API_KEY_ID=5gux7oe9ry0csnqp\nexport AKUITY_API_KEY_SECRET=sydbv91dal0edhy2a72wgiic589gldzc\nexport AKUITY_SERVER_URL=http://localhost:3001
 1347  rm -rf terraform.log
 1348  cd examples/resources
 1349  vim akp_instance/resource.tf
 1350  open akp_instance
 1351  which terraform
 1352  rm -rf /opt/homebrew/bin/terraform
 1353  brew install terraform
 1354  brew link terraform
 1355  terraform init
 1356  ka logs portal-server-7f8979647f-bwlcp |grep error
 1357  ka logs portal-server-7f8979647f-bwlcp |grep portal-server-7f8979647f-bwlcp
 1358  ka logs portal-server-7f8979647f-bwlcp |grep ApplyInstance
 1359  k describe po -n argocd-kezctxxgo9fsaiak argocd-server-7c4f44d84f-6999c
 1360  k get po -n argocd-4isxjfpziti7ezrw
 1361  git pull --rebase origin main
 1362  docker builder prune -a
 1363  k8s Skins
 1364  k9s skins
 1365  oepn .
 1366  open 
 1367  git clone https://github.com/derailed/k9s.git
 1368  k9s info
 1369  cd  /Users/<USER>/Library/Application\ Support/k9s
 1370  cd skins
 1371  cp -rf ./ ~/Library/Application\ Support/k9s/skins
 1372  cd ~/Library/Application\ Support/k9s
 1373  vim help.js
 1374  vim Desktop/help.js
 1375  cd go
 1376  cd AI
 1377  cd OpenManus
 1378  cat main.py
 1379  git diff ../akp_kargo_instance/basic.tf
 1380  ka get po
 1381  k get po -n v2.14.10-ak.54
 1382  k describe po -n argocd-2sn3qgzw1t3nn2ft argocd-2sn3qgzw1t3nn2ft 
 1383  k describe po -n argocd-2sn3qgzw1t3nn2ft argocd-2sn3qgzw1t3nn2ft
 1384  k get po -n argocd-2sn3qgzw1t3nn2ft
 1385  k describe po -n argocd-2sn3qgzw1t3nn2ft argocd-server-5f8ff569c5-lsmzz
 1386  k get pv -A
 1387  k get pvc -A
 1388  k get secrets -A|grep argocd-server-kubeconfig
 1389  k get secrets -n argocd-23kbx538gdu2auax argocd-server-kubeconfig
 1390  k get secrets -n argocd-23kbx538gdu2auax argocd-server-kubeconfig -oyaml
 1391  k delete ns akuity --force --grace-periods=0
 1392  k delete ns akuity --force --grace-period=0
 1393  k3d cluster delete akuity-customer-3
 1394  TOKEN="eyJhbGciOiJSUzI1NiIsImtpZCI6ImQ3Y2ExMGUzMDhkYjU3NDcwMzQ5OTZkMmU0MjczODZkNDFjODc0YjAifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.fQJiBF2tqII5R-RWOKmwxCAZkuHnYLUhcLZIHwtMuDXzPP2Syu2ZAKQFZ1B8YgQq2lyIl3maLGNv8dQ0QvMCRXG-DFa3HPqNZEVD7BGgnLS2Qbl8qM9xHifwnWP9oS5XHRu-PTDo6NoHEh3usATqYHo0FWVAuIqx0APwq0axmHCkhspI6sM6r9hV5ZGcNALSkDLTX0XHH0lTJROgE0KFi6QyI4gCYq3rQixMtu2WS75JtIIb2LYbTm8QtTvsW5-I6ftvUBeakSzTe7b2iZFEWvINTaUJjytgOK7O35N630ombMpBOw6QD_GPlqNcwsdf62WGbd5dy820i7nldmMfbw" && curl -s -H "Authorization: Bearer $TOKEN" "http://localhost:3001/api/v1/orgs/hrb6s47cp094ij27/argocd/instances/tok0u2b2fpuwfqya/clusters/om8ep5uf3eds9plf/manifests" -k| kubectl apply -f -
 1395  ls /Users/<USER>/go/bin/terraform-provider-akp
 1396  cksum ~/Downloads/terraform-provider-akp_0.9.0-rc.0_darwin_arm642/terraform-provider-akp_v0.9.0-rc.0
 1397  rm -rf /Users/<USER>/go/bin/terraform-provider-akp
 1398  mv /Users/<USER>/Downloads/terraform-provider-akp_0.9.0-rc.0_darwin_arm642/terraform-provider-akp_v0.9.0-rc.0 /Users/<USER>/go/bin/terraform-provider-akp 
 1399  cksum /Users/<USER>/go/bin/terraform-provider-akp
 1400  kubectl config view --flatten --minify
 1401  echo  LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUJkekNDQVIyZ0F3SUJBZ0lCQURBS0JnZ3Foa2pPUFFRREFqQWpNU0V3SHdZRFZRUUREQmhyTTNNdGMyVnkKZG1WeUxXTmhRREUzTkRneU1qZzFPRFF3SGhjTk1qVXdOVEkyTURNd016QTBXaGNOTXpVd05USTBNRE13TXpBMApXakFqTVNFd0h3WURWUVFEREJock0zTXRjMlZ5ZG1WeUxXTmhRREUzTkRneU1qZzFPRFF3V1RBVEJnY3Foa2pPClBRSUJCZ2dxaGtqT1BRTUJCd05DQUFTVFFIMFhWNE04ajNtbmI5TXQ0azFJTHJEZTEyNUl2UzR0ZGNaNTZvUnIKMXdzQmVXcEdjUnhkN2hlajNqSzJQNlhxVUpYOFdyY3lPQ3FnbGRleDIrNTJvMEl3UURBT0JnTlZIUThCQWY4RQpCQU1DQXFRd0R3WURWUjBUQVFIL0JBVXdBd0VCL3pBZEJnTlZIUTRFRmdRVVlETnVhNFFic3piQVVqaWEwekNUCmNoUFQvbll3Q2dZSUtvWkl6ajBFQXdJRFNBQXdSUUloQUtyYkliZHFCY0FBZEdTOGtwU0dubzQzcVBTTVZSblkKdTVaZlBVWGg1RzlSQWlCVUdXaEpYMlRGTTZFOUdQU3FEVzBLSWN3UmdBMXJVSFhzWVVBeTdHWFhvUT09Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K |base64 -d
 1402  akuity argocd instance list --organization-id jb3e59v4d217idob
 1403  export AKUITY_API_KEY_ID=o5rz5bzs5x9f2sny\nexport AKUITY_API_KEY_SECRET=aart6le61ee1jwew8tk56hxioh3bw2k5\nexport AKUITY_SERVER_URL=http://localhost:3001
 1404  kubectx
 1405  kubectl get pod --all-namespaces
 1406  vim terraform.tfstate
 1407  git pull --rebase\n
 1408  git branch -D add-ai-web-search-tmp
 1409  git branch -D mq-tmp
 1410  git checkout -b add-ai-doc-search
 1411  touch internal/services/ai/functions/doc_search.md
 1412  touch internal/services/ai/functions/search.go
 1413  mkdir internal/services/ai/types
 1414  vim internal/services/ai/types/openai.go
 1415  touch internal/services/ai/types/openai.go
 1416  k logs -n akuity-platform portal-server-6ff68bcccd-tc77h
 1417  k logs -n akuity-platform platform-controller-8679668dfb-l486g
 1418  k logs -n akuity-platform platform-controller-8679668dfb-l486g |grep error
 1419  k logs -n akuity-platform portal-server-6ff68bcccd-tc77h|grep error
 1420  k delete po -n akuity-platform
 1421  git commit -m "add document search for AI" -s
 1422  git checkout ai-doc-search
 1423  git checkout add-ai-doc-search 
 1424  git commit -m "update prompt for ai search" -s
 1425  ls dist/akuity-cli/v0.22.3-0.20250526064652-8777b9348c9b.dirty/akuity
 1426  ls -lhst dist/akuity-cli/v0.22.3-0.20250526064652-8777b9348c9b.dirty/akuity
 1427  export AKUITY_API_KEY_ID=o5rz5bzs5x9f2sny\nexport AKUITY_API_KEY_SECRET=11wnz5qzz1chs1nc1xlklath8pni5982\nexport AKUITY_SERVER_URL=https://portal-server.akuity-platform
 1428  mv dist/akuity-cli/v0.22.3-0.20250526064652-8777b9348c9b.dirty/akuity /usr/local/bin/akuity
 1429  sudo mv dist/akuity-cli/v0.22.3-0.20250526064652-8777b9348c9b.dirty/akuity /usr/local/bin/akuity
 1430  history |grep argocd |grep list
 1431  akuity argocd export instance argocd --insecure-skip-tls-verify
 1432  akuity argocd export instance argocd --insecure-skip-tls-verify --organization-id hrb6s47cp094ij27
 1433  akuity argocd export  argocd --insecure-skip-tls-verify --organization-id hrb6s47cp094ij27
 1434  rm -rf a.yaml ]
 1435  rm -rf a.yaml 
 1436  akuity argocd export  argocd --insecure-skip-tls-verify --organization-id hrb6s47cp094ij27 > a.yaml
 1437  git commit -m "fix" -s
 1438  rm -rf a.yaml
 1439  sudo mv dist/akuity-cli/v0.22.3-0.20250526064652-8777b9348c9b.dirty///akuity /usr/local/bin/akuity
 1440  rm a.yaml
 1441  akuity argocd export argocd1 --insecure-skip-tls-verify --organization-id hrb6s47cp094ij27 > a.yaml
 1442  akuity argocd apply instance --insecure-skip-tls-verify --organization-id hrb6s47cp094ij27 -f  a.yaml
 1443  k logs -n akuity-platform portal-server-798555ddfc-7gftj |grep error
 1444  akuity argocd export argocd2 --insecure-skip-tls-verify --organization-id hrb6s47cp094ij27 > b.yaml
 1445  vim b.yaml
 1446  git checkout -b update_cluster_spec 
 1447  rm -rf dist/akuity-cli/*
 1448  cd dist/akuity-cli 
 1449  cd v.dirty
 1450  akuity argocd instance list --organization-id hrb6s47cp094ij27 --insecure-skip-tls-verify
 1451  akuity argocd export argocd2 --insecure-skip-tls-verify --organization-id hrb6s47cp094ij27 > c.yaml
 1452  vim c.yaml
 1453  ls -lslht
 1454  git checkout ../../../models/models/argo_cd_cluster_ext.go
 1455  cd dist/akuity-cli/v.dirty/
 1456  cd dist/akuity-cli/v.dirty
 1457  sudo mv ./akuity /usr/local/bin/akuity
 1458  akuity argocd export argocd2 --insecure-skip-tls-verify --organization-id hrb6s47cp094ij27 
 1459  git push --set-upstream origin update_cluster_spe
 1460  git push --set-upstream origin update_cluster_spec
 1461  git remote remove own 
 1462  git remote add -h
 1463  git remote add origin ssh://**************/akuity/terraform-provider-akp.git
 1464  git remote remove upstream
 1465  git checkout -b applying-resource-fix origin/applying-resource-fix
 1466  go install mvdan.cc/gofumpt@latest
 1467  gofumpt
 1468  which gofmt
 1469  git checkout add-ai-doc-search
 1470  ls ~/.cursor/argv.json
 1471  cat ~/.cursor/argv.json
 1472  cat ~/.cursor/mcp.json
 1473  cat ~/.vscode/settings.json
 1474  rm -rf ./.vscode
 1475  rm -rf .vscode
 1476  cd ../terraform-provider-akp
 1477  cd dist/deploy && terraform init
 1478  terraform apply -auto-approve
 1479  portal
 1480  ui
 1481  akuity argocd instance list --organization-id hrb6s47cp094ij27
 1482  terraform apply > ~/Desktop/b.yaml
 1483  vimdiff ~/Desktop/a.yaml ~/Desktop/b.yaml
 1484  git commit -m "update system prompt" -s
 1485  k get po -n argocd_resources = data.akp_instance.argocd.argocd_resources
 1486  k get po -n argocd-9fv1fqly5q9puuii
 1487  go build -o /Users/<USER>/go/bin/terraform-provider-akp
 1488  mv argo-manifests argocd-manifests
 1489  terraform apply 
 1490  ➜  deploy git:(applying-resource-fix) ✗ terraform apply          
 1491  ╷
 1492  │ Warning: Provider development overrides are in effect
 1493  │ The following provider development overrides are set in the CLI configuration:
 1494  │  - akuity/akp in /Users/<USER>/go/bin
 1495  │ 
 1496  │ The behavior may therefore not match any released version of the provider and applying changes may cause the state to become incompatible with published releases.
 1497  rm terraform.tfstate
 1498  cp basic.tf basic.tf.bak
 1499  cd dist/deploy
 1500  git checkout applying-resource-fix
 1501  cd dist/
 1502  kubectl delete ns akuity --force --grace-periods 0
 1503  kubectl delete ns akuity --force --grace-period 0
 1504  history |grep Add
 1505  history |grep add
 1506  k getp o -A
 1507  TOKEN="eyJhbGciOiJSUzI1NiIsImtpZCI6ImJhYjg0MzZiZWVhMDE2MTdjNTQ1YmQwY2FhMjgyNmQwYjJkMTY5OWIifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.CEjbHc6VG9-_EgICxUBxatl57NyBIqQ8k_knc7LrSizx_wA5ArNv-wGDBmRm31ZBHAuT7cRXIxluoUIWfYfue22Jkpxdo4tlWIjzj7ElcfqqMPuprSLk6qs1xgjaif9RmsV4eaP4y8dQza53p94wctVeUHoBdm5hBHcqFzebH7x7RdJnfXIIRniFTcysgWHxepNce4dXCEJmxjkd-JgzgRFRDWIceBndKoofwusXC-EV9O1KZdJL1sjLew56G4-AeOJz-rqk2JNa88XOHuTnvzH81RCNjSnmbf3M-El1w_frIKNGDSFM3hRs8nAbxVq_Z7t0LEkA6M_vrV4XjDse3w" && curl -s -H "Authorization: Bearer $TOKEN" "https://portal-server.akuity-platform/api/v1/orgs/hrb6s47cp094ij27/argocd/instances/jq2synilr4ni3hdk/clusters/8ivoaumdchmcf93m/manifests" | kubectl apply -f -
 1508  TOKEN="eyJhbGciOiJSUzI1NiIsImtpZCI6ImJhYjg0MzZiZWVhMDE2MTdjNTQ1YmQwY2FhMjgyNmQwYjJkMTY5OWIifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.CEjbHc6VG9-_EgICxUBxatl57NyBIqQ8k_knc7LrSizx_wA5ArNv-wGDBmRm31ZBHAuT7cRXIxluoUIWfYfue22Jkpxdo4tlWIjzj7ElcfqqMPuprSLk6qs1xgjaif9RmsV4eaP4y8dQza53p94wctVeUHoBdm5hBHcqFzebH7x7RdJnfXIIRniFTcysgWHxepNce4dXCEJmxjkd-JgzgRFRDWIceBndKoofwusXC-EV9O1KZdJL1sjLew56G4-AeOJz-rqk2JNa88XOHuTnvzH81RCNjSnmbf3M-El1w_frIKNGDSFM3hRs8nAbxVq_Z7t0LEkA6M_vrV4XjDse3w" && curl -s -H "Authorization: Bearer $TOKEN" "https://portal-server.akuity-platform/api/v1/orgs/hrb6s47cp094ij27/argocd/instances/jq2synilr4ni3hdk/clusters/8ivoaumdchmcf93m/manifests" -k| kubectl apply -f -
 1509  TOKEN="eyJhbGciOiJSUzI1NiIsImtpZCI6ImJhYjg0MzZiZWVhMDE2MTdjNTQ1YmQwY2FhMjgyNmQwYjJkMTY5OWIifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.CEjbHc6VG9-_EgICxUBxatl57NyBIqQ8k_knc7LrSizx_wA5ArNv-wGDBmRm31ZBHAuT7cRXIxluoUIWfYfue22Jkpxdo4tlWIjzj7ElcfqqMPuprSLk6qs1xgjaif9RmsV4eaP4y8dQza53p94wctVeUHoBdm5hBHcqFzebH7x7RdJnfXIIRniFTcysgWHxepNce4dXCEJmxjkd-JgzgRFRDWIceBndKoofwusXC-EV9O1KZdJL1sjLew56G4-AeOJz-rqk2JNa88XOHuTnvzH81RCNjSnmbf3M-El1w_frIKNGDSFM3hRs8nAbxVq_Z7t0LEkA6M_vrV4XjDse3w" && curl -s -H "Authorization: Bearer $TOKEN" "https://portal-server.akuity-platform/api/v1/orgs/hrb6s47cp094ij27/argocd/instances/jq2synilr4ni3hdk/clusters/ky2yjurddvis04w7/manifests" -k | kubectl apply -f -
 1510  k get deploy
 1511  k edit deploy
 1512  k get po
 1513  git pll
 1514  ╵
 1515  vim /etc/docker/daemon.json
 1516  orbctl -h
 1517  orbctl restart
 1518  orbctl restart ubuntu
 1519  orbctl list
 1520  sudo systemctl daemon-reexec\nsudo systemctl restart docker\n
 1521  orb dns reload
 1522  orb doctor
 1523  orb 
 1524  sudo dscacheutil -flushcache; sudo killall -HUP mDNSResponder
 1525  nslookup registry-1.docker.io 8.8.8.8
 1526  orbstack settings\n
 1527  orb settings
 1528  orbctl settings
 1529  docker run --rm alpine nslookup google.com\n
 1530  docker run --rm busybox ping -c 3 8.8.8.8\n
 1531  FROM golang:1.22.1-alpine\n
 1532  TOKEN="******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" && curl -s -H "Authorization: Bearer $TOKEN" "https://portal-server.akuity-platform/api/v1/orgs/8vjk6ryzyw84kvo5/argocd/instances/efsyivpkowjfszkk/clusters/othisr3bdo3k4wan/manifests" -k | kubectl apply -f -
 1533  ka logs portal-server-659468b4bd-7vxqm
 1534  k delete po -n akuity-platform platform-controller-578bd89b74-xch56 portal-server-659468b4bd-7vxqm
 1535  k logs -n akuity-platform platform-controller-578bd89b74-7rhwv
 1536  ping api.openai.com
 1537  ping http://portal-server.akuity-platform/
 1538  curl http://portal-server.akuity-platform/
 1539  git checkout -b vaildate-suggested-contexts
 1540  git commit -m "validate suggested contexts" -s
 1541  cd /Users/<USER>/MyPro/akuity/akuity-platform
 1542  git checkout vaildate-suggested-contexts
 1543  git pull origin
 1544  history |grep git
 1545  git pull origin main
 1546  pw
 1547  tree
 1548  go build 
 1549  cd cmd
 1550  go build -o share
 1551  python3 sharefile.py --dir ~/.config/clash
 1552  git commit -m "fix get log error" -s
 1553  TOKEN="******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" && curl -s -H "Authorization: Bearer $TOKEN" "https://portal-server.akuity-platform/api/v1/orgs/o22qxp46d85j33vg/argocd/instances/2qj8v87yfu24goz8/clusters/mosbqawoyivukdze/manifests" -k | kubectl apply -f -
 1554  k delete po -n akuity-platform platform-controller-7b965c7f7-4hhhk portal-server-85b68f4f-4v52t
 1555  git commit -m "add unit test" -s
 1556  git commit -m "format files" -s
 1557  git commit -m "change the query list to count" -s
 1558  git commit -m "only check resource name" -s
 1559  cat .github/workflows/release.yml
 1560  git tag v0.9.0-rc.1
 1561  git push origin tag v0.9.0-rc.1
 1562  rm -rf a.yaml b.yaml c.yaml
 1563  git branch -D update_cluster_spec
 1564  git branch -D add-ai-doc-search
 1565  git branch -D fix-argo-apply
 1566  git branch -D add-ai-web-search
 1567  git branch -D ai-doc-search
 1568  d kubevision-scripts
 1569  curl -v https://portal-server.akuity-platform/instances/argocd\n
 1570  vim ~/.config/clash/config.yaml
 1571  cd /etc/
 1572  vim hosts
 1573  cd ~/.config
 1574  cd clash
 1575  curl -v https://portal-server.akuity-platform/instances/argocd
 1576  curl -v http://portal-server.akuity-platform/instances/argocd
 1577  clash curl -vLk http://portal-server.akuity-platform/instances/argocd
 1578  curl -vL http://portal-server.akuity-platform/instances/argocd
 1579  curl -vLk http://portal-server.akuity-platform/instances/argocd
 1580  curl https://registry-1.docker.io/v2/\n
 1581  c d..
 1582  rm @
 1583  cat README.md
 1584  d terraform-provider-akp
 1585  history |grep AKUITY_SERVER_URL
 1586  history |grep AKUITY_SERVER_URL |grep pnpm
 1587  docker pull golang:latest
 1588  curl google.com
 1589  curl youtube.com
 1590  wget google.com
 1591  cat index.html
 1592  rm -rf index.html
 1593  docker infor
 1594  docker info
 1595  ping docker.mirrors.ustc.edu.cn
 1596  ping dockerproxy.com
 1597  ping registry.docker-cn.com
 1598  ping hub-mirror.c.163.com
 1599  cd ~/.orbstack
 1600  cd config
 1601  docker pull /golang:alpine
 1602  mv ~/.orbstack/config/docker.json ./
 1603  k logs -n akuity-platform portal-server-544df76668-c2f7g
 1604  k logs -n akuity-platform portal-server-544df76668-c2f7g|grep error
 1605  k logs -n akuity-platform portal-server-544df76668-c2f7g|grep error|grep -v "does not exist"
 1606  k logs -n akuity-platform platform-controller-df87f59f4-d27wb |grep error
 1607  cp -rf docker.json ~/.orbstack/config/
 1608  orb delete k8s -f
 1609  orb start k8s
 1610  k3d cluster delete akuity-platform
 1611  ka
 1612  git checkout -b kargo-extension-setting
 1613  git commit -m "add kargo akuity intelligence setting" -s
 1614  git pull origin --rebase
 1615  git pull origin main --rebase
 1616  make
 1617  git commit -m "add AiSreEnabled field" -s
 1618  git commit -m "modify AkuityIntelligence struct" -s
 1619  docker pull busybox:latest
 1620  docker pull busybox 
 1621  docker pull centos:latest
 1622  docker pull centos
 1623  docker images |grep busybox
 1624  docker image -h
 1625  docker image rm 7b4721e21460 e99306e01437 3474b0c62e52
 1626  docker images |grep busy
 1627  docker rmi 7b4721e21460 3474b0c62e52
 1628  export USE_ORBSTACK=true
 1629  sh hack/dev-down.sh
 1630  cat ../kubevision-scripts/stop-akp.sh
 1631  make dev-down
 1632  orb status
 1633  bash -x hack/dev-down.sh
 1634  echo $USE_ORBSTACK
 1635  USE_ORBSTACK=true make dev-down
 1636  cd SlackUp
 1637  orb restart
 1638  orb restart -al
 1639  orb update
 1640  http://127.0.0.1:7890
 1641  telnet 
 1642  k get po -n argocd-031im5xqu4yuyz8r
 1643  k get po -n argocd-031im5xqu4yuyz8r  -w
 1644  TOKEN="eyJhbGciOiJSUzI1NiIsImtpZCI6IjRkNWEyMGQ0NDhmN2QyMDA2ZTNhNzgyY2E4ZmE5ZDBmYjgwMWI3YmMifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.dY0Sm1-28VldGRwb94QKpFFMTyoyHq4Y-8GYRhESTCa6blWOL--1AoTSna-s5PNiBzBCWJjSCKR4jUjhk0JZPA2xSL9Fk6iCNFL01jk_61f18RXJslKEuYxTJN7vJ4TpHSKzirNmbY1oGQJxITP7r7OVqMgWWikTPC3GJxRS40WU2OTHPgwkT5jMZupeZtd3WhLT9sgwf1DCYfquzK92NuM3CgiM3cSv4R6mtgaR3ff7r7n8zKYn1y-KRk1Dg56JzvFzt-ZTeESVtQjuIsD6jyERjn8UzrkjlHfQmEtldPpbzjaxBKa3Kh3yT22FQbB9ax6w_xbPbjamMW4z8YvvqA" && curl -s -H "Authorization: Bearer $TOKEN" "https://portal-server.akuity-platform/api/v1/orgs/f0asfoltf4jk6fvy/argocd/instances/031im5xqu4yuyz8r/clusters/kel4bldu3iad9ls3/manifests" -k | kubectl apply -f -
 1645  k get po -n argocd-031im5xqu4yuyz8r 
 1646  k logs -n akuity-platform platform-controller-75f7cf4766-lg58s
 1647  k delete po -n akuity-platform platform-controller-75f7cf4766-lg58s portal-server-5dc7f85848-bxx98
 1648  k logs -n akuity-platform platform-controller-75f7cf4766-9qghv
 1649  k logs -n akuity-platform platform-controller-75f7cf4766-9qghv -f
 1650  ping https://chatgpt.com/
 1651  ping chatgpt.com
 1652  docker pull golang 
 1653  ping us-docker.pkg.dev
 1654  docker pull dev.akuity.i
 1655  docker pull us-docker.pkg.dev/akuity/akp/akuity-platform:latest
 1656  docker images |grep akuity-platform
 1657  docker images |grep akuity
 1658  dockr pull busybox
 1659  lls
 1660  kubectl get pod -n kube-system -l k8s-app=kube-dns -o 'jsonpath={.items[0].status.phase}'
 1661  rm -rf ~/.orbstack/config/docker.json
 1662  cat docker.json
 1663  cp docker.json ~/.orbstack/config/docker.json
 1664  docker pull /golang:1.23.8
 1665  docker pull golang:1.23.8
 1666  docker pull library/debian:12.7-slim
 1667  docker pull library/golang:1.23.8
 1668  obr restart
 1669  mv  ~/.orbstack/config/docker.json  ~/.orbstack/config/config.json
 1670  vim ~/.orbstack/config/config.json
 1671  orb config docker
 1672  orb restart docker
 1673  vim Dockerfile
 1674  docker run -it --rm \\n  -e HTTP_PROXY=http://127.0.0.1:7890 \\n  -e HTTPS_PROXY=http://127.0.0.1:7890 \\n  alpine sh\n
 1675  docker pull library/node:22.3.0-bullseye
 1676  cat Dockerfile
 1677  curl -I https://google.com
 1678  cat ~/.orbstack/vmconfig.json
 1679  export https_proxy=http://127.0.0.1:7890 http_proxy=http://127.0.0.1:7890 all_proxy=socks5://127.0.0.1:7890
 1680  cat ~/.orbstack/config/docker.json
 1681  docker build -t proxy-test .\n
 1682  kubectl delete po -n akuity-platform postgres-7ccb65b454-psldz
 1683  docker image
 1684  k describe po -n akuity-platform postgres-7ccb65b454-qlxsn
 1685  docker pull postgres:13.7
 1686  docker pull rancher/k3s:v1.31.3-k3s1
 1687  kubectl config -h
 1688  kubectl config get-contexts
 1689  kubectl config delete-context k3d-akuity-customer 
 1690  kubectl config delete-context k3d-akuity-customer  k3d-akuity-customer1 orbstack
 1691  kubectl config delete-context k3d-akuity-customer1 orbstack
 1692  kubectl config delete-context orbstack
 1693  orb restart 
 1694  orb restart -a
 1695  nslookup registry-1.docker.io
 1696  kubectl delete po -n kube-system coredns-56f6fc8fd7-ts6nf
 1697  k describe po -n kube-system local-path-provisioner-5cf85fd84d-f4gt4
 1698  k describe po -n argocd-5y1bizr6l8jpmlnh k3s-webhook-58cd46d987-j564j
 1699  k describe po -n argocd-5y1bizr6l8jpmlnh argocd-redis-ha-server-0
 1700  docker pull quay.io/akuity/busybox:1.36.0
 1701  k get po -n argocd-5y1bizr6l8jpmlnh k3s-54fccc5ff6-wb4n9
 1702  k describe po -n argocd-5y1bizr6l8jpmlnh k3s-54fccc5ff6-wb4n9
 1703  k describe po -n argocd-5y1bizr6l8jpmlnh argocd-application-controller-5db57dd6f6-mr2wp
 1704  k describe po -n argocd-5y1bizr6l8jpmlnh argocd-server-78dbd7f8ff-pns9x
 1705  docker pull quay.io/akuity/argocd:v3.0.4-ak.57
 1706  TOKEN="******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" && curl -s -H "Authorization: Bearer $TOKEN" "https://portal-server.akuity-platform/api/v1/orgs/vvy3tdtsq5xvm8ud/argocd/instances/5y1bizr6l8jpmlnh/clusters/4qbcnazuu7f4jp87/manifests" -k | kubectl apply -f -
 1707  git checkout .
 1708  git checout main
 1709  docker pull  docker.io/library/golang:alpine
 1710  git fetch --tags\n
 1711  git checkout tags/v0.22.2
 1712  git checkout -b my-branch-based-on-v0.22.2 tags/v0.22.2\n
 1713  k delete po -n akuity-platform platform-controller-54d4d68df9-k97mk portal-server-686d474bbf-nv2kg
 1714  doceker images rm 265a761757c0 97be8d4e86b3 bf262bd3536a 71926e17edc2 eb6d36b20a72 a61cc89fe632
 1715  docker images rm 265a761757c0 97be8d4e86b3 bf262bd3536a 71926e17edc2 eb6d36b20a72 a61cc89fe632
 1716  docker image rm 265a761757c0 97be8d4e86b3 bf262bd3536a 71926e17edc2 eb6d36b20a72 a61cc89fe632
 1717  docker image rm 265a761757c0 97be8d4e86b3 bf262bd3536a 71926e17edc2 eb6d36b20a72 a61cc89fe632 265a761757c0
 1718  docker pull https://registry-1.docker.io/v2/rancher/mirrored-pause/manifests/3.6
 1719  docker pull registry-1.docker.io/v2/rancher/mirrored-pause/manifests/3.6
 1720  docker pull docker.io/rancher/mirrored-pause:3.6
 1721  k delete po -n kube-system --all
 1722  k get deploy -n kube-system
 1723  k edit deploy -n kube-system coredns
 1724  k describe po -n kube-system rancher/mirrored-coredns-coredns:1.11.3
 1725  k describe po -n kube-system coredns-56f6fc8fd7-m6rvf
 1726  k delete po -n kube-system coredns-56f6fc8fd7-m6rvf
 1727  docker pull rancher/mirrored-pause/manifests/3.6
 1728  k describe po -n kube-system coredns-56f6fc8fd7-jmn9n
 1729  k describe po -n kube-system local-path-provisioner-5cf85fd84d-p7flq
 1730  k delete po -n kube-system coredns-56f6fc8fd7-jmn9n local-path-provisioner-5cf85fd84d-p7flq metrics-server-5985cbc9d7-t7cqf
 1731  k delete po -n kube-system coredns-56f6fc8fd7-jmn9n local-path-provisioner-5cf85fd84d-p7flq metrics-server-5985cbc9d7-t7cqf --force --grace-period 0
 1732  orbctl shell
 1733  k delete po -n kube-system --all --force --grace-period 0
 1734  docker pull rancher/mirrored-pause:3.6
 1735  watch -1 "kubectl describe pod -n kube-system -l k8s-app=kube-dns"
 1736  k delete po -n kube-system --all --force --grace-period 0;kubectl describe pod -n kube-system -l k8s-app=kube-dns
 1737  kubectl get pod -n kube-system -l k8s-app=kube-dns -o
 1738  kubectl get pod -n kube-system -l k8s-app=kube-dns 
 1739  kubectl get pod -n kube-system -l k8s-app=kube-dns
 1740  k get pod -n kube-system -l k8s-app=kube-dns
 1741  k logs -n akuity-platform platform-controller-7f5cd75f69-mdgr6
 1742  k logs -n akuity-platform portaldb-update-52nrc
 1743  k logs -n akuity-platform portaldb-update-xrgkz
 1744  orb -h
 1745  orb --help
 1746  k delete po -n akuity-platform platform-controller-5b984d57b7-4h2lp portal-server-b7d56c8f7-l689h
 1747  history |grep Cust
 1748  history |grep Custo
 1749  history |grep ostumer
 1750  k get po -A-w
 1751  history |grep CursorPro
 1752  mv ~/Downloads/Mac2/CursorPro.app /Applications
 1753  mv ~/Downloads/Mac2/CursorPro.app /Applications chmod +x /Applications/CursorPro.app/Contents/MacOS/CursorPro && sudo xattr -rd com.apple.quarantine /Applications/CursorPro.app
 1754  k3d cluster delete akuity-customer-2
 1755  k3d cluster delete akuity-customer-1
 1756  CUSTOMER=customer1 ./hack/add-customer.sh customer1
 1757  telent 127.0.0.1 7899
 1758  telnet 127.0.0.1 7899
 1759  k3d cluster create --help
 1760  k3d cluster create test --wait
 1761  telnet ************* 7897
 1762  docker exec -it k3d-akuity-$CUSTOMER-server-0 sh
 1763  env | grep -i proxy
 1764  env 
 1765  docker exec -it k3d-test-server-0 sh -c "ip route | grep default"
 1766  ping *************
 1767  docker exec -it k3d-akuity-customer1-serverlb sh -c "ip route | grep default"
 1768  docker exec -it k3d-akuity-registry.localhost sh -c "ip route | grep default"
 1769  k3d cluster delete akuity-customer1
 1770  k3d cluster delete test
 1771  docker image rm rancher/mirrored-pause:3.6 || true
 1772  docker images 7d46a07936af
 1773  docker images 
 1774  docker images |grep 7d46a07936af
 1775  docker exec -it k3d-akuity-customer1-server-0 sh -c "ip route | grep default"\n# 输出：default via ************* dev eth0\n
 1776  curl -x http://*************:7899 https://registry-1.docker.io/v2/\n
 1777  k3d node list
 1778  curl -I http://*************:7899
 1779  ping *************
 1780  telnet ************* 7899
 1781  telnet ************* 7890
 1782  telnet localhsot 7890
 1783  telnet localhost 7890
 1784  telnet localhost 7899
 1785  telnet localhost 7897
 1786  docker exec -it k3d-akuity-customer1-server-0 sh -c "ip route | grep default"
 1787  ping host.k3d.internal
 1788  docker inspect 9759c3c313b8
 1789  k get po -A -owide
 1790  telnet 0.0.0.0 7890
 1791  curl -I --proxy http://host.k3d.internal:7890 https://www.google.com
 1792  docker ps|grep k3d
 1793  sudo ss -tlnp | grep 7890
 1794  curl -x http://*************:7890 http://www.google.com
 1795  curl -x http://127.0.0.1:7890 http://www.google.com
 1796  docker exec -it k3d-akuity-customer1-server-0 sh
 1797  lsof -iTCP:7890 -sTCP:LISTEN\n
 1798  telnet 127.0.0.1 7890
 1799  sudo lsof -nP -i :7890\n
 1800  sudo /usr/libexec/ApplicationFirewall/socketfilterfw --setglobalstate off\n
 1801  docker network inspect k3d-akuity-customer1 | grep Gateway
 1802  docker network inspect k3d-akuity-customer1
 1803  curl -x http://127.0.0.1:7890/ http://www.google.com
 1804  sudo netstat -tlnp | grep 7890
 1805  history |grep "grep 7890"
 1806  netstat -an | grep 7890
 1807  history |grep 7890
 1808  lsof -iTCP:7890 -sTCP:LISTEN
 1809  lsof -nP -i :7890
 1810  docker ps |grep k3d
 1811  kubectl describe pod -n kube-system -l k8s-app=kube-dns
 1812  brew install kind
 1813  kind ?
 1814  kind 
 1815  k3d
 1816  orb stop
 1817  k3d --help
 1818  brew install docker
 1819  open /Applications/Docker.app
 1820  docker version
 1821  docker -h
 1822  docker start
 1823  docker start -h
 1824  rm -rf /usr/local/bin/docker
 1825  suod rm -rf /usr/local/bin/docker
 1826  sudo rm -rf /usr/local/bin/docker
 1827  ls /usr/local/bin/docker-c*
 1828  rm -rf /usr/local/bin/docker-c*
 1829  sudo rm -rf /usr/local/bin/docker-c*
 1830  brew install --cask docker
 1831  /Applications/OrbStack.app/Contents/MacOS/xbin/docker; exit
 1832  Desktop
 1833  vim test.yaml
 1834  mv test.yaml test.conf
 1835  git checkout hack
 1836  ping baidu.com 
 1837  ping bing.com
 1838  ping http://auth-6218a440.wifi.com/
 1839  ping 2.2.2.2
 1840  ping 2.2.2.1
 1841  vim note.txt
 1842  sudo killall -HUP mDNSResponder
 1843  sudo dscacheutil -flushcache; sudo killall -HUP mDNSResponder\n
 1844  nslookup www.apple.com
 1845  nslookup www.baidu.com
 1846  ls /Library/Preferences/SystemConfiguration/com.apple.*.plist
 1847  sudo rm /Library/Preferences/SystemConfiguration/com.apple.*.plist\n
 1848  reboot
 1849  sudo reboot
 1850  open ~/.config/clash
 1851  echo '********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************' |base64 -d
 1852  echo '/*******************************************************************************************************************************************************************************************************************************************************' |base64 -d
 1853  echo '*******************************************************************************************************************************************************************************************************************************************************' |base64 -d
 1854  echo "eyJhZGQiOiJjZ3JvdXAubm9kZTEudi5ub2RlbGlzdC1haXJwb3J0LmNvbSIsInBvcnQiOiI1MDAwMSIsImlkIjoiNWY5ZTVkMDctMTQzOC00MzQ0LWE0NjctYjU3ZDJmYmQ1OTlkIiwiYWx0ZXJJZCI6IjAiLCJ0eXBlIjoiTm9uZSIsIm5ldCI6IndzIiwidGxzIjoiIiwibmFtZSI6IlRva3lvIFYyIC0gQyBHcm91cCJ9" |base64 -d
 1855  echo "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" |base64 -d
 1856  echo "vmess://eyJhZGQiOiJjZ3JvdXAubm9kZTUudi5ub2RlbGlzdC1haXJwb3J0LmNvbSIsInBvcnQiOiI1MDAwMSIsImlkIjoiNWY5ZTVkMDctMTQzOC00MzQ0LWE0NjctYjU3ZDJmYmQ1OTlkIiwiYWx0ZXJJZCI6IjAiLCJ0eXBlIjoiTm9uZSIsIm5ldCI6IndzIiwidGxzIjoiIiwibmFtZSI6IkNhbGlmb3JuaWEgVjIgLSBDIEdyb3VwIn0"|base64 -d
 1857  echo "vmess://eyJhZGQiOiJjZ3JvdXAubm9kZTUudi5ub2RlbGlzdC1haXJwb3J0LmNvbSIsInBvcnQiOiI1MDAwMSIsImlkIjoiNWY5ZTVkMDctMTQzOC00MzQ0LWE0NjctYjU3ZDJmYmQ1OTlkIiwiYWx0ZXJJZCI6IjAiLCJ0eXBlIjoiTm9uZSIsIm5ldCI6IndzIiwidGxzIjoiIiwibmFtZSI6IkNhbGlmb3JuaWEgVjIgLSBDIEdyb3VwIn0="|base64 -d
 1858  echo "vmess://eyJhZGQiOiJjZ3JvdXAubm9kZTQudi5ub2RlbGlzdC1haXJwb3J0LmNvbSIsInBvcnQiOiI1MDAwMSIsImlkIjoiNWY5ZTVkMDctMTQzOC00MzQ0LWE0NjctYjU3ZDJmYmQ1OTlkIiwiYWx0ZXJJZCI6IjAiLCJ0eXBlIjoiTm9uZSIsIm5ldCI6IndzIiwidGxzIjoiIiwibmFtZSI6IkhvbmdLb25nIFYyIC0gQyBHcm91cCJ9"|base64 -d
 1859  ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************e
 1860  orb start
 1861  which docker
 1862  brew uninstall docker
 1863  vim ~/.orbstack/config/docker.json
 1864  docker ps
 1865  golang
 1866  mkdir ClashToSS
 1867  open src.yaml
 1868  go run 
 1869  go mod init test
 1870  echo "vmess://eyJ2IjoiMiIsInBzIjoiQ2FsaWZvcm5pYSBWMiAtIEMgR3JvdXAiLCJhZGQiOiJjZ3JvdXAubm9kZTUudi5ub2RlbGlzdC1haXJwb3J0LmNvbSIsInBvcnQiOiI1MDAwMSIsImlkIjoiNWY5ZTVkMDctMTQzOC00MzQ0LWE0NjctYjU3ZDJmYmQ1OTlkIiwiYWlkIjoiMCIsIm5ldCI6InRjcCIsInR5cGUiOiJub25lIiwiaG9zdCI6IiIsInBhdGgiOiIvIiwidGxzIjoiIn0="|base64 -d
 1871  echo "eyJ2IjoiMiIsInBzIjoiQ2FsaWZvcm5pYSBWMiAtIEMgR3JvdXAiLCJhZGQiOiJjZ3JvdXAubm9kZTUudi5ub2RlbGlzdC1haXJwb3J0LmNvbSIsInBvcnQiOiI1MDAwMSIsImlkIjoiNWY5ZTVkMDctMTQzOC00MzQ0LWE0NjctYjU3ZDJmYmQ1OTlkIiwiYWlkIjoiMCIsIm5ldCI6InRjcCIsInR5cGUiOiJub25lIiwiaG9zdCI6IiIsInBhdGgiOiIvIiwidGxzIjoiIn0="|base64 -d
 1872  echo "eyJhZGQiOiJjZ3JvdXAubm9kZTUudi5ub2RlbGlzdC1haXJwb3J0LmNvbSIsInBvcnQiOiI1MDAwMSIsImlkIjoiNWY5ZTVkMDctMTQzOC00MzQ0LWE0NjctYjU3ZDJmYmQ1OTlkIiwiYWx0ZXJJZCI6IjAiLCJ0eXBlIjoiTm9uZSIsIm5ldCI6IndzIiwidGxzIjoiIiwibmFtZSI6IkNhbGlmb3JuaWEgVjIgLSBDIEdyb3VwIn0=" |base64 -d
 1873  echo "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"|base64 -d
 1874  ./convert 
 1875  echo "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"|base64 -d
 1876  echo "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"|base64 -d
 1877  echo "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"|base64 -d
 1878  echo "******************************************************************************************************************************************************************************************************************************************************" |base64 -d
 1879  echo "YWVzLTI1Ni1nY206c2hFdlpRUUJTNGJSZTh3RkA2Ni4xMTIuMjExLjE2OToxODE2Nw#<EMAIL>"|base64 -d
 1880  go build -o convert main.go
 1881  ./convert |base64 -d
 1882  ping cgroup.node5.s.nodelist-airport.com
 1883  ping cgroup.node2.s.nodelist-airport.com
 1884  ping cgroup.node3.v.nodelist-airport.com
 1885  cd p
 1886  git rebase origin/main
 1887  git rebase origin/main main
 1888  k logs -n akuity-platform portal-server-67477d546f-v7nch
 1889  k logs -n akuity-platform platform-controller-7f96d6c95-c9xgm
 1890  k logs -n akuity-platform platform-controller-7f96d6c95-c9xgm |grep error
 1891  cd FileServer
 1892  cd file-sharing-service
 1893  mkdir stock
 1894  python3 -m venv myenv
 1895  source myenv/bin/activate 
 1896  pip install yfinance matplotlib pandas numpy\n
 1897  rm test.py
 1898  git commit -m "add permisson control" -s
 1899  git pull origin kargo-extension-setting --rebase
 1900  git reset HEAD^8a52a67fe1917539619e7aa1717f5134cb92d9e5
 1901  git rebase -i origin/main
 1902  git rebase -i origin/main --reapply-cherry-picks
 1903  git push origin kargo-extension-setting --force
 1904  git checkout kargo-extension-setting
 1905  ➜  kubevision-scripts git:(main) ✗ ./refresh-akp.sh 
 1906  git commit -m "modify the condition of AiSreEnabled" -s
 1907  git commit -m "remove optional from akuityIntelligence" -s
 1908  cd FilesShareServer
 1909  cat vpn.yaml
 1910  cd ../..
 1911  ls
 1912  cd golang
 1913  ls
 1914  ak
 1915  cd akuity-platform
 1916  git checkout main
 1917  git branch
 1918  git branch -D kargo-extension-setting
 1919  git branch
 1920  git branch -D vaildate-suggested-contexts
 1921  git branch
 1922  git branch -D my-branch-based-on-v0.22.2
 1923  git branch
 1924  git pull
 1925  git status
 1926  git pull
 1927  git log
 1928  cd ../kubevision-scripts
 1929  UI=true ./refresh-akp.sh
 1930  clear
 1931  UI=true ./refresh-akp.sh
 1932  k get po -A
 1933  k get po -n akuity-platform
 1934  k logs -n akuity-platform portal-server-d975bf7bb-czgmz
 1935  k get po -n akuity-platform
 1936  k delete po -n akuity-platform --all
 1937  k get po -n akuity-platform
 1938  kx
 1939  k get po -A
 1940  TOKEN="eyJhbGciOiJSUzI1NiIsImtpZCI6ImIyNzlhZDQwOWVjOTIzZTI4YTc1NjI2NTllZWUyZjExZWNkZjk4YzIifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.gmA5Yt6ARcdAnOXZDviTnd3VJWKdbr_Q8G24Gx_y2mupoqp5GSHsAUOyVh3ZaI5t1G24lO4i2Eq4Xmy6c8-UZSxfhvxa-g1PW_FgJ6pwzM9NHCqvocum1drFhxSYUR9b_YnIpg9pi0V0g79m5bbulfbC06yGulXVB3JmaTgWoFNVMy3JUsMpivrzOaXAGn8KUR9JXbyDN1uMi8MjtEfuYByMOjkZedZQKAjMKXQieMPppmTYx0HEZvSWdEcaV2XdlDQ6xSSbuLKdo7jinQLoJZvES3Id2DFD87iVYONs1CJSP7rCVvJtNVS6OJhVzvFaw7T4O42siuLxSUBqjXCCCg" && curl -s -H "Authorization: Bearer $TOKEN" "https://portal-server.akuity-platform/api/v1/orgs/jhgyp72hy7aohk55/argocd/instances/k7o2nfd04mzglvnw/clusters/6j3hhunad23ltdwo/manifests" -k | kubectl apply -f -
 1941  k get po -A
 1942  k get po -A -w
 1943  k get po -A
 1944  k get po -A -w
 1945  k describe po -n akuity argocd-repo-server-7db46477f8-8cftz
 1946  k get po -A
 1947  k delete po -n akuity argocd-repo-server-7db46477f8-8cftz argocd-repo-server-7db46477f8-xbwlk argocd-notifications-controller-6bf7c4689d-cnj5k
 1948  k get po -A
 1949  k describe po -n akuity argocd-repo-server-7db46477f8-8cftz
 1950  k describe po -n akuity argocd-repo-server-7db46477f8-wnxp7
 1951  k describe po -n akuity argocd-repo-server-7db46477f8-s2x4p
 1952  k get po -A
 1953  k get deploy -n akuity
 1954  k delete ns akuity --force --grace-periods 0
 1955  k delete ns akuity --force --grace-period 0
 1956  k get po -A
 1957  TOKEN="eyJhbGciOiJSUzI1NiIsImtpZCI6ImIyNzlhZDQwOWVjOTIzZTI4YTc1NjI2NTllZWUyZjExZWNkZjk4YzIifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.gmA5Yt6ARcdAnOXZDviTnd3VJWKdbr_Q8G24Gx_y2mupoqp5GSHsAUOyVh3ZaI5t1G24lO4i2Eq4Xmy6c8-UZSxfhvxa-g1PW_FgJ6pwzM9NHCqvocum1drFhxSYUR9b_YnIpg9pi0V0g79m5bbulfbC06yGulXVB3JmaTgWoFNVMy3JUsMpivrzOaXAGn8KUR9JXbyDN1uMi8MjtEfuYByMOjkZedZQKAjMKXQieMPppmTYx0HEZvSWdEcaV2XdlDQ6xSSbuLKdo7jinQLoJZvES3Id2DFD87iVYONs1CJSP7rCVvJtNVS6OJhVzvFaw7T4O42siuLxSUBqjXCCCg" && curl -s -H "Authorization: Bearer $TOKEN" "https://portal-server.akuity-platform/api/v1/orgs/jhgyp72hy7aohk55/argocd/instances/k7o2nfd04mzglvnw/clusters/1pg2t43fxk38faah/manifests" -k | kubectl apply -f -
 1958  k get po - A
 1959  k get po -A
 1960  kx
 1961  k get po -A
 1962  ./stop-akp.sh
 1963  k get po -A
 1964  ./start-akp.sh 1
 1965  sudo vim /etc/hosts
 1966  k get po -A
 1967  k describe po -n argocd-r1zfq4wuf90ltp1p argocd-server-57cbb4c5cb-76xch
 1968  k get po -A
 1969  k get po -A -w
 1970  k get po -A 
 1971  kx
 1972  k get po -A
 1973  TOKEN="eyJhbGciOiJSUzI1NiIsImtpZCI6Ijg3MDY3ODEyMzU3ZjZkZGY0M2ZmY2NkMzUzZDI3MzAzMDVhOTNlYjYifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.IhCMBWIYYj-TYxc3ajODBtAf9nYTGcsD3qhn8tUR0zd6tfhANYisLIXfF6g0F3yjcAMWi6MQxy8Z5QF5wWohklovvZ3XuddeIUCa8usVDItRN8s_IJfndz7BS4O06m2HP3-N-jmPkN5tFRGWuntFSZusO0oG5_QPtwGisu8NEPpCdQiop5-VG49J5fL_pO8rDQd9CxqZLxqheFZEQPZ6ft-d2sciI9sh72CL7kLW1LRYpPh_wljQRGBgaZeuk4odeNgRblJaZEGKgBmMOcejRykpVtDJhw1ick8hTyp_EOTqS7LRkcOKs7CXmXISj1X9JLZ5w6EKTaYbyhJxgl5afw" && curl -s -H "Authorization: Bearer $TOKEN" "https://portal-server.akuity-platform/api/v1/orgs/jhjc7qa0afi3esut/argocd/instances/r1zfq4wuf90ltp1p/clusters/h6denee5iyyov216/manifests" -k | kubectl apply -f -
 1974  k get po -A
 1975  kx
 1976  k get po -n akuity-platform
 1977  k logs -n akuity-platform portal-server-56d87d9d8b-5q9qr
 1978  k logs -n akuity-platform portal-server-56d87d9d8b-5q9qr |grep error
 1979  k logs -n akuity-platform portal-server-56d87d9d8b-5q9qr -f |grep error 
 1980  k get po -n akuity-platform
 1981  k logs -n akuity-platform What is Ceph?
 1982  k logs -n akuity-platform platform-controller-5d9f4645f6-96nwt
 1983  k logs -n akuity-platform platform-controller-5d9f4645f6-96nwt |grep error
 1984  k logs -n akuity-platform platform-controller-5d9f4645f6-96nwt -f |grep error
 1985  git status
 1986  git checkout -b fix-auth
 1987  git checkout main
 1988  git pull
 1989  git status
 1990  git branch
 1991  git branch -D username
 1992  git branch
 1993  git branch -D fix-auth
 1994  git checkout fix-auth
 1995  git stsatus
 1996  git status
 1997  git checkout -b fix-auth
 1998  git status
 1999  git statsus
 2000  git status
 2001  git add internal
 2002  git commit -m "fix authentication issues" -s
 2003  git status
 2004  git push
 2005  git push --set-upstream origin fix-auth
 2006  git status
 2007  git checkout main
 2008  git status
 2009  k9s
 2010  git branch
 2011  git checkout fix-auth
 2012  cd ../kubevision-scripts
 2013  ./refresh-akp.sh
 2014  cd ..
 2015  cd akuity-platform
 2016  ls
 2017  grep InvalidArgument -rnw ./*
 2018  grep InvalidArgument -rnw ./* |grep provider
 2019  grep InvalidArgument -rnw ./ |grep provider
 2020  grep InvalidArgument -rnw ./ 
 2021  grep InvalidArgument -rnw ./* 
 2022  ➜  akuity-platform git:(fix-auth) grep InvalidArgument -rnw ./               
 2023  ➜  akuity-platform git:(fix-auth) grep InvalidArgument -rnw ./               |grep provider
 2024  ➜  akuity-platform git:(fix-auth) grep InvalidArgument -rnw ./     
 2025  ➜  akuity-platform git:(fix-auth) grep InvalidArgument -rnw ./* |grep provider
 2026  kubectl -n akuity-platform edit secret akuity-platform
 2027  k get po -n akuity-platform
 2028  k delete po -n akuity-platform platform-controller-5bcc5f55dc-8gnsq portal-server-6c6dc8d457-5blt5
 2029  clear
 2030  git status
 2031  git diff
 2032  git checkout main
 2033  git status
 2034  git pull
 2035  git status
 2036  git checkout -b feedback-comment
 2037  make generate
 2038  git status
 2039  git diff
 2040  ➜  akuity-platform git:(feedback-comment) ✗ git diff
 2041  ➜  akuity-platform git:(feedback-comment) ✗ clear
 2042  cd models
 2043  make generate
 2044  git status
 2045  pwd
 2046  cd ..
 2047  git status
 2048  git add aims api docs internal models pkg portal
 2049  git status
 2050  git commit -m "add feedback comments" -s
 2051  git status
 2052  git push
 2053  git push --set-upstream origin feedback-comment
 2054  cd ../kubevision-scripts
 2055  ls
 2056  UI=true ./refresh-akp.sh
 2057  ➜  kubevision-scripts git:(main) ✗ UI=true ./refresh-akp.sh
 2058  +++ dirname ./refresh-akp.sh
 2059  ++ SCRIPT_DIR=.
 2060  ++ '[' -f ./.env ']'
 2061  ++ source ./.env
 2062  +++ AKUITY_ROOT=/Users/<USER>/MyPro/akuity
 2063  +++ AKP_ROOT=/Users/<USER>/MyPro/akuity/akuity-platform
 2064  +++ AGENT_ROOT=/Users/<USER>/MyPro/akuity/agent
 2065  +++ AKP_IMAGE_REPO=us-docker.pkg.dev/akuity/akp/akuity-platform
 2066  +++ AGENT_SERVER_IMAGE_REPO=us-docker.pkg.dev/akuity/akp/agent-server
 2067  cd ..
 2068  ls
 2069  cd akuity-platform
 2070  cd ../kubevision-scripts
 2071  ➜  kubevision-scripts git:(main) ✗ UI=true ./refresh-akp.sh
 2072  ls
 2073  exit
 2074  ➜  kubevision-scripts git:(main) ✗ UI=true ./refresh-akp.sh
 2075  ➜ UI=true ./refresh-akp.sh
 2076  ls
 2077  ➜ UI=true ./refresh-akp.sh
 2078  ls
 2079  cd ../
 2080  cd akuity-platform
 2081  ls
 2082  cd models
 2083  make generate
 2084  cd ..
 2085  make generate
 2086  cd ../kubevision-scripts
 2087  ls
 2088  UI=true ./refresh-akp.sh
 2089  cd ../
 2090  ls
 2091  cd akuity-platform
 2092  ls
 2093  make generate
 2094  cd ../kubevision-scripts
 2095  ls
 2096  UI=true ./refresh-akp.sh
 2097  ak
 2098  ls
 2099  cd akuity-platform
 2100  ls
 2101  git status
 2102  git diff
 2103  git status
 2104  git add aims api pkg portal
 2105  git status
 2106  git log
 2107  git status
 2108  git commit -m "remove opt from comment" -s 
 2109  git status
 2110  git push
 2111  git status
 2112  cd ..
 2113  cd akuity-platform
 2114  git status
 2115  git diff
 2116  git add internal
 2117  git commit -m "tmp" -s
 2118  git checkout main
 2119  git pull
 2120  cd ../akuity-platform
 2121  cd ../kubevision-scripts
 2122  ./refresh-akp.sh
 2123  k get po -A
 2124  UI=true ./refresh-akp.sh
 2125  cd /private/var/folders/mc/lw2530px5r93s78qx6b61fzw0000gn/T/
 2126  ls
 2127  cd mingqiu-cursor-zsh
 2128  ls
 2129  cd ..
 2130  ls
 2131  cd mingqiu-cursor-zsh
 2132  ls
 2133  cd ..
 2134  k get po -A
 2135  cd ../akuity-platform
 2136  git pull
 2137  cd ../kubevision-scripts
 2138  UI=true ./refresh-akp.sh
 2139  clear
 2140  k get po -A
 2141  git branch
 2142  git checkout feedback-comment
 2143  ls
 2144  make generate
 2145  git stuatus
 2146  git status
 2147  git checkout aims/ui/src/lib/apiclient/organization/v1/organization_pb.ts portal/ui/src/lib/apiclient/organization/v1/organization_pb.ts   pkg/api/gen/organization/v1/organization_grpc.pb.go pkg/api/gen/organization/v1/organization.pb.gw.go pkg/api/gen/organization/v1/organization.pb.go pkg/api/gen/organization/v1/organization.gw.client.go models/models/ai_conversation.gen.go  aims/ui/src/lib/apiclient/organization/v1/organization_pb.ts
 2148  git status
 2149  make generate
 2150  git status
 2151  cd models
 2152  make generate
 2153  cd ..
 2154  git status
 2155  cd ..
 2156  cd akuity-platform
 2157  ls
 2158  cd ../
 2159  cd ..
 2160  cd MyPro/SlackUp
 2161  ./SlackUp
 2162  clear
 2163  git status
 2164  cd models
 2165  make generate
 2166  cd ..
 2167  git status
 2168  ➜  akuity-platform git:(feedback-comment) ✗ git status
 2169  On branch feedback-comment
 2170  Your branch is ahead of 'origin/feedback-comment' by 1 commit.
 2171  Changes not staged for commit:
 2172  git status
 2173  git add aims api docs internal models pkg portal internal
 2174  git status
 2175  git push
 2176  git status
 2177  git log
 2178  git commit --amend
 2179  git status
 2180  git push --force
 2181  ls
 2182  cd ../kubevision-scripts
 2183  ./refresh-akp.sh
 2184  UI=true ./refresh-akp.sh
 2185  cd ..
 2186  cd akuity-platform
 2187  make generate
 2188  git status
 2189  make generate
 2190  git status
 2191  make format
 2192  git status
 2193  git diff
 2194  git status
 2195  git add internal
 2196  git status
 2197  git commit -m "fix linter error" -s
 2198  git push 
 2199  clear
 2200  ls
 2201  cd ../kubevision-scripts
 2202  ls
 2203  UI=true ./refresh-akp.sh
 2204  echo "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"|base64 -d
 2205  echo "YWVzLTI1Ni1nY206c2hFdlpRUUJTNGJSZTh3RkAxMzguMTI4LjIyMS4yMToxOTIwOQ"|base64 -d
 2206  echo "******************************************************************************************************************************************************************************************************************************************************"|base64 -d
 2207  echo "******************************************************************************************************************************************************************************************************************************************************"|base64 -d
 2208  echo "****************************************************************************************************************************************************************************************************************************************************"|base64 -d
 2209  echo "********************************************************************************************************************************************************************************************************************************************************"|base64 -d
 2210  cd MyPro/python
 2211  ls
 2212  ls ~/.config/clash
 2213  python3 sharefile.py --dir ~/Desktop
 2214  ssh root@138.128.221.21
 2215  git status
 2216  brew services start mongodb/brew/mongodb-community
 2217  cd src/backend
 2218  pnpm start
 2219  MyPro
 2220  ls
 2221  cd python
 2222  ls
 2223  python3 sharefile.py --dir ~/Desktop
 2224  k get po -A
 2225  clear
 2226  ls
 2227  MyPro
 2228  ls
 2229  cd akuity
 2230  ls
 2231  cd akuity-platform
 2232  ls
 2233  git statsu
 2234  git status
 2235  git fetch origin
 2236  git merge origin/main
 2237  git status
 2238  make generate
 2239  make generate-in-container
 2240  git status
 2241  git add pkg
 2242  git status
 2243  git commit
 2244  git push 
 2245  clear
 2246  sudo vim /etc/hosts
 2247  git status
 2248  git checkout main
 2249  git log
 2250  git status
 2251  ls
 2252  UI=true ./refresh-akp.sh
 2253  cd ../kubevision-scripts
 2254  UI=true ./refresh-akp.sh
 2255  git status
 2256  cd ../akuity-platform
 2257  git status
 2258  la
 2259  git status
 2260  make generate-in-container
 2261  git status
 2262  cd ../kubevision-scripts
 2263  UI=true ./refresh-akp.sh
 2264  make generate-in-container
 2265  cd ../akuity-platform
 2266  make generate-in-container
 2267  git status
 2268  cd ../kubevision-scripts
 2269  ./refresh-akp.sh
 2270  k9s
 2271  k get po -n akuity-platform
 2272  k logs -n akuity-platform portal-server-77cbdc944b-mdr8p
 2273  cd ../kubevision-scripts
 2274  ./refresh-akp.sh
 2275  k get po -A
 2276  k logs -n akuity-platform portal-server-5bb7c9d547-894ff
 2277  ./refresh-akp.sh
 2278  k get po -A
 2279  k logs -n akuity-platform portal-server-5bb7c9d547-2ntx4
 2280  k logs -n akuity-platform portal-server-d45c544d7-2ntx4
 2281  k logs -n akuity-platform portal-server-d45c544d7-2ntx4 -f
 2282  ./refresh-akp.sh
 2283  k get po -A
 2284  k logs -n akuity-platform portal-server-5f7fd58445-c22qn
 2285  k logs -n akuity-platform portal-server-5f7fd58445-c22qn -f
 2286  cd ..
 2287  cd akuity-platform
 2288  git status
 2289  k get svc -A
 2290  kx
 2291  k get svc -A
 2292  ll
 2293  clear
 2294  pwd
 2295  ls
 2296  cat a.yaml
 2297  clear
 2298  cd portal/ui
 2299  find . -name "package.json"
 2300  cat package.json
 2301  git status
 2302  cd ..
 2303  ls
 2304  cd ..
 2305  ls
 2306  git status
 2307  git add aims
 2308  git add api
 2309  git add docs internal pkg portal
 2310  git status
 2311  git commit -m "add trew view filter" -s
 2312  git log
 2313  git branch
 2314  git checkout feedback-comment
 2315  git status
 2316  git add internal
 2317  git status
 2318  git log
 2319  git commit -m "limit feedback size and length" -s
 2320  git status
 2321  git push
 2322  cd ../kubevision-scripts
 2323  ./refresh-akp.sh
 2324  ak
 2325  ls
 2326  d akuity-platform
 2327  ls
 2328  k get po -A
 2329  kx
 2330  k get po -A
 2331  kx
 2332  k get po -A
 2333  kx
 2334  k get po - A
 2335  k get po -A
 2336  k9s
 2337  clear
 2338  ls
 2339  cat install.sh
 2340  cd ..
 2341  cd akuity-platform
 2342  git branch
 2343  git checkout main
 2344  git log
 2345  git checkout -b tree-view-filter
 2346  git checkout main
 2347  git reset HEAD^ --hard
 2348  git pull
 2349  git branch
 2350  git branch -D fix-auth
 2351  git status
 2352  git branch
 2353  git checkout tree-view-filter
 2354  git status
 2355  clear
 2356  ls
 2357  cd ../kubevision-scripts
 2358  ls
 2359  ./refresh-akp.sh
 2360  k get po -n akuity-platform
 2361  pwd
 2362  cd ../kubevision-scripts
 2363  ./refresh-akp.sh
 2364  kx
 2365  k get po -A
 2366  cd ..
 2367  cd akuity-platform
 2368  git status
 2369  ls
 2370  cd ..
 2371  ls
 2372  cd kubevision-scripts
 2373  UI=true ./refresh-akp.sh
 2374  k get po -A
 2375  kx
 2376  k get po -A
 2377  k describe po -n akuity akuity-agent-57c4d749dd-7tp5h
 2378  k get po -A
 2379  k describe po -n akuity akuity-agent-57c4d749dd-m7ndb
 2380  k get po -A
 2381  k delete po -n akuity akuity-agent-57c4d749dd-7tp5h akuity-agent-57c4d749dd-m7ndb
 2382  k get po -A
 2383  k describe po -n akuity akuity-agent-57c4d749dd-2jmft
 2384  UI=true ./refresh-akp.sh
 2385  AKUITY_SERVER_URL=http://localhost:3001 pnpm run dev 
 2386  cd portal/ui
 2387  AKUITY_SERVER_URL=http://localhost:3001 pnpm run dev 
 2388  docker pull us-docker.pkg.dev/akuity/docker-io/curlimages/curl:8.11.1
 2389  git status
 2390  cd ..
 2391  cd akuity-platform
 2392  git status
 2393  ./refresh-akp.sh
 2394  k9s
 2395  ./refresh-akp.sh
 2396  cd ../
 2397  ls
 2398  cd akuity-platform
 2399  ls
 2400  UI=true ./refresh-akp.sh
 2401  cd ../kubevision-scripts
 2402  UI=true ./refresh-akp.sh
 2403  history |grep pnpm 
 2404  cd akuity-platform
 2405  ls
 2406  cd portal/ui
 2407  PORTAL_SERVER_URL=https://portal-server.akuity-platform pnpm run dev
 2408  git status
 2409  ./refresh-akp.sh
 2410  k get po -n akuity-platform
 2411  kx
 2412  k get po -n akuity-platform
 2413  kx
 2414  k get po -n akuity-platform
 2415  k logs -n akuity-platform portal-server-cb5584fd5-njh6m
 2416  ./refresh-akp.sh
 2417  k get po -n akuity-platform
 2418  k logs -n akuity-platform portal-server-cb5584fd5-njh6m
 2419  k logs -n akuity-platform portal-server-8c7448f68-47ljr
 2420  k logs -n akuity-platform portal-server-8c7448f68-47ljr -f
 2421  ./refresh-akp.sh
 2422  k get po -n akuity-platform
 2423  k logs -n akuity-platform portal-server-74f579f7bf-c2dhd
 2424  k logs -n akuity-platform portal-server-74f579f7bf-c2dhd -f
 2425  cd ../
 2426  ls
 2427  cd akuity-platform
 2428  ls
 2429  clear
 2430  git status
 2431  cd ../kubevision-scripts
 2432  ./refresh-akp.sh
 2433  k get po -n akuity-platform
 2434  k logs -n akuity-platform portal-server-cb894bf64-g2f9x
 2435  k logs -n akuity-platform portal-server-cb894bf64-g2f9x -f
 2436  ./refresh-akp.sh
 2437  k get po -n akuity-platform
 2438  k logs -n akuity-platform portal-server-6d9cb7f445-5hz2z
 2439  k logs -n akuity-platform portal-server-6d9cb7f445-5hz2z -f
 2440  ./refresh-akp.sh
 2441  k get po -n akuity-platform
 2442  k logs -n akuity-platform portal-server-855d9d6776-rm9mj
 2443  k logs -n akuity-platform portal-server-855d9d6776-rm9mj -f
 2444  ./refresh-akp.sh
 2445  k get po -n akuity-platform
 2446  k logs -n akuity-platform portal-server-cd4b7cd79-kljqv
 2447  k logs -n akuity-platform portal-server-cd4b7cd79-kljqv -f
 2448  git status
 2449  cd ../akuity-platform
 2450  ls
 2451  git status
 2452  git diff internal/services/k8sresource/treeview.go
 2453  git checkout internal/services/k8sresource/treeview.go
 2454  cd ../kubevision-scripts
 2455  ./refresh-akp.sh
 2456  k get po -n akuity-platform
 2457  k logs -n akuity-platform portal-server-57b8c8c8b5-ldvng -f
 2458  ./refresh-akp.sh
 2459  k get po -n akuity-platform
 2460  k logs -n akuity-platform portal-server-67f5976cdf-trdvf -f
 2461  ./refresh-akp.sh
 2462  k get po -n akuity-platform
 2463  k logs -n akuity-platform portal-server-564df4b67c-zzsng -f
 2464  git status
 2465  cd ../akuity-platform
 2466  git status
 2467  git checkout  internal/services/k8sresource/treeview.go
 2468  ./refresh-akp.sh
 2469  cd ../kubevision-scripts
 2470  ./refresh-akp.sh
 2471  k get po -n akuity-platform
 2472  k logs -n akuity-platform portal-server-68dcf596c-dt2lc -f
 2473  git status
 2474  cd ..
 2475  cd akuity-platform
 2476  git status
 2477  git diff  internal/services/k8sresource/treeview.go
 2478  git diff  internal/services/k8sresource/treeview_reference_resource.sql
 2479  cd ../kubevision-scripts
 2480  ./refresh-akp.sh
 2481  k get po -n akuity-platform
 2482  vim portal-server-log.sh
 2483  sh portal-server-log.sh
 2484  cd ../kubevision-scripts
 2485  sh refresh-akp.sh
 2486  sh portal-server-log.sh
 2487  k get po -n akuity-platform
 2488  sh portal-server-log.sh
 2489  k get po -n akuity-platform
 2490  sh portal-server-log.sh
 2491  UI=true ./refresh-akp.sh
 2492  cat portal-server-log.sh
 2493  NAMESPACE="akuity-platform"
 2494  POD_PREFIX="portal-server"
 2495  POD_NAME=$(kubectl get pods -n ${NAMESPACE} --no-headers -o custom-columns=":metadata.name,:status.phase" | grep "${POD_PREFIX}" | grep "Running" | head -n 1 | awk '{print $1}')
 2496  kubectl logs -n ${NAMESPACE} ${POD_NAME} -f
 2497  POD_NAME=$(kubectl get pods -n ${NAMESPACE} --no-headers -o custom-columns=":metadata.name,:status.phase" | grep "${POD_PREFIX}" | grep "Running" | head -n 1 | awk '{print $1}')
 2498  kubectl logs -n ${NAMESPACE} ${POD_NAME} -f
 2499  ls
 2500  cd akuity-platform
 2501  make generate-in-container
 2502  git status
 2503  git diff portal
 2504  clear
 2505  cd ..
 2506  ls
 2507  cd ..
 2508  ls
 2509  sh portal-server-log.sh
 2510  ./refresh-akp.sh 
 2511  sh portal-server-log.sh
 2512  cd ..
 2513  cd akuity-platform
 2514  git statuw
 2515  git status
 2516  git add internal portal
 2517  git status
 2518  git commit -m "tmp" -s
 2519  git checkout main
 2520  cd ../
 2521  cd kubevision-scripts
 2522  ./refresh-akp.sh
 2523  sh portal-server-log.sh
 2524  k get po -A
 2525  k get po -n akuity-platform
 2526  sh portal-server-log.sh
 2527  k get po -n akuity-platform
 2528  sh portal-server-log.sh
 2529  cd ..
 2530  ls
 2531  cd akuity-platform
 2532  git branch
 2533  git checkout tree-view-filter
 2534  cd ../kubevision-scripts
 2535  ./refresh-akp.sh
 2536  cd ..
 2537  cd akuity-platform
 2538  git status
 2539  git diff
 2540  git status
 2541  cd ../kubevision-scripts
 2542  ./refresh-akp.sh
 2543  sh portal-server-log.sh
 2544  k get po -n akuity-platform
 2545  sh portal-server-log.sh
 2546  ls
 2547  cat portal-server-log.sh
 2548  sh portal-server-log.sh
 2549  PORTAL_SERVER_URL=https://portal-server.akuity-platform pnpm run dev
 2550  sh portal-server-log.sh
 2551  cat portal-server-log.sh
 2552  sh portal-server-log.sh
 2553  ./refresh-akp.sh
 2554  ./refresh-akp.sh
 2555  sh portal-server-log.sh
 2556  k get po -n akuity-platform
 2557  sh portal-server-log.sh
 2558  git status
 2559  cd ../akuity-platform
 2560  git status
 2561  git diff portal/ui/src/feature/kubevision/components/kubevision-explorer-dashboard/kubevision-explorer-filter-bar.tsx
 2562  git diff portal/ui/src/feature/kubevision/components/kubevision-explorer-dashboard/kubevision-explorer-filter.tsx
 2563  cd ../kubevision-scripts
 2564  ./refresh-akp.sh
 2565  ls
 2566  sh portal-server-log.sh
 2567  ./refresh-akp.sh
 2568  sh portal-server-log.sh
 2569  k get po -A
 2570  sh portal-server-log.sh
 2571  ls
 2572  curl -fsSL https://raw.githubusercontent.com/yeongpin/cursor-free-vip/main/scripts/install.sh -o install.sh && chmod +x install.sh && ./install.sh
 2573  MyPro/SlackUp
 2574  ls
 2575  ./SlackUp
 2576  sudo curl -fsSL https://raw.githubusercontent.com/yeongpin/cursor-free-vip/main/scripts/install.sh -o install.sh && chmod +x install.sh && ./install.sh
 2577  ls
 2578  cd ~/Downloads
 2579  ls
 2580  ./CursorFreeVIP_1.11.03_mac_arm64
 2581  ls
 2582  sudo ./CursorFreeVIP_1.11.03_mac_arm64
 2583  mkdir ~/Desktop/cursor-patch\ncp "/Applications/Cursor.app/Contents/Resources/app/out/main.js" ~/Desktop/cursor-patch/main.js
 2584  code ~/Desktop/cursor-patch/main.js
 2585  cd ~/Desktop/cursor-patch
 2586  ls
 2587  open .
 2588  cp ~/Desktop/cursor-patch/main.js /Applications/Cursor.app/Contents/Resources/app/out
 2589  sudo cp ~/Desktop/cursor-patch/main.js /Applications/Cursor.app/Contents/Resources/app/out
 2590  ./SlackUp
 2591  ls
 2592  cd MyPro
 2593  ls
 2594  cd ~/Downloads
 2595  ls
 2596  sudo ./CursorFreeVIP_1.11.03_mac_arm64
 2597  exit
 2598  ls
 2599  cd Downloads
 2600  ls
 2601  sudo ./CursorFreeVIP_1.11.03_mac_arm64
 2602  open chrome
 2603  which Chrome
 2604  sudo chmod -R 755 ~/Library/Application\ Support/Google/Chrome
 2605  sudo chown -R $(whoami) ~/Library/Application\ Support/Google/Chrome
 2606  ls
 2607  cd MyPro
 2608  cd akuity
 2609  ls
 2610  sh portal-server-log.sh
 2611  cd  akuity-platform
 2612  ls
 2613  cd portal/ui
 2614  ls
 2615  PORTAL_SERVER_URL=https://portal-server.akuity-platform pnpm run dev
 2616  cd ..
 2617  ls
 2618  cd akuity-platform
 2619  cd ../kubevision-scripts
 2620  ls
 2621  ./refresh-akp.sh
 2622  cd ..
 2623  ls
 2624  cd ..
 2625  ls
 2626  cd ..
 2627  sh portal-server-log.sh
 2628  ls
 2629  ./refresh-akp.sh
 2630  ls
 2631  ./portal-server-log.sh
 2632  sh portal-server-log.sh
 2633  cd Downloads
 2634  ls
 2635  sudo ./CursorFreeVIP_1.11.03_mac_arm64
 2636  chmod +x /Applications/CursorPro.app/Contents/MacOS/CursorPro && sudo xattr -rd com.apple.quarantine /Applications/CursorPro.app
 2637  ls
 2638  cd MyPro/SlackUp
 2639  ls
 2640  ./SlackUp
 2641  git status
 2642  ls
 2643  cd ..
 2644  cd ../..
 2645  cd kubevision-scripts
 2646  ls
 2647  kx
 2648  k3d cluster delete akuity-customer1; CUSTOMER=customer1 ./hack/add-customer.sh customer1
 2649  cd ../..
 2650  cd akuity
 2651  cd akuity-platform
 2652  k3d cluster delete akuity-customer1; CUSTOMER=customer1 ./hack/add-customer.sh customer1
 2653  k get po A
 2654  k get po -A
 2655  k x
 2656  kx
 2657  k get po -A
 2658  ls
 2659  cd ../kubevision-scripts
 2660  ./stop-akp.sh 1
 2661  1;5u
 2662  kx
 2663  k get po -A
 2664  k describe po -n kube-system coredns-56f6fc8fd7-z4rpc
 2665  k get po -A
 2666  k describe po -n kube-system coredns-56f6fc8fd7-z4rpc
 2667  ls
 2668  k describe po -n kube-system coredns-56f6fc8fd7-z4rpc
 2669  k get po -A
 2670  ls
 2671  ./stop-akp.sh
 2672  kx
 2673  k get po -A
 2674  ls
 2675  UI=true ./refresh-akp.sh
 2676  ls
 2677  kx
 2678  k get po -A
 2679  CUSTOMER=customer ./hack/add-customer.sh customer
 2680  cd ../akuity-platform
 2681  CUSTOMER=customer ./hack/add-customer.sh customer
 2682  k get po -A
 2683  kx
 2684  k get po -A
 2685  kx
 2686  k get po -A
 2687  kubectl config delete-context k3d-akuity-customer1
 2688  kubectl config delete-context k3d-akuity-customer
 2689  kx 
 2690  k get po -A
 2691  CUSTOMER=customer ./hack/add-customer.sh customer
 2692  k get po -A
 2693  kx
 2694  k get po -A
 2695  ls
 2696  kubectl config delete-context k3d-akuity-customer
 2697  history >> a.log
 2698  vim a.log
 2699  cd ../kubevision-scripts
 2700  ls
 2701  ./start-akp.sh 2
 2702  kx
 2703  k get po -A
 2704  k get po -A -w
 2705  k get po -A
 2706  kx
 2707  k get po -A
 2708  k get po -A -w
 2709  k get po -A
 2710  k get po -A -w
 2711  k get po -A 
 2712  kx
 2713  k get po -A
 2714  TOKEN="eyJhbGciOiJSUzI1NiIsImtpZCI6IjM4ZTBhMmNkNGE0MTA5NGE2NDkzMDc4OGI1ZGY0NTcyN2JjZWFiOTgifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.vOprLRwhpphybsHr2wstO5VDo1oZewLG361MwHjnHvmQZh24Bz83U69YYMG0o8VrtP2bPA3MdkKXZtcsBGEkC74swDjuia4esDKLeVtadJIM0ujXaA6Z2Jf7QE88hr-4JOi_QqG-NZiXkaETAANVgR3ABUzr-xymAZFyQm122wtNYBtQudJW902WIQbKkD8P4V3imp7MPatctpuu28grs8XfF3822oiwJCi_cXy-BdvaapNZaI3FGCZ_UPEeS64WLp6P5MhFWqAswYcvn7TKWkXbh2_4YdgX8_mT8SR39V0GeIc_tjBz608yvsFm9uhLle8Gi33642zKRGd-QcBuig" && curl -s -H "Authorization: Bearer $TOKEN" "https://portal-server.akuity-platform/api/v1/orgs/llwxnuoz1gnqhzrx/argocd/instances/tyh5f0tolu04uz54/clusters/zplwwpc7c4s73n26/manifests" -k | kubectl apply -f -
 2715  k get po -A -w
 2716  ls
 2717  sudo vim /etc/hosts
 2718  kx
 2719  k get po -A
 2720  kx
 2721  k get po -A
 2722  kx
 2723  k get po -A
 2724  kx
 2725  k get po -A
 2726  k9s
 2727  kx
 2728  k9s
 2729  kx
 2730  k get po -A
 2731  k describe po -n app2 app2-helm-guestbook-7bf6bf59d4-bdcr2
 2732  k get po -A
 2733  ls
 2734  cd portal/ui
 2735  PORTAL_SERVER_URL=https://portal-server.akuity-platform pnpm run dev
 2736  pwd
 2737  make generate-in-container
 2738  ls
 2739  pwd
 2740  cd ../kubevision-scripts
 2741  ./refresh-akp.sh
 2742  k9s
 2743  ./refresh-akp.sh
 2744  sh refresh-akp.sh
 2745  k get po -A
 2746  k get po -n akuity-platform
 2747  kx
 2748  k get po -n akuity-platform
 2749  k logs -n akuity-platform portal-server-647cf9656d-nhxk9
 2750  k logs -n akuity-platform portal-server-647cf9656d-nhxk9 -f
 2751  sh refresh-akp.sh
 2752  ls
 2753  cd ..
 2754  ls
 2755  sh portal-server-log.sh
 2756  sh refresh-akp.sh
 2757  sh portal-server-log.sh
 2758  sh refresh-akp.sh
 2759  sh portal-server-log.sh
 2760  sh refresh-akp.sh
 2761  sh portal-server-log.sh
 2762  sh refresh-akp.sh
 2763  ping registry-1.docker.io
 2764  sh refresh-akp.sh
 2765  ls
 2766  sh portal-server-log.sh
 2767  git status
 2768  cd .../
 2769  cd ..
 2770  ls
 2771  cd akuity-platform
 2772  git status
 2773  make generate-in-container
 2774  git status
 2775  cd ../kubevision-scripts
 2776  sh refresh-akp.sh
 2777  cd /Users/<USER>/MyPro/akuity/akuity-platform && grep -A 1 -B 1 "ListResourcesTreeview" internal/portalapi/organization/list_kubernetes_resources_v1.go
 2778  git status
 2779  cd ../
 2780  cd ..
 2781  ls
 2782  cd ..
 2783  ls
 2784  cd terraform-provider-akp
 2785  ls
 2786  git status
 2787  git diff examples/resources/akp_kargo_instance/basic.tf
 2788  ls
 2789  vim ~/.zshrc
 2790  akuity argocd instance list --organization-id llwxnuoz1gnqhzrx
 2791  source ~/.zshrc
 2792  akuity argocd instance list --organization-id llwxnuoz1gnqhzrx
 2793  ls
 2794  git fetch --tags
 2795  git remote -v
 2796  git fetch --tags
 2797  git tags
 2798  git tag
 2799  git checkout tags/v0.8.4
 2800  go install .
 2801  ls
 2802  ls ~/go/bin/terraform-provider-akp
 2803  ls -lsth ~/go/bin/terraform-provider-akp
 2804  date
 2805  vim ~/.terraformrc
 2806  cat ~/.terraformrc
 2807  ls ~/go/bin/
 2808  clear
 2809  ls
 2810  history |grep terraform
 2811  ls
 2812  git status
 2813  cd dist
 2814  ls
 2815  cd deploy
 2816  ls
 2817  rm -rf terraform.tfstate*
 2818  git status
 2819  ls
 2820  cat basic.tf
 2821  ls
 2822  cd examples
 2823  ls
 2824  cd resources
 2825  ls
 2826  cd akp_kargo_instance
 2827  git status
 2828  ls
 2829  git diff basic.tf
 2830  ls
 2831  cd ..
 2832  ls
 2833  git status
 2834  mkdir tmp
 2835  cp  examples/resources/akp_kargo_instance/basic.tf tmp
 2836  cd  examples/resources/akp_kargo_instance/b
 2837  cd  examples/resources/akp_kargo_instance/
 2838  git status
 2839  ls
 2840  git checkout basic.tf
 2841  git status
 2842  ls
 2843  vim basic.tf
 2844  ls
 2845  rm -rf terraform.tfstate
 2846  git status
 2847  ls
 2848  cd kargo-manifests
 2849  ls
 2850  cat kargo1.yaml
 2851  ls
 2852  cd ..
 2853  ls
 2854  terraform plan
 2855  ls
 2856  terraform plan
 2857  ls
 2858  pwd
 2859  open .
 2860  ls
 2861  rm -rf resource.tf
 2862  terraform plan
 2863  ls
 2864  terraform plan
 2865  terraform apply
 2866  sudo /etc/hosts
 2867  sudo vim /etc/hosts
 2868  ls
 2869  git status
 2870  git checkout resource.tf
 2871  git status
 2872  ls
 2873  cat resource.tf
 2874  ls
 2875  mv basic.tf ../
 2876  rm -rf terraform.tfstate
 2877  open .
 2878  terraform plan
 2879  cat ../basic.tf
 2880  terraform plan
 2881  history |grep terraform |grep state
 2882  history |grep terraform |grep state |grep -v rm
 2883  ls
 2884  terraform plan
 2885  cat ../basic.tf
 2886  terraform plan
 2887  terraform apply
 2888  cat ../basic.tf
 2889  cp ../basic.tf ./
 2890  ls
 2891  rm -rf terraform.tfstate
 2892  ls
 2893  clear
 2894  ls
 2895  mv resource.tf ../
 2896  terraform plan
 2897  terraform apply
 2898  sudo vim /etc/hosts
 2899  ls
 2900  cd kargo-manifests
 2901  ls
 2902  cat kargo1.yaml
 2903  k get po -n akuity-platform
 2904  k get po -A
 2905  k describe po -n kargo-121nfb9yek8fkjrt kargo-controller-my-agent-754fc468d6-fnrms
 2906  k get po -A
 2907  k describe po -n kargo-121nfb9yek8fkjrt kargo-controller-my-agent-754fc468d6-fnrms
 2908  k get po -A
 2909  git checkout main
 2910  git pull
 2911  git log
 2912  go install
 2913  pwd
 2914  cd ../..
 2915  cd ..
 2916  go install .
 2917  cd /examples/resources/akp_kargo_instance/kargo-manifests\n
 2918  cd /examples/resources/akp_kargo_instance
 2919  cd examples/resources/akp_kargo_instance
 2920  ls
 2921  mv resource.tf ../
 2922  terraform plan
 2923  terraform apply
 2924  git fetch
 2925  git checkout fix-resource-inconsistency
 2926  git log
 2927  pwd
 2928  cd ../../
 2929  cd ..
 2930  go install .
 2931  cd examples/resources/akp_kargo_instance\n
 2932  terraform apply
 2933  clear
 2934  ls
 2935  cd ..
 2936  ls
 2937  open .
 2938  git status
 2939  git branch
 2940  git checkout tags/v0.8.4
 2941  git status
 2942  history |grep export |grep terr
 2943  terraform -h
 2944  history |grep output |grep terraform
 2945  ls
 2946  mv resource.tf ../
 2947  terraform output 
 2948  ls
 2949  cd akp_kargo_instance
 2950  terraform output 
 2951  ls
 2952  mv resource.tf ../
 2953  terraform output 
 2954  ls
 2955  history |grep output |grep terraform
 2956  terraform output argo_resources_yaml
 2957  k get po -A
 2958  ls
 2959  cd ..
 2960  ls
 2961  cd akp_instance
 2962  ls
 2963  mv cmp.tf resource.tf ../
 2964  ls
 2965  terraform plan
 2966  pwd
 2967  go install .
 2968  ls
 2969  terraform plan
 2970  terraform apply
 2971  kx
 2972  k get po -A
 2973  kx
 2974  k get po -A
 2975  kx
 2976  k get po -A
 2977  TOKEN="eyJhbGciOiJSUzI1NiIsImtpZCI6IjM4ZTBhMmNkNGE0MTA5NGE2NDkzMDc4OGI1ZGY0NTcyN2JjZWFiOTgifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.k5VLpuNwaCljO6lL0NPFDhr8ZorL8ZX__oCEuoPbx7ULZzrXX1t67jeXGc9bueUBfHyeb0KBZd_ZUal8J69aDkIvp_II5i4TS4uaACkBc9OdFk8vYS70kHWNMwCZOnfaMnLgI3CjmBEuRSlYwsA_dXZQzl2Y-V7tgOMRs1mEjabP-lX3Abwqu0UrDFw_CQ3wTMLuK7aMDu61wjiJoVMGe5Ad4K4HeqgQ0dqVW0E7twudigiohvcAXnY8p-c2vzz3nbMdsDsP6UfPhiIC4RfZHzwL-KITRZ6L6WKLio4rQTFANzjPfEs8ciFW8hVfZPB3DWUQI_vMKSRABCHRYzmY0A" && curl -s -H "Authorization: Bearer $TOKEN" "http://localhost:3001/api/v1/orgs/llwxnuoz1gnqhzrx/argocd/instances/f9hu0r5dg9wmkrds/clusters/xcdalgggrz12r8yq/manifests" -k | kubectl apply -f -
 2978  k get po -A
 2979  kx
 2980  k get po -A
 2981  ls
 2982  git checkout tags/v0.9.0
 2983  pwd
 2984  cd ../..
 2985  go install .
 2986  terraform apply
 2987  k get po -A
 2988  ls
 2989  pwd
 2990  git branch
 2991  git checkout fix-resource-inconsistency
 2992  git status
 2993  go install .
 2994  git status
 2995  terraform apply
 2996  clear
 2997  ls
 2998  git checkout main
 2999  git status
 3000  cd ..
 3001  ls
 3002  cd akuity-platform
 3003  ls
 3004  git status
 3005  cd ../kubevision-scripts
 3006  UI=true ./refresh-akp.sh
 3007  clear
 3008  ls
 3009  clear
 3010  cd ..
 3011  cd akuity-platform
 3012  git status
 3013  make generate-in-container
 3014  ls
 3015  cd /Users/<USER>/MyPro/akuity/akuity-platform && go test -v ./internal/services/k8sresource -run TestBuildResourceTree
 3016  ls
 3017  cd ../kubevision-scripts
 3018  UI=true ./refresh-akp.sh
 3019  cd /Users/<USER>/MyPro/akuity/akuity-platform && go test -v ./internal/services/k8sresource -run TestBuildResourceTree
 3020  UI=true ./refresh-akp.sh
 3021  cd /Users/<USER>/MyPro/akuity/akuity-platform && go test -v ./internal/services/k8sresource -run TestBuildResourceTree
 3022  UI=true ./refresh-akp.sh
 3023  git status
 3024  cd ..
 3025  cd akuity-platform
 3026  git status
 3027  make generate-in-container
 3028  clear
 3029  ls
 3030  git status
 3031  git satus
 3032  git status
 3033  cd ../kubevision-scripts
 3034  UI=true ./refresh-akp.sh
 3035  ls
 3036  git status
 3037  git diff aims
 3038  git status
 3039  git add aims api
 3040  git diff  internal/services/k8sresource/treeview_reference_resource.sql
 3041  git add internal pkg
 3042  git status 
 3043  git add docs
 3044  git status
 3045  git diff portal/ui/src/lib/apiclient/organization/v1/organization_pb.ts
 3046  git add portal/ui/src/lib/apiclient/organization/v1/organization_pb.ts
 3047  git status
 3048  git log
 3049  git commit --amend
 3050  git status
 3051  git checkout portal/ui/src/feature/kubevision/components/kubevision-explorer-dashboard/kubevision-explorer-filter.tsx  portal/ui/src/feature/kubevision/components/kubevision-explorer-dashboard/kubevision-explorer-table.tsx   portal/ui/src/feature/kubevision/components/kubevision-explorer-dashboard/kubevision-explorer-filter-bar.tsx
 3052  git status
 3053  cd ..
 3054  cd akuity-platform
 3055  git status
 3056  git add portal
 3057  git status
 3058  git commit --amend
 3059  git status
 3060  git commit --amend
 3061  git status
 3062  git add portal 
 3063  git status
 3064  git commit --amend
 3065  cd ../kubevision-scripts
 3066  UI=true ./refresh-akp.sh
 3067  git status
 3068  git push
 3069  git push --set-upstream origin tree-view-filter
 3070  UI=true ./refresh-akp.sh
 3071  cd /Users/<USER>/MyPro/akuity/akuity-platform/portal/ui && NODE_ENV=production pnpm run build:ui
 3072  clear
 3073  ls
 3074  UI=true ./refresh-akp.sh
 3075  git status
 3076  git diff
 3077  git status
 3078  git add internal portal
 3079  git status
 3080  git commit -m "fix compile error" -s
 3081  git push 
 3082  git status
 3083  make generate-in-container
 3084  k9s
 3085  vim /etc/hosts
 3086  sudo vim /etc/hosts
 3087  kx
 3088  k get po -A
 3089  k describe po app1-helm-guestbook-69df499f4f-ctc9g
 3090  k get po -A
 3091  k describe po app1-helm-guestbook-69df499f4f-ctc9g
 3092  k get po -A
 3093  k describe po app1-helm-guestbook-69df499f4f-ctc9g
 3094  k get po -A
 3095  k describe pod app1-helm-guestbook-69df499f4f-ctc9g
 3096  k get po -A
 3097  kx
 3098  k get po -A
 3099  k get job -A
 3100  k delete job -n akuity-platform --all
 3101  k get job -A
 3102  k get po -A
 3103  PORTAL_SERVER_URL=https://portal-server.akuity-platform pnpm run dev
 3104  sudo vim /etc/hosts
 3105  kx
 3106  TOKEN="eyJhbGciOiJSUzI1NiIsImtpZCI6ImFmOTdiZTBiNTAyZjUxNGUyYWM5MzViODkxOTVjZmI0ZjQyNjQyYjMifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.0Xs6NKPlG31KqncjMUswN2Z3dzz0i1KRFIpE1KqzDnZfY9QCjlgFfmX1O9PQ0tRkJ68YOgNBwqJENfYO82maLTN3oQBMgqH48Rqr7JieZEzbRluFEvlUbBLXzpGeyX2JYUgeonQtBogPs1MGno358QiUOm8ekrzdHhuBhBhW6j5s6fETEQv0T5HkCU1AdawwygIaqAYojPVDx7Dtjgb3dNslUL3iU-7JYAhj4j4pqkUdaqTkK-M5mRInPeY8jNxwSzpqaf0oGKPKjVGW2jRwRZ-zPI0lmS_7E4OyyZaevnypDSjhXBZltG7DLqDu89mSIhcs8P014wGMb3WA0yqzmg" && curl -s -H "Authorization: Bearer $TOKEN" "https://portal-server.akuity-platform/api/v1/orgs/llwxnuoz1gnqhzrx/argocd/instances/o68gco9ntj3gghgp/clusters/4q6pdovynchvu6ri/manifests" -k | kubectl apply -f -
 3107  k get po -A
 3108  MyPro/SlackUp
 3109  ls
 3110  ./SlackUp
 3111  ls
 3112  cd MyPro
 3113  ls
 3114  cd akuity
 3115  ls
 3116  cd akuity-platform
 3117  ls
 3118  cd ../kubevision-scripts
 3119  UI=true ./refresh-akp.sh
 3120  ./refresh-akp.sh
 3121  k get po -A
 3122  k9s
 3123  cd ../kubevision-scripts
 3124  sh refresh-akp.sh
 3125  cd ..
 3126  cd akuity-platform
 3127  cd portal/ui
 3128  PORTAL_SERVER_URL=https://portal-server.akuity-platform pnpm run dev
 3129  kubectl get nodes
 3130  kubectl get all -A
 3131  kubectl logs -n argocd-f9hu0r5dg9wmkrds -l app=agent-server
 3132  kubectl get pods -n argocd-f9hu0r5dg9wmkrds
 3133  kubectl logs -n argocd-f9hu0r5dg9wmkrds agent-server-58c9d679c7-ctdkv
 3134  kubectl logs -n argocd-f9hu0r5dg9wmkrds k3s-6cbbd96777-9qfpw
 3135  kubectl logs -n argocd-f9hu0r5dg9wmkrds k3s-webhook-58dc585fbf-7gbr9
 3136  kubectl logs -n argocd-f9hu0r5dg9wmkrds argocd-server-67bdd46d7b-9mcf7
 3137  kubectl logs -n argocd-f9hu0r5dg9wmkrds argocd-repo-server-7d4f5c8c5c-2qj8v
 3138  kubectl get pods -n argocd-f9hu0r5dg9wmkrds | grep repo-server
 3139  kubectl logs -n argocd-f9hu0r5dg9wmkrds argocd-repo-server-58cb4d59f7-8jt7j
 3140  kubectl logs -n argocd-f9hu0r5dg9wmkrds argocd-application-controller-0
 3141  kubectl get pods -n argocd-f9hu0r5dg9wmkrds | grep application-controller
 3142  kubectl logs -n argocd-f9hu0r5dg9wmkrds argocd-application-controller-6f99fbb6b9-wm4ml
 3143  clear
 3144  git status
 3145  cd ../
 3146  cd akuity-platform
 3147  ls
 3148  git status
 3149  git diff internal/services/k8sresource/treeview.go
 3150  ls
 3151  pwd
 3152  ls
 3153  cd ../kubevision-scripts
 3154  ls
 3155  sh refresh-akp.sh
 3156  cd ..
 3157  cd akuity-platform
 3158  git status
 3159  git diff 
 3160  git status
 3161  git add internal
 3162  git status
 3163  git commit -m "modify health status filter" -s
 3164  git status
 3165  git push 
 3166  cd ../kubevision-scripts
 3167  sh refresh-akp.sh
 3168  sh ./refresh-akp.sh
 3169  git status
 3170  cd ../
 3171  cd akuity-platform
 3172  ls
 3173  git status
 3174  git diff
 3175  cd ../kubevision-scripts
 3176  git status
 3177  sh refresh-akp.sh
 3178  cd /Users/<USER>/MyPro/akuity/akuity-platform && go test ./internal/services/k8sresource -run TestBuildResourceTree -v
 3179  cd ../akuity-platform
 3180  git status
 3181  git add internal
 3182  git status
 3183  git commit -m "modify filter logic" -s
 3184  cd /Users/<USER>/MyPro/akuity/akuity-platform && go test ./internal/services/k8sresource -run TestBuildResourceTree -v
 3185  cd /Users/<USER>/MyPro/akuity/akuity-platform && go test ./internal/services/k8sresource -run TestBuildResourceTree/Name_filter_only_-_app -v
 3186  cd /Users/<USER>/MyPro/akuity/akuity-platform && go test ./internal/services/k8sresource -run TestBuildResourceTree/Name_filter_only_-_backend -v
 3187  cd /Users/<USER>/MyPro/akuity/akuity-platform && go test ./internal/services/k8sresource -run TestBuildResourceTree -v
 3188  cd /Users/<USER>/MyPro/akuity/akuity-platform && go test -v ./internal/services/k8sresource -run TestBuildResourceTree
 3189  git status
 3190  git add internal
 3191  git status
 3192  git log
 3193  git commit --amend
 3194  git push
 3195  git status
 3196  git fetch origin
 3197  git merge origin/main
 3198  make generate-in-container
 3199  git statud
 3200  git status
 3201  git add pkg internal
 3202  git status
 3203  git commit
 3204  git push 
 3205  k get po -A
 3206  kx
 3207  k get po -A
 3208  kubectl config delete-context k3d-akuity-customer
 3209  kubectl config delete-context k3d-akuity-customer-2
 3210  kx
 3211  sh start-akp.sh 1
 3212  cd portal/ui && pnpm run lint
 3213  pnpm run lint -- --fix
 3214  pnpm eslint --ignore-path .gitignore . --max-warnings=0 --fix
 3215  pnpm run lint
 3216  make format
 3217  git status
 3218  git diff internal
 3219  git status
 3220  git add internal portal
 3221  git status
 3222  git commit -m "fix linter error" -s
 3223  git push 
 3224  cd ../kubevision-scripts
 3225  UI=true ./refresh-akp.sh
 3226  cd portal/ui && pnpm add @r2wc/react-to-web-component
 3227  pnpm add @r2wc/react-to-web-component
 3228  pnpm run build
 3229  UI=true ./refresh-akp.sh
 3230  git status
 3231  vim /etc/hosts
 3232  sudo vim /etc/hosts
 3233  kx
 3234  TOKEN="******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" && curl -s -H "Authorization: Bearer $TOKEN" "https://portal-server.akuity-platform/api/v1/orgs/fxtplhnf7jfact2o/argocd/instances/igdnyj9f2d62u6m8/clusters/8pl0p1twzpiv3256/manifests" -k | kubectl apply -f -
 3235  k get po -A
 3236  kx
 3237  k get po -A
 3238  kx
 3239  k get po -A
 3240  PORTAL_SERVER_URL=https://portal-server.akuity-platform pnpm run dev
 3241  ./refresh-akp.sh
 3242  git status
 3243  cd ../akuity-platform
 3244  git status
 3245  git diff internal
 3246  git status
 3247  git add internal
 3248  git status
 3249  git commit -m "modify filter logic" -s
 3250  git push 
 3251  cd ../kubevision-scripts
 3252  sh refresh-akp.sh
 3253  k get po -A
 3254  export GH_PROXY=https://gh.monlor.com/ IMAGE_PROXY=ghcr.monlor.com && bash -c "$(curl -fsSL ${GH_PROXY}https://raw.githubusercontent.com/monlor/docker-xiaoya/main/install.sh)"\n
 3255  curl -fsSL "https://alist.example.com/v3.sh" -o v3.sh && bash v3.sh
 3256  ping alist.example.com
 3257  curl -fsSL "https://alist.example.com/v3.sh" -o v3.sh && bash v3.sh
 3258  ping google.com
 3259  curl -fsSL "https://alist.example.com/v3.sh" -o v3.sh && bash v3.sh
 3260  docker run -d --restart=unless-stopped -v /etc/alist:/opt/alist/data -p 5244:5244 -e PUID=0 -e PGID=0 -e UMASK=022 --name="alist" xhofe/alist:latest
 3261  docker exec -it alist ./alist admin set password
 3262  mkdir /opt/data
 3263  sudo mkdir /opt/data
 3264  cd /opt/data
 3265  ls
 3266  sudo mkdir /opt/aliyun
 3267  cd ~/Downloads
 3268  pwd
 3269  ping ************
 3270  telnet ************ 5666
 3271  ak
 3272  cd akuity-platform/portal/ui
 3273  PORTAL_SERVER_URL=https://portal-server.akuity-platform pnpm run dev
 3274  ls
 3275  cd ../
 3276  ls
 3277  cd ../kubevision-scripts
 3278  sh refresh-akp.sh
 3279  cd ../akuity-platform
 3280  cd /Users/<USER>/MyPro/akuity/akuity-platform && make proto
 3281  grep -n "proto" Makefile
 3282  grep -n "buf generate" Makefile
 3283  make generate-buf
 3284  git status
 3285  make generate-ui-buf
 3286  make generate-aims-buf
 3287  git status
 3288  make generate-in-container
 3289  git status
 3290  git diff aims
 3291  git status
 3292  git add aims api docs internal pkg portal
 3293  git status
 3294  git log
 3295  git commit -m "change resourceNameContains to string" -s
 3296  git pull 
 3297  git pull  --rebase
 3298  git status
 3299  git log
 3300  git fetch origin
 3301  git merge origin/main
 3302  git push 
 3303  cd ../kubevision-scripts
 3304  UI=true ./refresh-akp.sh
 3305  git status
 3306  cd portal/ui && npm run build
 3307  UI=true ./refresh-akp.sh
 3308  git status
 3309  git diff
 3310  git add portal
 3311  git commit -m "fix frontend error" -s
 3312  git push
 3313  clear
 3314  ls
 3315  ./refresh-akp.sh
 3316  UI=true ./refresh-akp.sh
 3317  cd ../
 3318  cd akuity-platform
 3319  make format
 3320  git status
 3321  cd portal/ui && pnpm run lint
 3322  git status
 3323  git add portal
 3324  git status
 3325  git commit -m "fix linter error" -s
 3326  git push
 3327  git status
 3328  git diff
 3329  cd ../kubevision-scripts
 3330  sh refresh-akp.sh
 3331  cd ..
 3332  cd akuity-platform
 3333  git status
 3334  git diff
 3335  git checkout internal
 3336  git status
 3337  git add portal
 3338  git commit -m "tmp" -s
 3339  git checkout main
 3340  git pull
 3341  cd ..
 3342  cd akuity-platform/
 3343  ls
 3344  pwd
 3345  git status
 3346  ls ~/Applications
 3347  which cursor
 3348  cursor -h
 3349  ls
 3350  cd ~/Library/Application Support/
 3351  cd ~/Library/Application\ Support
 3352  ls
 3353  cd Cursor
 3354  ls
 3355  cd User
 3356  ls
 3357  cat settings.json
 3358  ls
 3359  pwd
 3360  vim ~/.vscode/settings.json
 3361  vim ~/.vscode/
 3362  ls
 3363  vim ~/.vscode/settings.json
 3364  ak
 3365  ls
 3366  cd akuity-platform
 3367  ls
 3368  git branch
 3369  git checkout tree-view-filter
 3370  cd ../kubevision-scripts
 3371  sh refresh-akp.sh
 3372  kx
 3373  k get po -A
 3374  vim /etc/hosts
 3375  sudo vim /etc/hosts
 3376  kx
 3377  k get po A
 3378  k get po -A
 3379  kx
 3380  k get po -A
 3381  k delete ns test
 3382  vim /etc/hosts
 3383  sudo vim /etc/hosts
 3384  kx
 3385  k get po -A
 3386  TOKEN="eyJhbGciOiJSUzI1NiIsImtpZCI6IjE1YTFlNDlhMjNkNzQ1ZTU3ZmUwNjlmYjYxZWRmZTNlZTk5NzBmMjEifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.axLHsVFB94zD1GDLeuiqF80gs_TdmgqnVJdIdjDvDlcYd3dSO81Tul5s4uiN0ICUFm-TG8F8fseuxVEiLwvYZYlEIEFvBIWqle4D2AUwIWVcmOvym-KMG25A8ys7mBX0WvO2EmxYxLUfB-wqGelwTSMEQK4Hxw4Rf_eDxIirRI_HFJ8lr8bo_i1JMaiBIXBWtYhmC_ncBUOhvL_PpZk1g3jeikGU1_Bsoc0a_jG6CF3EqmJHhnen6I5_KO_jy0grbHteO-h0bwPhNA2RQO6XpQZv8ta990zZy3zXnzZk9Uv2-UWFEvpJsRCRHHLjngWID6tM1ey0Tml2aQmjeIIEOg" && curl -s -H "Authorization: Bearer $TOKEN" "http://localhost:3001/api/v1/orgs/fxtplhnf7jfact2o/argocd/instances/ubfqdabg8vca7qeq/clusters/y9ur07hriizlq3t2/manifests" -k | kubectl apply -f -
 3387  k get po -A
 3388  k get po -A -w
 3389  kx
 3390  k get po -A
 3391  kx
 3392  k get po -A
 3393  k delete po -n akuity akuity-agent-c97889bdf-pddh7
 3394  k get po -A
 3395  kx
 3396  k get po -A
 3397  kx
 3398  k get po -A
 3399  kx
 3400  k get po -A
 3401  k logs -n akuity-platform portal-server-5477b989b5-vddpl
 3402  k logs -n akuity-platform portal-server-5477b989b5-vddpl |grep error
 3403  kx
 3404  k get po -A
 3405  k logs -n akuity akuity-agent-c97889bdf-g9zzt
 3406  k logs -n akuity akuity-agent-c97889bdf-g9zzt -f
 3407  kx
 3408  k get po -A
 3409  ls
 3410  ./stop-akp.sh 2
 3411  ./stop-akp.sh 
 3412  kx
 3413  k get po -A
 3414  sh start-akp.sh 1
 3415  sudo vim /etc/hosts
 3416  kx
 3417  TOKEN="******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" && curl -s -H "Authorization: Bearer $TOKEN" "https://portal-server.akuity-platform/api/v1/orgs/gskd8l8k2dzvfsm6/argocd/instances/354rsnl1pxru0l10/clusters/5gtvnjwrp7hpj7zh/manifests" -k | kubectl apply -f -
 3418  k get po -A
 3419  k get po -A -w
 3420  k get po -A
 3421  kl
 3422  ko
 3423  k get po -A
 3424  kx
 3425  k get po -A
 3426  ls
 3427  cd akuity-platform
 3428  ls
 3429  git status
 3430  cd ../akuity-platform
 3431  ls
 3432  git status
 3433  git log
 3434  git status
 3435  git reset HEAD^ --soft
 3436  git status
 3437  git commit -m "modify frontend code" -s
 3438  git fetch origin
 3439  git merge origin/main
 3440  git push
 3441  kx
 3442  k get po -A
 3443  kx
 3444  k get po -A
 3445  kx
 3446  k get po -A
 3447  git status
 3448  git add internal
 3449  git status
 3450  git add portal
 3451  git status
 3452  git commit -m "change search relation into non-recursive" -s
 3453  git status
 3454  cd akuity-platform/portal/ui && npm run lint -- src/feature/kubevision/components/kubevision-deprecated-api-dashboard/kubevision-deprecated-api-drawer.tsx
 3455  cd /Users/<USER>/MyPro/akuity/akuity-platform/portal/ui && npm run lint -- src/feature/kubevision/components/kubevision-deprecated-api-dashboard/kubevision-deprecated-api-drawer.tsx
 3456  cd /Users/<USER>/MyPro/akuity/akuity-platform/portal/ui && npm run lint -- src/feature/kubevision/components/kubevision-explorer-dashboard/kubevision-explorer-filter.tsx
 3457  cd portal/ui
 3458  npm run lint 
 3459  npm run lint ./src
 3460  cd akuity-platform
 3461  ls
 3462  git status
 3463  git add portal
 3464  git status
 3465  git commit --amend
 3466  git status
 3467  git fetch origin
 3468  git merge origin/main
 3469  git push
 3470  pwd
 3471  make format
 3472  git status
 3473  git diff internal
 3474  git add internal
 3475  git commit -m "format" -s
 3476  git push
 3477  git status
 3478  ls
 3479  cd ../kubevision-scripts
 3480  UI=true ./refresh-akp.sh
 3481  eslint --ignore-path .gitignore . --max-warnings=0 src
 3482  lint eslint --ignore-path .gitignore . --max-warnings=0 src
 3483  akuity-platform@0.0.1 lint eslint --ignore-path .gitignore . --max-warnings=0 src
 3484  UI=true ./refresh-akp.sh
 3485  clear
 3486  ls
 3487  ak
 3488  cd akuity-platform
 3489  ls
 3490  git status
 3491  git add internal
 3492  git status
 3493  cd ../kubevision-scripts
 3494  UI=true ./refresh-akp.sh
 3495  cd /Users/<USER>/MyPro/akuity/akuity-platform && go test -timeout 30s -run ^TestBuildResourceTree$ github.com/akuityio/akuity-platform/internal/services/k8sresource
 3496  cd ../kubevision-scripts
 3497  git status
 3498  cd ../akuity-platform
 3499  git status
 3500  cd /Users/<USER>/MyPro/akuity/akuity-platform && go test -timeout 30s -run ^TestBuildResourceTree$ github.com/akuityio/akuity-platform/internal/services/k8sresource
 3501  cd ../kubevision-scripts
 3502  UI=true ./refresh-akp.sh
 3503  git status
 3504  git add internal
 3505  git status
 3506  git log
 3507  git commit -m "optimize filter logic" -s
 3508  git fetch origin
 3509  git merge origin/main
 3510  make generate-in-container
 3511  git status
 3512  git add pkg
 3513  git status
 3514  git commit 
 3515  git status
 3516  git push
 3517  UI=true ./refresh-akp.sh
 3518  cd ../kubevision-scripts
 3519  ls
 3520  ./stop-akp.sh
 3521  kx
 3522  k get po -A
 3523  sh start-akp.sh 1
 3524  kx
 3525  k get po -A
 3526  TOKEN="******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" && curl -s -H "Authorization: Bearer $TOKEN" "https://portal-server.akuity-platform/api/v1/orgs/nbcnd3i9rd5xn7i1/argocd/instances/ojt5uaybc03xuftq/clusters/8rz4mmej0z7d5wc1/manifests" -k | kubectl apply -f -
 3527  k get po-A
 3528  k get po -A
 3529  k get po -A -w
 3530  sudo vim /etc/hosts
 3531  ak
 3532  cd akuity-platform/portal/ui
 3533  PORTAL_SERVER_URL=https://portal-server.akuity-platform pnpm run dev
 3534  sh refresh-akp.sh
 3535  cd ../
 3536  ls
 3537  cd akuity-platform
 3538  ls
 3539  git status
 3540  git add internal
 3541  git status
 3542  git checkout main
 3543  git pull
 3544  git checkout -b fix-enable-monitoring
 3545  git status
 3546  git commit -m "fix: enable monitoring on both instance and cluster levels in a single operation" -s
 3547  git status
 3548  git push
 3549  git status
 3550  git checkout main
 3551  git checkout -b fix-treeview
 3552  git status
 3553  git branch
 3554  git checkout tree-view-filter
 3555  make generate-in-container
 3556  git status
 3557  UI=true ./refresh-akp.sh
 3558  cd ../kubevision-scripts
 3559  UI=true ./refresh-akp.sh
 3560  cd /Users/<USER>/MyPro/akuity/akuity-platform && find portal/ui -type f -name "*.tsx" -o -name "*.ts" | xargs grep -l "healthStatuses:" | head
 3561  UI=true ./refresh-akp.sh
 3562  cd /Users/<USER>/MyPro/akuity/akuity-platform && find portal/ui -type f -name "*.tsx" -o -name "*.ts" | xargs grep -l "resourceNameContains:" | head
 3563  find . -name "*.ts*" -exec grep -l "filter.*\.resourceNameContains" {} \;
 3564  UI=true ./refresh-akp.sh
 3565  cd portal/ui && npm run build
 3566  clear
 3567  ls
 3568  git status
 3569  make format
 3570  git status
 3571  git add aims api docs internal pkg portal
 3572  git status
 3573  git commit -m "change treeview filter names" -s
 3574  git fetch origin
 3575  git merge origin/main
 3576  git push
 3577  UI=true ./refresh-akp.sh
 3578  cd ../kubevision-scripts
 3579  UI=true ./refresh-akp.sh
 3580  cd ..
 3581  cd akuity-platform
 3582  git status
 3583  git add internal
 3584  git status
 3585  git commit -m "change treeview filter names" -s
 3586  git push
 3587  cd ../
 3588  cd akuity-platform
 3589  git status
 3590  git add internal
 3591  git status
 3592  git commit -m "update healthy filter logic" -s
 3593  git push
 3594  sh refresh-akp.sh
 3595  cd ..
 3596  cd akuity-platform
 3597  ls
 3598  git fetch origin
 3599  git merge origin/main
 3600  make generate-in-container
 3601  git status
 3602  git add pkg
 3603  git commit
 3604  git push
 3605  cd ~/MyPro/SlackUp
 3606  ./SlackUp
 3607  cd MyPro/SlackUp
 3608  ./SlackUp
 3609  git status
 3610  git checkout main
 3611  git status
 3612  git pull
 3613  git status
 3614  git branch
 3615  git branch -D fix-enable-monitoring
 3616  git branch
 3617  git branch -D feedback-comment
 3618  git branch
 3619  git branch -D tree-view-filter
 3620  git branch
 3621  git branch -D fix-treeview
 3622  git status
 3623  git log
 3624  git checkout -b treeview-order
 3625  ls
 3626  cd ../kubevision-scripts
 3627  sh refresh-akp.sh
 3628  cd /Users/<USER>/MyPro/akuity/akuity-platform && go list -m all | grep k8s.io/apimachinery
 3629  cd /Users/<USER>/MyPro/akuity/akuity-platform && find . -name "sort.go" | grep -i k8s
 3630  cd ..
 3631  cd akuity-platform
 3632  git status
 3633  git add internal
 3634  git status
 3635  git commit -m "feat: tree view maintain stable resources order" -s
 3636  git status
 3637  git push
 3638  git status
 3639  git add internal
 3640  git commit --amend
 3641  git push --hard
 3642  git status
 3643  git push --hard
 3644  git push --force
 3645  ./SlackUp
 3646  cd ../kubevision-scripts
 3647  sh refresh-akp.sh
 3648  k9s
 3649  cd ../
 3650  cd akuity-platform
 3651  ls
 3652  git status
 3653  git checkout main
 3654  git status
 3655  git diff internal
 3656  git add internal
 3657  git checkout main
 3658  git stash
 3659  git checkout main
 3660  cd ../kubevision-scripts
 3661  sh refresh-akp.sh
 3662  cd ../
 3663  cd ..
 3664  git status
 3665  git branch
 3666  git checkout treeview-order
 3667  git status
 3668  git log
 3669  git stash
 3670  git stash list
 3671  git stash -h
 3672  git stash apply
 3673  git status
 3674  git log
 3675  git diff
 3676  cd ../kubevision-scripts
 3677  git status
 3678  cd ../akuity-platform
 3679  git status
 3680  git add internal
 3681  git status
 3682  git log
 3683  git status
 3684  git commit -m "add compare by group" -s
 3685  git push 
 3686  git pull
 3687  gi tlog
 3688  git log
 3689  clear
 3690  ls
 3691  cd ../kubevision-scripts
 3692  sh refresh-akp.sh
 3693  cd ../akuity-platform
 3694  ls
 3695  git status
 3696  git checkout main
 3697  git branch
 3698  git branch -D treeview-order
 3699  git status
 3700  clear
 3701  ls
 3702  git pull
 3703  ls
 3704  make generate-in-container
 3705  make build
 3706  make help
 3707  make generate-buf
 3708  git status
 3709  ls
 3710  cd ../kubevision-scripts
 3711  UI=true ./refresh-akp.sh
 3712  ls
 3713  cd ../akuity-platform
 3714  make generate-buf
 3715  make generate-in-container
 3716  cd ../kubevision-scripts
 3717  UI=true ./refresh-akp.sh
 3718  cd portal/ui && NODE_ENV=development pnpm run build:ai-support-engineer
 3719  cd portal/ui && cat package.json | grep "build"
 3720  cat package.json | grep "build"
 3721  NODE_ENV=development pnpm run build:extensions
 3722  NODE_ENV=development pnpm run build:kargo-extensions
 3723  cd /Users/<USER>/MyPro/akuity/akuity-platform && UI=false ./kubevision-scripts/refresh-akp.sh
 3724  find . -name "refresh-akp.sh"
 3725  cd /Users/<USER>/MyPro && find . -name "refresh-akp.sh"
 3726  cd /Users/<USER>/MyPro/akuity && UI=false ./kubevision-scripts/refresh-akp.sh
 3727  UI=true ./refresh-akp.sh
 3728  chmod +x /Applications/CursorPro.app/Contents/MacOS/CursorPro && sudo xattr -rd com.apple.quarantine /Applications/CursorPro.app
 3729  cd ../kubevision-scripts
 3730  UI=true ./refresh-akp.sh
 3731  cd /Users/<USER>/MyPro/akuity/akuity-platform/portal/ui && pnpm install react-ace ace-builds ace-linters
 3732  cd /Users/<USER>/MyPro/akuity/akuity-platform/portal/ui && NODE_ENV=production pnpm run build
 3733  UI=true ./refresh-akp.sh
 3734  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 3735  ls
 3736  cd ../kubevision-scripts
 3737  PORTAL_SERVER_URL=https://portal-server.akuity-platform pnpm run dev
 3738  cd ../akuity-platform/portal/ui
 3739  PORTAL_SERVER_URL=https://portal-server.akuity-platform pnpm run dev
 3740  cd ../akuity-platform
 3741  git status
 3742  cd ../kubevision-scripts
 3743  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 3744  cd /Users/<USER>/MyPro/akuity/akuity-platform && grep -r "console.log.*aiProvider" portal/ui/src/ --include="*.tsx" --include="*.ts"
 3745  cd ../kubevision-scripts
 3746  sh refresh-akp.sh
 3747  ls
 3748  vim /etc/hosts
 3749  sudo vim /etc/hosts
 3750  k get po -A
 3751  k get po -n kargo-6ljp9qq0muz7rlsz
 3752  k delete po -n kargo-6ljp9qq0muz7rlsz --all
 3753  k get po -n kargo-6ljp9qq0muz7rlsz
 3754  k get po -n kargo-6ljp9qq0muz7rlsz -w
 3755  k get po -n kargo-6ljp9qq0muz7rlsz 
 3756  k describe po -n kargo-6ljp9qq0muz7rlsz kargo-webhooks-server-547b7d47b5-h4f4m
 3757  docker pull ghcr.io/akuity/kargo:v1.5.3
 3758  ping google.com
 3759  docker pull  ghcr.io/akuity/kargo:v1.5.3
 3760  docker pull  kargo:v1.5.3
 3761  docker login ghcr.io
 3762  ls
 3763  ak
 3764  cd kubevision-scripts
 3765  ls
 3766  ./stop-akp.sh
 3767  docker images
 3768  docker images|grep akuity
 3769  docker pull us-docker.pkg.dev/akuity/akp/agent-server:0.6.0
 3770  docker pull us-docker.pkg.dev/akuity/akp/akuity-platform
 3771  docker images|grep akuity
 3772  kx
 3773  sh start-akp.sh 1
 3774  docker images
 3775  docker pull us-docker.pkg.dev/akuity/akp/agent-server:0.6.0
 3776  cd MyPro/SlackUp
 3777  ./SlackUp
 3778  sudi vim  /etc/hosts
 3779  sudo vim /etc/hosts
 3780  r
 3781  sudo vim /etc/hosts
 3782  clear
 3783  k get po -A
 3784  sudo vim /etc/hosts
 3785  clear
 3786  k get po -A
 3787  k get po -n argocd-1350h2p9i69yqkoi
 3788  kx
 3789  TOKEN="eyJhbGciOiJSUzI1NiIsImtpZCI6ImQ1Y2YzNjViOGE1NjIwNjc2NzNiZTEyNzc0NmRkYzNjZTg4ZDU0NjkifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.N1-1da-KgPU14EDjXDYTLAeCcBKry4kliN3-XEk_5ETkHl12yAgVoCO7pjtJWJCunVStqhoHrBKRtB_wllwAK2QmjlW9FY6bVxnPbaZrKcaO_ZpIBp5C8w9nrwzOKwtAxy6yzw4fbrTWOvPG7eQjWThnUoWzedmtsjHe_4BMKH6cw5ibgpxOmyOov8VWSPcRP0d3Bci2asD-zL_8IYFORxDT9WnXiubd6zQS7-RIaNMuczKcTizKX_4nUSoFwwtyyp4SA9hfH6Qr-saggwvdGe41h4WeSuGrh4T6Q1Y_enFcJsFl2coUAOPA4dnJZrNol7jHyYCi_w9YSn63xbRlnQ" && curl -s -H "Authorization: Bearer $TOKEN" "https://portal-server.akuity-platform/api/v1/orgs/0rwlqxgauoyvw3sm/argocd/instances/1350h2p9i69yqkoi/clusters/e86htu8teqjvg5o8/manifests" -k | kubectl apply -f -
 3790  git status
 3791  git log
 3792  git status
 3793  git pull
 3794  ENABLE_KARGO=true ./refresh-akp.sh
 3795  ls
 3796  git log
 3797  ENABLE_KARGO=true ./refresh-akp.sh
 3798  k get po -A
 3799  clear
 3800  ls
 3801  git log
 3802  ENABLE_KARGO=true ./start-akp.sh
 3803  ls  /bin/akputil
 3804  sudo  mv /Users/<USER>/MyPro/akuity/akuity-platform/dist/akputil /bin/
 3805  go env
 3806  go env|grep path
 3807  go env|grep PATH
 3808  mv /Users/<USER>/MyPro/akuity/akuity-platform/dist/akputil /Users/<USER>/go/bin/
 3809  ENABLE_KARGO=true ./start-akp.sh
 3810  akuity -h
 3811  vim ~/.zshrc
 3812  echo $GOPATH
 3813  go env |grep PATH
 3814  ENABLE_KARGO=true ./start-akp.sh
 3815  akputil
 3816  ls /Users/<USER>/go/bin/
 3817  cp  /Users/<USER>/go/bin/akputil /usr/local/bin
 3818  sudo cp  /Users/<USER>/go/bin/akputil /usr/local/bin
 3819  akputil
 3820  ENABLE_KARGO=true ./start-akp.sh
 3821  sudo vim /etc/hosts
 3822  ENABLE_KARGO=true ./start-akp.sh
 3823  k get po -A
 3824  kx
 3825  k get po -A
 3826  kx
 3827  k get po -A
 3828  ENABLE_KARGO=true ./start-akp.sh
 3829  source ~/.zshrc
 3830  env |grep API
 3831  vim  ~/.zshrc
 3832  source ~/.zshrc
 3833  ENABLE_KARGO=true ./start-akp.sh
 3834  cd /Users/<USER>/MyPro/akuity/akuity-platform && curl -X GET "http://localhost:3000/api/v1/organizations/ai-provider" -v
 3835  ls
 3836  PORTAL_SERVER_URL=https://portal-server.akuity-platform pnpm run dev
 3837  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 3838  cd ..
 3839  cd akuity-platform
 3840  git status
 3841  git add aims api internal portal
 3842  git status
 3843  git add pkg
 3844  git status
 3845  git commit -m "feat: add info about AI provide to AI Assistant extension" -s
 3846  git branch
 3847  git checkout -b ai-info
 3848  git checkout main
 3849  git reset HEAD^ --hard
 3850  git branch
 3851  git checkout ai-info
 3852  cd /Users/<USER>/MyPro/akuity/akuity-platform && npm run dev
 3853  cd /Users/<USER>/MyPro/akuity/akuity-platform/portal/ui && npm run dev
 3854  git status
 3855  git add portal
 3856  git status
 3857  git commit -m "remove debug info" -s
 3858  git status
 3859  git push
 3860  git push --set-upstream origin ai-info
 3861  git status
 3862  git add internal portal
 3863  git status
 3864  git commit -m "remove annotations" -s
 3865  git push
 3866  cd ../kubevision-scripts
 3867  sh refresh-akp.sh
 3868  cd ../
 3869  cd akuity-platform
 3870  git status
 3871  git add internal
 3872  git commit -m "change get org function" -s
 3873  git push
 3874  cd ../kubevision-scripts
 3875  sh refresh-akp.sh
 3876  cd ../
 3877  cd akuity-platform
 3878  git status
 3879  git add internal
 3880  git status
 3881  git add internal
 3882  git status
 3883  git commit -m "change get ai provider function" -s
 3884  git push
 3885  git status
 3886  git add portal
 3887  git status
 3888  git commit -m "modify getDisplayAIProvider function" -s
 3889  git push
 3890  cd ../kubevision-scripts
 3891  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 3892  git status
 3893  cd ../akuity-platform
 3894  git status
 3895  git diff portal
 3896  git status
 3897  git diff portal
 3898  git checkout portal
 3899  git status
 3900  git add portal
 3901  git status
 3902  cd ../kubevision-scripts
 3903  cd ../
 3904  cd akuity-platform
 3905  git status
 3906  git commit -m "update aiProvider" -s
 3907  git push
 3908  cd ../kubevision-scripts
 3909  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 3910  MyPro/SlackUp
 3911  ./SlackUp
 3912  ping http://***************/
 3913  ping ***************
 3914  ping ************
 3915  ping ***************
 3916  sudo vim /etc/hosts
 3917  ping jump-tiger.com
 3918  ssh <EMAIL>
 3919  ssh <EMAIL>
 3920  ssh-keygen -t rsa -b 4096 -C "<EMAIL>"\n
 3921  ssh-copy-id <EMAIL>
 3922  ssh <EMAIL>
 3923  vim ~/.zshrc
 3924  source ~/.zshrc
 3925  tiger
 3926  vim ~/.zshrc
 3927  ssh <EMAIL>
 3928  tiger
 3929  ping jump-tiger.com
 3930  tiger
 3931  cd MyPro
 3932  https://github.com/sxcool1024/Jay.git
 3933  git clone https://github.com/sxcool1024/Jay.git
 3934  cd Jay
 3935  ls
 3936  cd 20
 3937  cd ..
 3938  rm -rf Jay
 3939  tiger
 3940  cd ../
 3941  ls
 3942  cd ~/MyPro
 3943  cd python
 3944  ls
 3945  diskutil list
 3946  ls
 3947  diskutil list
 3948  sudo diskutil eraseDisk ExFAT MyDisk /dev/disk5\n
 3949  ssh tiger
 3950  tiger
 3951  cd MyPro
 3952  ls
 3953  cd python
 3954  ls
 3955  python3 sharefile.py
 3956  python3 sharefile.py --port 9010 --dir ~/Downloads
 3957  cd ~/Downloads
 3958  ls
 3959  ping http://jump-tiger.com/
 3960  ping jump-tiger.com
 3961  cd Downloads
 3962  ls alist
 3963  chmod +x alist
 3964  ./alist server
 3965  ./alist admin
 3966  ./alist admin random
 3967  ./alist admin set Pas5word
 3968  cd MyPro
 3969  cd python
 3970  ls
 3971  python3 sharefile.py --port 9010 --dir ~/Downloads
 3972  history 
 3973  history |grep disk
 3974  diskutil list
 3975  sudo diskutil eraseDisk ExFAT MyDisk /dev/disk4
 3976  cd MyPro/SlackUp
 3977  ls
 3978  ./SlackUp
 3979  vim config.yaml
 3980  ./SlackUp
 3981  sudo vim /etc/hosts
 3982  ssh <EMAIL>
 3983  vim ~/.ssh/known_hosts
 3984  ssh <EMAIL>
 3985  ping jump-tiger.com
 3986  ssh <EMAIL>
 3987  history|grep dist
 3988  history |grep disk
 3989  diskutil list
 3990  history|grep dist
 3991  history|grep disk
 3992  sudo diskutil eraseDisk ExFAT MyDisk /dev/disk4
 3993  ls
 3994  ssh <EMAIL>
 3995  ls
 3996  cd ~/Desktop
 3997  ls
 3998  cd tmp
 3999  ls
 4000  <NAME_EMAIL>://vol2/1000/Photos
 4001  scp -rf  camera <EMAIL>://vol2/1000/Photos
 4002  scp -rf  camera <EMAIL>:/vol2/1000/Photos
 4003  ls
 4004  scp ./camera <EMAIL>:/vol2/1000/Photos
 4005  scp ./camera -rf <EMAIL>:/vol2/1000/Photos
 4006  scp ./camera -r <EMAIL>:/vol2/1000/Photos
 4007  scp -r ./camera <EMAIL>:/vol2/1000/Photos
 4008  rsync
 4009  ping jump-tiger.com
 4010  rsync -avz --progress -e ssh ./camera admin@************:/vol2/1000/Photos
 4011  brew install rsync parallel 
 4012  cd MyPro/SlackUp
 4013  ls
 4014  ./SlackUp
 4015  vim config.yaml
 4016  ./SlackUp
 4017  clear
 4018  ls
 4019  ssh <EMAIL>
 4020  ls
 4021  git status
 4022  git add internal portal
 4023  git status
 4024  git commit -m "change display ai provider into backend logic" -s
 4025  git fetch origin
 4026  git merge origin/main
 4027  git status
 4028  git add portal
 4029  git status
 4030  git commit
 4031  git status
 4032  cd portal/ui
 4033  pnpm run lint --fix
 4034  git status
 4035  git diff src/feature/ai-support-engineer/ai-support-engineer.tsx
 4036  git add src
 4037  git status
 4038  git log
 4039  git commit -m "fix linter error" -s
 4040  git status
 4041  git push
 4042  ls
 4043  cd ../
 4044  ls
 4045  cd ..
 4046  cd ../kubevision-scripts
 4047  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 4048  clear
 4049  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 4050  cd ../
 4051  cd akuity-platform
 4052  cd portal/ui
 4053  PORTAL_SERVER_URL=https://portal-server.akuity-platform pnpm run dev
 4054  git status
 4055  git diff portal/ui/src/feature/ai-support-engineer/components/conversation-input-area/conversation-input-area.tsx
 4056  git status
 4057  git add internal portal
 4058  git status
 4059  git commit -m "change the way of get model version" -s
 4060  git status
 4061  git push
 4062  git status
 4063  git add portal
 4064  git status
 4065  git commit -m "change function name" -s
 4066  git push
 4067  cd ../kubevision-scripts
 4068  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 4069  cd ../
 4070  cd akuity-platform
 4071  make generate-in-container
 4072  git status
 4073  git add aims internal pkg portal
 4074  git status
 4075  git add api
 4076  git status
 4077  git commit -m "change variable names" -s
 4078  git push
 4079  cd ../kubevision-scripts
 4080  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 4081  cd ../akuity-platform
 4082  git status
 4083  git add internal
 4084  git status
 4085  git commit -m "change function name" -s
 4086  git push
 4087  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 4088  cd ..
 4089  cd akuity-platform
 4090  git status
 4091  git add portal
 4092  git status
 4093  git commit -m "add div classname" -s
 4094  git push
 4095  git status
 4096  git log
 4097  clear
 4098  git status
 4099  cd portal/ui
 4100  pnpm run lint
 4101  pnpm run lint --fix
 4102  pnpm run lint 
 4103  git status
 4104  git diff src
 4105  git add src
 4106  git commit -m "fix linter error" -s
 4107  git push
 4108  clear
 4109  ls
 4110  clear
 4111  PORTAL_SERVER_URL=https://portal-server.akuity-platform pnpm run dev
 4112  k9s
 4113  ls
 4114  cd ../kubevision-scripts
 4115  ls
 4116  ./refresh-akp.sh
 4117  cd ../
 4118  cd akuity-platform
 4119  git status
 4120  git diff
 4121  git status
 4122  git add api internal
 4123  git status
 4124  git commit -m "tmp" -s
 4125  git cehckout main
 4126  git checkout main
 4127  git pull
 4128  git status
 4129  git chekcout -b treeview-fix
 4130  git checkout -b treeview-fix
 4131  make generate-in-container
 4132  cd ../kubevision-scripts
 4133  sh refresh-akp.sh
 4134  git status
 4135  cd ../kubevision-scripts
 4136  cd ../akuity-platform
 4137  git status
 4138  git diff
 4139  git add aims api docs internal pkg portal
 4140  git status
 4141  git commit -m "change HealthStatus types from strings to emum" -s
 4142  git push
 4143  git status
 4144  git branch
 4145  git checkout ai-info
 4146  git status
 4147  git log
 4148  clear
 4149  git commit --amend
 4150  git reset HEAD~1
 4151  git status
 4152  git checkout internal/services/k8sresource/treeview.go
 4153  git status
 4154  git log
 4155  git diff api/proto/extension/v1/extension.proto
 4156  git diff  api/proto/kargo/v1/kargo.proto
 4157  git diff api/proto/organization/v1/organization.proto
 4158  git checkout api/proto/organization/v1/organization.proto
 4159  git status
 4160  make generate-in-container
 4161  git status
 4162  git diff
 4163  cd ../kubevision-scripts
 4164  sh refresh-akp.sh
 4165  cd ../akuity-platform
 4166  make generate-in-container
 4167  git status
 4168  cd ../kubevision-scripts
 4169  sh refresh-akp.sh
 4170  cd ../akuity-platform
 4171  make generate-in-container
 4172  git status
 4173  cd ../kubevision-scripts
 4174  sh refresh-akp.sh
 4175  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 4176  git status
 4177  cd ..
 4178  git status
 4179  git add aims api docs internal pkg portal
 4180  git status
 4181  git log
 4182  git commit -m "do not hard codes model version in frontend logic" -s
 4183  git push
 4184  git fetch origin
 4185  git merge origin/main
 4186  git push
 4187  make generate-in-container
 4188  ping jump-tiger.com
 4189  history|grep disk
 4190  diskutil list
 4191  rar
 4192  diskutil list
 4193  sudo diskutil eraseDisk ExFAT MyDisk /dev/disk4
 4194  diskutil list
 4195  sudo diskutil eraseDisk ExFAT MyDisk /dev/disk4
 4196  diskutil list
 4197  sudo diskutil eraseDisk ExFAT MyDisk /dev/disk10
 4198  cd MyPro/SlackUp
 4199  cat config.yaml
 4200  ./SlackUp
 4201  diskutil list
 4202  sudo diskutil eraseDisk ExFAT MyDisk /dev/disk9
 4203  clear
 4204  git status
 4205  git diff internal/extensions/js/handler.go
 4206  git diff internal/utils/misc/argocd.go
 4207  git checkout internal models
 4208  git status
 4209  git add portal
 4210  git status
 4211  cd ../kubevision-scripts
 4212  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 4213  git status
 4214  cd ../akuity-platform
 4215  git status
 4216  make generate-in-container
 4217  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 4218  cd portal/ui
 4219  pnpm run lint 
 4220  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 4221  PORTAL_SERVER_URL=https://portal-server.akuity-platform pnpm run dev
 4222  git status
 4223  git add portal internal
 4224  git status
 4225  git add internal
 4226  git status
 4227  git log
 4228  git commit -m "update layout get model logic" -s
 4229  git push
 4230  git fetch origin
 4231  git merge origin/main
 4232  git push 
 4233  git status
 4234  git add portal
 4235  cd portal/ui
 4236  pnpm run lint --fix
 4237  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 4238  cd ..
 4239  git status
 4240  git add portal
 4241  git status
 4242  git commit -m "remove debug log" -s
 4243  git push
 4244  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 4245  cd ../akuity-platform
 4246  cd portal/ui
 4247  ls
 4248  PORTAL_SERVER_URL=https://portal-server.akuity-platform pnpm run dev
 4249  ak
 4250  cd akuity-platform
 4251  PORTAL_SERVER_URL=https://portal-server.akuity-platform pnpm run dev
 4252  cd portal/ui
 4253  PORTAL_SERVER_URL=https://portal-server.akuity-platform pnpm run dev
 4254  clear
 4255  cd ..
 4256  cd ../
 4257  cd ../kubevision-scripts
 4258  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 4259  PORTAL_SERVER_URL=https://portal-server.akuity-platform pnpm run dev
 4260  cd ../akuity-platform
 4261  git status
 4262  git add portal
 4263  git commit -m "fix linter error" -s
 4264  git push
 4265  clear
 4266  git status
 4267  git checkout main
 4268  git status
 4269  git pull
 4270  git branch
 4271  git checkout treeview-fix
 4272  git branch
 4273  git log
 4274  clear
 4275  cd portal/ui
 4276  ls
 4277  pnpm run lint
 4278  cd ..
 4279  make generate-in-container
 4280  cd ..
 4281  make generate-in-container
 4282  git status
 4283  git diff internal/portalapi/organization/list_kubernetes_resources_v1.go
 4284  git diff  internal/services/k8sresource/treeview.go
 4285  git add aims api docs internal pkg portal
 4286  git status
 4287  cd ../kubevision-scripts
 4288  ./refresh-akp.sh
 4289  cd ../akuity-platform
 4290  git status
 4291  git commit -m "change kind filters to gvk filters" -s
 4292  git push
 4293  clear
 4294  git status
 4295  git diff
 4296  cd ..
 4297  git diff
 4298  git status
 4299  git log
 4300  git status
 4301  git add internal
 4302  git commit -m "change variables name" -s
 4303  git push
 4304  git branch
 4305  git checkout ai-info
 4306  git status
 4307  clear
 4308  git status
 4309  cd portal/ui
 4310  pnpm run lint
 4311  pnpm run lint --fix
 4312  pnpm run lint 
 4313  git status
 4314  git diff portal
 4315  git diff
 4316  git status
 4317  git diff  portal/ui/src/feature/organization/organization-settings/views/general/rename-org.tsx
 4318  git checkout internal
 4319  git status
 4320  git diff
 4321  git add portal
 4322  git commit -m "fix linter error" -s
 4323  git push
 4324  pnpm run lint 
 4325  clear
 4326  ls
 4327  git status
 4328  git branch
 4329  git checkout treeview-fix
 4330  vim ~/.gitconfig
 4331  git status
 4332  git checkout main
 4333  git pull
 4334  git log
 4335  clear
 4336  git checkout -b ai-contexts-fix
 4337  git status
 4338  git add internal
 4339  git status
 4340  git commit -m "fix Duplicated context showing in the suggested context" -s
 4341  git log
 4342  git commit --amend
 4343  clear
 4344  ls
 4345  git branch
 4346  git checkout treeview-fix
 4347  clear
 4348  cd ..
 4349  cd akuity-platform
 4350  git status
 4351  git checkout main
 4352  git pull
 4353  git log
 4354  clear
 4355  make generate-in-container
 4356  cd ../kubevision-scripts
 4357  ls
 4358  sh refresh-akp.sh
 4359  git status
 4360  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 4361  git branch
 4362  cd ../
 4363  ls
 4364  d akuity-platform
 4365  cd akuity-platform
 4366  branch
 4367  git branch
 4368  git checkout ai-contexts-fix
 4369  git status
 4370  clear
 4371  ls
 4372  ak
 4373  cd ../SlackUp
 4374  ak/Slackup
 4375  ak; ../SlackUp
 4376  cd /roo
 4377  cd /
 4378  ak; ../Slackup
 4379  ls
 4380  ./SlackUp
 4381  exit
 4382  diskutil list
 4383  sudo diskutil eraseDisk ExFAT MyDisk /dev/disk9
 4384  clear
 4385  ls
 4386  git status
 4387  git branch
 4388  git checkout treeview-fix
 4389  cd ../kubevision-scripts
 4390  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 4391  cd ../akuity-platform
 4392  make generate-in-container
 4393  git status
 4394  cd ../kubevision-scripts
 4395  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 4396  cd ../akuity-platform
 4397  git status
 4398  git checkout portal
 4399  git status
 4400  git log
 4401  git reset HEAD~1
 4402  git log
 4403  git reset HEAD~1
 4404  git status
 4405  gi tlog
 4406  git log
 4407  git checkout aims api docs
 4408  git status
 4409  git checkout internal pkg portal
 4410  git status
 4411  git log
 4412  cd ../kubevision-scripts
 4413  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 4414  cd ../akuity-platform
 4415  git status
 4416  git log
 4417  git checkout main
 4418  git branch -D treeview-fix
 4419  git fetch origin
 4420  git checkout -b treeview-fix  origin/treeview-fix
 4421  git log
 4422  git reset HEAD~1 --hard
 4423  git log
 4424  ls
 4425  cd ../kubevision-scripts
 4426  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 4427  git status
 4428  git add portal
 4429  git commit -m "fix frontend build error" -s
 4430  git push --force
 4431  clear
 4432  ak ../Slack
 4433  ak
 4434  cd ../SlackUp
 4435  ./SlackUp
 4436  vim config.yaml
 4437  ./SlackUp
 4438  diskutil list
 4439  sudo diskutil eraseDisk ExFAT MyDisk /dev/disk9
 4440  ./SlackUp
 4441  git status
 4442  git branch
 4443  git checkout ai-contexts-fix
 4444  git status
 4445  git log
 4446  git commit --amend
 4447  cd ../kubevision-scripts
 4448  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 4449  cd ..
 4450  git status
 4451  git push
 4452  git push --set-upstream origin ai-contexts-fix
 4453  clear
 4454  cd ../akuity-platform
 4455  git status
 4456  git add internal
 4457  git commit -m "enhance suggestedContexts prompt" -s
 4458  git fetch origin
 4459  git merge origin/main
 4460  git push
 4461  cd ../kubevision-scripts
 4462  git status
 4463  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 4464  ping jump-tiger.com
 4465  ping ************
 4466  ping ************
 4467  ping ************
 4468  ping ************
 4469  ping ************
 4470  ping ************
 4471  ping ************
 4472  ping ************
 4473  ping ************
 4474  ping ************
 4475  ping ************
 4476  ping ************
 4477  ping ************
 4478  ping ************
 4479  ping ************
 4480  ping ************
 4481  ./refresh-akp.sh
 4482  k8s
 4483  k9s
 4484  ./refresh-akp.sh
 4485  k9s
 4486  ./refresh-akp.sh
 4487  k get po -A
 4488  k logs -n akuity-platform portal-server-5db5955f49-2xtws
 4489  k logs -n akuity-platform portal-server-5db5955f49-2xtws > a.log
 4490  open .
 4491  k logs -n akuity-platform portal-server-5db5955f49-2xtws 
 4492  k logs -n akuity-platform portal-server-5db5955f49-2xtws -f
 4493  k logs -n akuity-platform portal-server-5db5955f49-2xtws -f > b.log
 4494  k logs -n akuity-platform portal-server-5db5955f49-2xtws > c.log
 4495  rm -rf b.log
 4496  open .
 4497  ls
 4498  cat c.log
 4499  sudo vim /etc/hosts
 4500  git fetch origin
 4501  history|grep checkout 
 4502  git status
 4503  git stash
 4504  git status
 4505  make generate-in-container
 4506  git status
 4507  cd ../kubevision-scripts
 4508  sh refresh-akp.sh
 4509  cd ../akuity-platform
 4510  git status
 4511  git add internal
 4512  git commit -m "fix e2e test failure" -s
 4513  git status
 4514  git push
 4515  git branch
 4516  git checkout ai-contexts-fix 
 4517  git status
 4518  clear
 4519  ls
 4520  cd ../akuity-platform
 4521  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 4522  git status
 4523  git stash list
 4524  git stash pop 
 4525  git status
 4526  git log
 4527  git stash
 4528  git stash pop 
 4529  git status
 4530  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 4531  diskutil list
 4532  sudo diskutil eraseDisk ExFAT MyDisk /dev/disk4
 4533  diskutil list
 4534  sudo diskutil eraseDisk ExFAT MyDisk /dev/disk4
 4535  ls
 4536  df -h
 4537  diskutil list
 4538  sudo diskutil eraseDisk ExFAT MyDisk /dev/disk4
 4539  diskutil list
 4540  sudo diskutil eraseDisk ExFAT MyDisk /dev/disk4
 4541  diskutil list
 4542  sudo diskutil eraseDisk ExFAT MyDisk /dev/disk4
 4543  cd ../akuity-platform
 4544  git status
 4545  git diff
 4546  clear
 4547  git status
 4548  cd ../kubevision-scripts
 4549  clear
 4550  sh refresh-akp.sh
 4551  clear
 4552  cd ..
 4553  cd akuity-platform
 4554  git status
 4555  git diff  internal/services/ai/prompt.md
 4556  git checkout internal/services/ai/prompt.md
 4557  git diff
 4558  cd ../kubevision-scripts
 4559  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 4560  cd ..
 4561  cd akuity-platform
 4562  git status
 4563  git add diff
 4564  git diff
 4565  git status
 4566  git add internal/services/ai/prompt.md  internal/services/ai/service.go
 4567  git status
 4568  git commit -m "Split the SuggestedContexts into separate objects " -s
 4569  git status
 4570  git push
 4571  git fetch origin
 4572  git merge origin/main
 4573  git push
 4574  git status
 4575  git dif
 4576  git diff
 4577  git status
 4578  git add internal models
 4579  git status
 4580  git rm  internal/portalapi/organization/data.json  internal/services/ai/data.json
 4581  git status
 4582  git rm  internal/portalapi/organization/data.json  internal/services/ai/data.json
 4583  git rm  internal/portalapi/organization/data.json  internal/services/ai/data.json -f
 4584  git status
 4585  git status 
 4586  git commmit -m "modify the deduplication logic of SuggestedContexts" -s
 4587  git commit -m "modify the deduplication logic of SuggestedContexts" -s
 4588  git status
 4589  git push 
 4590  cd ../kubevision-scripts
 4591  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 4592  cd ../kubevision-scripts
 4593  ls
 4594  git fetch origin
 4595  git merge origin/main
 4596  cd ../akuity-platform
 4597  git fetch origin
 4598  git merge origin/main
 4599  cd ../kubevision-scripts
 4600  ls
 4601  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 4602  cd ..
 4603  cd akuity-platform
 4604  git status
 4605  git push 
 4606  cd ../
 4607  cd akuity-platform
 4608  cd portal/ui
 4609  pnpm i
 4610  ls
 4611  cd ../
 4612  cd ..
 4613  ls
 4614  cd ../kubevision-scripts
 4615  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 4616  sh refresh-akp.sh
 4617  cd ..
 4618  cd akuity-platform
 4619  git status
 4620  rm -rf     internal/services/ai/a.json
 4621  git add internal
 4622  git commit -m "modify the slice allocate" -s
 4623  git fetch origin
 4624  git merge origin/main
 4625  git pull --rebase
 4626  git log
 4627  git push
 4628  /var/folders/mc/lw2530px5r93s78qx6b61fzw0000gn/T/orbstack-open-terminal_4EE29177.sh; exit
 4629  clear
 4630  git checkout main
 4631  git branch
 4632  git branch -D ai-info
 4633  git branch
 4634  git branch -D treeview-fix
 4635  git branch
 4636  git branch -D  feat/filter-tree-view
 4637  git branch
 4638  git status
 4639  git pull
 4640  git log
 4641  clear
 4642  ls
 4643  cd ../kubevision-scripts
 4644  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 4645  sh refresh-akp.sh
 4646  clear
 4647  diskutil list
 4648  sudo diskutil eraseDisk ExFAT MyDisk /dev/disk4
 4649  diskutil list
 4650  sudo diskutil eraseDisk ExFAT MyDisk /dev/disk4
 4651  diskutil list
 4652  sudo diskutil eraseDisk ExFAT MyDisk /dev/disk4
 4653  diskutil list
 4654  sudo diskutil eraseDisk ExFAT MyDisk /dev/disk4
 4655  diskutil list
 4656  sudo dd if=/Users/<USER>/Downloads/debian-live-12.11.0-amd64-gnome.iso of=/dev/disk4 bs=4M status=progress && sync
 4657  ls
 4658  ./SlackUp
 4659  git status
 4660  git diff 
 4661  ls
 4662  cd ..
 4663  cd akuity-platform
 4664  make generate-in-container
 4665  ls
 4666  df -h
 4667  cd /Volumes/Untitled
 4668  ls
 4669  vim test.sh
 4670  sudo vim test.sh
 4671  cd MyPro/python
 4672  ls
 4673  mkdir share
 4674  vim share/test.sh
 4675  python3 sharefile.py --port 9010 --dir ./share
 4676  cd MyPro/python
 4677  ls
 4678  cd share
 4679  ls
 4680  mount.sh
 4681  vim mount.sh
 4682  cd ..
 4683  python3 sharefile.py --port 9010 --dir ./share
 4684  make generate-in-container
 4685  git status
 4686  git add aiks api pkg portal internal
 4687  git status
 4688  git add aims api pkg portal internal
 4689  git commit -m "add api for kargo to retrive extension config" -s
 4690  git pull --rebase
 4691  make generate-in-container
 4692  git status
 4693  git add pkg
 4694  git commit
 4695  git log
 4696  git status
 4697  git rebase --continue
 4698  git log
 4699  git pull --rebase
 4700  git log
 4701  clear
 4702  git checkout -b extension-config
 4703  cd ../kubevision-scripts
 4704  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 4705  git push
 4706  git push --set-upstream origin extension-config
 4707  cd portal/ui
 4708  history |grep pnpm
 4709  PORTAL_SERVER_URL=https://portal-server.akuity-platform pnpm run dev
 4710  pwd
 4711  cd ..
 4712  cd akuity-platform
 4713  make generate-in-container
 4714  git status
 4715  git add aims api internal pkg portal
 4716  git status
 4717  git commit -m "add permission token validation" -s
 4718  git pull --rebase
 4719  git log
 4720  git push
 4721  cd ../kubevision-scripts
 4722  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 4723  cd ../akuity-platform
 4724  git status
 4725  git add internal
 4726  git commit -m "change validate function" -s
 4727  git push
 4728  python3 sharefile.py --port 9010 --dir ./share
 4729  ls
 4730  cd share
 4731  ls
 4732  vim mount.sh
 4733  vim a.sh
 4734  python3 sharefile.py --port 9010 --dir ./share
 4735  cd ..
 4736  python3 sharefile.py --port 9010 --dir ./share
 4737  diskutil lis
 4738  diskutil list
 4739  sudo diskutil eraseDisk ExFAT MyDisk /dev/disk4
 4740  ls
 4741  vim /etc/hosts
 4742  ping jump-tiger.com
 4743  cat /etc/hosts
 4744  ssh <EMAIL>
 4745  ssh admin@************
 4746  wget https://github.com/wnlen/clash-for-linux/archive/refs/heads/master.zip
 4747  scp master.zip <EMAIL>:~/
 4748  ping jump-tiger.com
 4749  vim /etc/hosts
 4750  scp master.zip admin@************:~
 4751  scp ./master.zip admin@************:/home
 4752  sudo scp ./master.zip admin@************:/home
 4753  sudo scp ./master.zip admin@************:/vol1
 4754  ping google.com
 4755  ls
 4756  ak
 4757  cd akuity-platform
 4758  cd ../kubevision-scripts
 4759  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 4760  ssh admin@************
 4761  ls
 4762  cd Pictures
 4763  ls
 4764  cd Photo\ Booth\ Library
 4765  ls
 4766  cd ..
 4767  open .
 4768  ping fnos.net
 4769  ping auth-6218a440.wifi.com
 4770  ping *************
 4771  cd Downloads/syncthing-macos-arm64-v2.0.0-rc.22
 4772  ls
 4773  ./syncthing
 4774  ls
 4775  pwd
 4776  cd ~/
 4777  ls
 4778  cd Pictures
 4779  ls
 4780  pwd
 4781  ssh admin@************
 4782  telnet ************ 7890
 4783  exit
 4784  ssh admin@************
 4785  ssh-copy-id <EMAIL>
 4786  ssh <EMAIL>
 4787  ping jump-tiger.com
 4788  vim ~/.ssh/known_hosts
 4789  ssh-keygen -t rsa -b 4096 -C "<EMAIL>"\n
 4790  ssh-copy-id <EMAIL>
 4791  ssh <EMAIL>
 4792  ls
 4793  vim ~/.zshrc
 4794  tiger
 4795  ls
 4796  tiger
 4797  cd Desktop
 4798  ls
 4799  cd   Desktop - Ming的MacBook Pro (2)
 4800  cd Desktop\ -\ Ming的MacBook\ Pro\ \(2\)
 4801  ls
 4802  tar -cf - tmp | nc ************ 12345
 4803  tiger
 4804  cd tmp
 4805  ls
 4806  bbcp
 4807  brew install bbcp
 4808  rsync -avz --parallel=8 camera admin@************:/vol1/1000
 4809  rsync -avz --parallel=4 camera admin@************:/vol1/1000
 4810  rsync -h
 4811  rsync -h |grep parr
 4812  rsync -h |grep para
 4813  rsync -h |grep pa
 4814  ls
 4815  vim upload.sh
 4816  find camara -type f | parallel -j 4 'rsync -avz --max-size=100M admin@************:/vol1/1000'
 4817  find camera -type f | parallel -j 4 'rsync -avz --max-size=100M admin@************:/vol1/1000'
 4818  ping google.com
 4819  rsync -avz --max-size=100M admin@************:/vol1/1000 camera
 4820  ssh admin@************
 4821  find camera -type f | parallel -j 4 'rsync -avz --max-size=100M admin@************:/vol1/1000'
 4822  find camera -type f | parallel -j 4 'rsync -avz --max-size=100M admin@************:/vol1/1000/Ming'
 4823  find camera -type f | parallel -j 4 'rsync -avzR --max-size=100M admin@************:/vol1/1000/Ming'
 4824  tiger
 4825  cd ~/Desktop
 4826  ls
 4827  sort a.txt | uniq > deduped.txt
 4828  sort answers.txt|uniq > debuped.txt
 4829  cat debuped.txt
 4830  cat debuped.txt|wc -l
 4831  cat answers.txt|wc -l
 4832  rm -rf answers.txt
 4833  mv debuped.txt flb-L.txt
 4834  sed 's/^● *//' a.txt > cleaned.txt\n
 4835  cat cleaned.txt
 4836  vim cleaned.txt
 4837  oepn cleaned.txt
 4838  open cleaned.txt
 4839  ak
 4840  cd ../SlackUp
 4841  ./SlackUp
 4842  tiger
 4843  ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n
 4844  echo "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n"|base64 -d
 4845  cd Desktop
 4846  ls
 4847  cd Desktop\ -\ Ming的MacBook\ Pro\ \(2\)
 4848  cd tmp
 4849  ls
 4850  cd camera
 4851  du -hs .
 4852  tiger
 4853  tar --no-xattrs -cf - camera  | nc ************ 12345
 4854  git branch
 4855  git checkout main
 4856  git log
 4857  git reset HEAD~1 --hard
 4858  git pull
 4859  clear
 4860  ls
 4861  clear
 4862  ak/
 4863  ak
 4864  cd akuity-platform
 4865  ls
 4866  cd ../kubevision-scripts
 4867  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 4868  cd ../akuity-platform
 4869  git fetch origin
 4870  git checkout -b feat/filter-tree-view origin/feat/filter-tree-view
 4871  git log
 4872  cd ../kubevision-scripts
 4873  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 4874  k9s
 4875  history |grep cursor
 4876  chmod +x /Applications/CursorPro.app/Contents/MacOS/CursorPro && sudo xattr -rd com.apple.quarantine /Applications/CursorPro.app
 4877  ls
 4878  cat ~/.zshrc
 4879  history |grep refresh
 4880  ENABLE_KARGO=true ./refresh-akp.sh
 4881  ls
 4882  sh stop-akp.sh
 4883  k get po -A
 4884  kx
 4885  k get po -A
 4886  kx
 4887  ENABLE_KARGO=true ./refresh-akp.sh
 4888  kx
 4889  ENABLE_KARGO=true ./refresh-akp.sh
 4890  ls
 4891  history > h.log
 4892  vim h.log
 4893  sh stop-akp.sh
 4894  clear
 4895  ls
 4896  ENABLE_KARGO=true ./start-akp.sh
 4897  k get po -A
 4898  ENABLE_KARGO=true ./start-akp.sh
 4899  k get po -A
 4900  k get po -A -w
 4901  kx
 4902  ENABLE_KARGO=true ./start-akp.sh
 4903  kx
 4904  k get po -A
 4905  ENABLE_KARGO=true ./start-akp.sh
 4906  sh stop-akp.sh
 4907  kx
 4908  ENABLE_KARGO=true ./start-akp.sh
 4909  k get po -A
 4910  k get po -A -w
 4911  k get po -A
 4912  k get po -A -w
 4913  k get po -A
 4914  k logs -n akuity-platform instances-upgrader-42zhn
 4915  k logs -n akuity-platform instances-upgrader-cwttg
 4916  k get po -A
 4917  k logs -n akuity-platform portal-server-648c895dcc-bmrmk
 4918  cd ../akuity-platform
 4919  git checkout main
 4920  git log
 4921  git pull
 4922  git log
 4923  cd ../kubevision-scripts
 4924  sh stop-akp.sh
 4925  ls
 4926  ENABLE_KARGO=true ./start-akp.sh
 4927  k get po -A
 4928  kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=aims-server -n akuity-platform --timeout=300s
 4929  k get po -A
 4930  kx
 4931  kubectl get pods -l app.kubernetes.io/name=aims-server -n akuity-platform
 4932  ENABLE_KARGO=true ./start-akp.sh
 4933  clear
 4934  vim /Users/<USER>/MyPro/akuity/kubevision-scripts/manifests/argocd.yaml
 4935  k get po -A
 4936  k logs -n akuity-platform akuity-platform
 4937  k logs -n akuity-platform portal-server-65655d9464-zdp89
 4938  k get po -A
 4939  k logs -n akuity-platform platform-controller-5f8cc6fc8b-p7rql
 4940  k get po -A
 4941  ENABLE_KARGO=true ./start-akp.sh
 4942  k logs -n akuity-platform portal-server-65655d9464-zdp89
 4943  vim /Users/<USER>/MyPro/akuity/kubevision-scripts/manifests/argocd.yaml
 4944  ENABLE_KARGO=true ./start-akp.sh
 4945  k9s
 4946  vim  /Users/<USER>/MyPro/akuity/kubevision-scripts/manifests/argocd.yaml
 4947  sh stop-akp.sh
 4948  ENABLE_KARGO=true ./start-akp.sh
 4949  git status
 4950  git diff manifests/argocd.yaml
 4951  cat ~/.zshrc
 4952  which akuity
 4953  cd ../akuity-platform
 4954  ls
 4955  make cli
 4956  ls
 4957  cd dist
 4958  ls
 4959  cd akuity-cli
 4960  ls
 4961  cd v0.23.1-0.20250630061553-cc0b11292f6f.dirty
 4962  ls
 4963  ls -lsht
 4964  date
 4965  which akuity
 4966  mv akuity /usr/local/bin
 4967  sudo mv akuity /usr/local/bin
 4968  akuity -h
 4969  ENABLE_KARGO=true ./start-akp.sh
 4970  k get po -A
 4971  which akuity
 4972  sh stop-akp.sh
 4973  git pull
 4974  git log
 4975  make cli
 4976  cd dist/akuity-cli/v0.23.1-0.20250630061553-cc0b11292f6f.dirty//
 4977  ls
 4978  ls -lsht
 4979  date
 4980  sudo mv akuity /usr/local/bin
 4981  clear
 4982  docker images
 4983  ENABLE_KARGO=true ./start-akp.sh
 4984  ak
 4985  ls
 4986  k get po -A
 4987  k get po -A -w
 4988  k get  pod -l app.kubernetes.io/name=aims-server -n akuity-platform
 4989  k get  pod -l app.kubernetes.io/name=aims-server -n akuity-platform -w
 4990  k get po -A 
 4991  k get po -n akuity-platform
 4992  k get po -A 
 4993  k get pod -l app.kubernetes.io/name=portal-server -n akuity-platform
 4994  k get pod -l app.kubernetes.io/name=aims-server -n akuity-platform
 4995  k get pod -l app.kubernetes.io/name=platform-controller -n akuity-platform 
 4996  k get pod -l app.kubernetes.io/name=aims-server
 4997  k get pod -l app.kubernetes.io/name=aims-server -n akuity-platform
 4998  k get po -n akuity-platform -A
 4999  k get deploy -n akuity-platform
 5000  ENABLE_KARGO=true ./start-akp.sh
 5001  k get deploy -n akuity-platform
 5002  vim /Users/<USER>/MyPro/akuity/kubevision-scripts/manifests/argocd.yaml
 5003  k get po -n akuity-platform
 5004  k logs -n platform-controller-655fdb8cd-26zbh
 5005  k logs -n akuity-platform platform-controller-655fdb8cd-26zbh
 5006  sh stop-akp.sh
 5007  kx
 5008  k get po -A
 5009  ENABLE_KARGO=true ./start-akp.sh
 5010  clear
 5011  k get po -A
 5012  k get po -A -w
 5013  k get po -n akuity-platform
 5014  k logs -n akuity-platform platform-controller-8fbd44668-wjdlg
 5015  k get po -n akuity-platform
 5016  k logs -n akuity-platform portal-server-b5d496ff4-z722t
 5017  clear
 5018  k get po -n akuity-platform
 5019  kubectl logs platform-controller-8fbd44668-wjdlg -n akuity-platform -p\n
 5020  POD=$(kubectl get pods -n akuity-platform -l app.kubernetes.io/name=platform-controller -o jsonpath="{.items[0].metadata.name}")\nkubectl logs "$POD" -n akuity-platform -p\n
 5021  POD=$(kubectl get pods -n akuity-platform -l app.kubernetes.io/name=platform-controller -o jsonpath="{.items[0].metadata.name}")\nkubectl logs "$POD" -n akuity-platform\n
 5022  ls
 5023  clear
 5024  POD=$(kubectl get pods -n akuity-platform -l app.kubernetes.io/name=platform-controller -o jsonpath="{.items[0].metadata.name}")\nkubectl logs "$POD" -n akuity-platform\n
 5025  clear
 5026  POD=$(kubectl get pods -n akuity-platform -l app.kubernetes.io/name=platform-controller -o jsonpath="{.items[0].metadata.name}")\nkubectl logs "$POD" -n akuity-platform\n
 5027  ENABLE_KARGO=true ./start-akp.sh
 5028  POD=$(kubectl get pods -n akuity-platform -l app.kubernetes.io/name=platform-controller -o jsonpath="{.items[0].metadata.name}")\nkubectl logs "$POD" -n akuity-platform\n
 5029  clear
 5030  POD=$(kubectl get pods -n akuity-platform -l app.kubernetes.io/name=platform-controller -o jsonpath="{.items[0].metadata.name}")\nkubectl logs "$POD" -n akuity-platform\n
 5031  ENABLE_KARGO=true ./start-akp.sh
 5032  POD=$(kubectl get pods -n akuity-platform -l app.kubernetes.io/name=platform-controller -o jsonpath="{.items[0].metadata.name}")\nkubectl logs "$POD" -n akuity-platform\n
 5033  clear
 5034  vim log.sh
 5035  sh log.sh
 5036  sh log.sh|grep error
 5037  clear
 5038  akuity argocd apply -f /Users/<USER>/MyPro/akuity/kubevision-scripts/manifests/argocd.yaml --insecure-skip-tls-verify --org-name test-org
 5039  ENABLE_KARGO=true ./start-akp.sh
 5040  sh log.sh|grep error
 5041  cat log.sh
 5042  POD=$(kubectl get pods -n akuity-platform -l app.kubernetes.io/name=platform-controller -o jsonpath="{.items[0].metadata.name}")
 5043  kubectl logs "$POD" -n akuity-platform -p
 5044  k get po -A
 5045  clear
 5046  ls
 5047  ENABLE_KARGO=true ./start-akp.sh
 5048  POD=$(kubectl get pods -n akuity-platform -l app.kubernetes.io/name=platform-controller -o jsonpath="{.items[0].metadata.name}")
 5049  sh log.sh
 5050  k get po -n akuity-platform
 5051  k get svc -n akuity-platform
 5052  k logs -n akuity-platform postgres-7ccb65b454-cxrqn
 5053  clear
 5054  k get po -n akuity-platform
 5055  ENABLE_KARGO=true ./start-akp.sh
 5056  sh log.sh
 5057  ENABLE_KARGO=true ./start-akp.sh
 5058  sh log.sh
 5059  k get po -n akuity-platform
 5060  k logs -n akuity-platform portal-server-59855d5d8-gml2z
 5061  vim  /Users/<USER>/MyPro/akuity/kubevision-scripts/manifests/argocd.yaml
 5062  cd manifests
 5063  ls
 5064  vim kargo.yaml
 5065  ls
 5066  vim argocd.yaml
 5067  cd ..
 5068  ENABLE_KARGO=true ./start-akp.sh
 5069  k get po -n akuity-platform
 5070  k logs -n akuity-platform portal-server-58f965ddd6-h4zpw
 5071  vim  /Users/<USER>/MyPro/akuity/kubevision-scripts/manifests/argocd.yaml
 5072  ENABLE_KARGO=true ./start-akp.sh
 5073  k get po -n akuity-platform
 5074  kx
 5075  k get nodes
 5076  kx
 5077  k logs -n akuity-platform portal-server-85c495bcf4-wjf9x
 5078  kx
 5079  akuity argocd apply -f - --insecure-skip-tls-verify --org-name test-org
 5080  vim manifests/argocd_resources.yaml
 5081  ENABLE_KARGO=true ./start-akp.sh
 5082  ls
 5083  grep aiSreEnabled manifests
 5084  grep -wrn aiSreEnabled manifests
 5085  vim manifests/kargo.yaml
 5086  ENABLE_KARGO=true ./start-akp.sh
 5087  cd ..
 5088  ls
 5089  cd akuity-platform
 5090  ls
 5091  make generate-in-container
 5092  git status
 5093  cd ../kubevision-scripts
 5094  git status
 5095  git diff manifests
 5096  git diff utils.sh
 5097  vim utils.sh
 5098  git diff utils.sh
 5099  git status
 5100  git add manifests utils.sh
 5101  git status
 5102  git diff restart-portal.sh
 5103  git status
 5104  git commit -m "update argo kargo instance api" -s
 5105  git pull --rebase
 5106  git log
 5107  git checkout -b update-instance-api
 5108  git push
 5109  clear
 5110  ls
 5111  cat log.sh
 5112  rm -rf log.sh
 5113  cd kubevision-scripts
 5114  git diff
 5115  git status
 5116  git add manifests
 5117  git status
 5118  git commit -m "update argocd kargo instance config" -s
 5119  git push
 5120  ENABLE_KARGO=true ./start-akp.sh
 5121  pwd
 5122  cd ../
 5123  cd ..
 5124  pwd
 5125  cd ~/Desktop
 5126  ls
 5127  cd Desktop\ -\ Ming的MacBook\ Pro\ \(2\)
 5128  ls
 5129  cd tmp
 5130  ls
 5131  tiger
 5132  parallel
 5133  find camera -mindepth 1 -maxdepth 1 | parallel -j 10 \\n  'rsync -a --progress admin@************:/vol1/1000/'\n
 5134  rsync -a --progress admin@************:/vol1/1000/
 5135  ls
 5136  rsync -a --progress camera admin@************:/vol1/1000/
 5137  ls
 5138  rsync -a --progress camera admin@************:/vol1/1000/
 5139  find camera -mindepth 1 -maxdepth 1 | parallel -j 10 \\n  'rsync -a --progress {} admin@************:/vol1/10000/{/}'
 5140  find camera -mindepth 1 -maxdepth 1 | parallel -j 10 \\n  'rsync -a --progress  admin@************:/vol1/10000/'
 5141  find camera -mindepth 1 -maxdepth 1 | parallel -j 10 \\n  'rsync -a --progress {}  admin@************:/vol1/10000/'
 5142  find camera -mindepth 1 -maxdepth 1 | parallel -j 10 \\n  'rsync -a --progress {} admin@************:/vol1/1000/{/}'
 5143  find camera -mindepth 1 -maxdepth 1
 5144  find camera/Photos -mindepth 1 -maxdepth 1 | parallel -j 10 \\n  'rsync -a --progress {}  admin@************:/vol1/10000/Photos'
 5145  find camera/Photos -mindepth 1 -maxdepth 1 | parallel -j 10 \\n  'rsync -a --progress {}  admin@************:/vol1/10000/camera/Photos'
 5146  find camera/Photos -mindepth 1 -maxdepth 1 | parallel -j 10 \\n  'rsync -a --progress {}  admin@************:/vol1/1000/camera/Photos'
 5147  find camera/Photos -mindepth 1 -maxdepth 1
 5148  cd camera/Photos/Ming
 5149  ls
 5150  cd ..
 5151  camera/Photos/Photos
 5152  ls camera/Photos/Photos
 5153  cd ..
 5154  pwd
 5155  cd Photos/Photos
 5156  ls
 5157  cd ../..
 5158  cd ..
 5159  find camera/Photos -mindepth 1 -maxdepth 1 | parallel -j 10 \\n  'rsync -a --progress {}  admin@************:/vol1/1000/camera/Photos'
 5160  rsync -a --progress camera admin@************:/vol1/1000/
 5161  ls
 5162  cd camera
 5163  ls
 5164  cd Photos
 5165  ls
 5166  ll
 5167  tiger
 5168  ak
 5169  k get po -A
 5170  kx
 5171  k get po -A
 5172  kx
 5173  k get po -A
 5174  k logs -n akuity-platform portal-server-785f559676-tsfxt
 5175  clear
 5176  ls
 5177  cd kubevision-scripts
 5178  ls
 5179  git checkout main
 5180  git status
 5181  git diff
 5182  git checkout manifests
 5183  git status
 5184  git pull
 5185  git checkout main
 5186  git pull
 5187  git reset HEAD~1 --hard
 5188  git pull
 5189  git log
 5190  clear
 5191  ENABLE_KARGO=true ./start-akp.sh
 5192  cd ../akuity-platform
 5193  git pull
 5194  git log
 5195  cd ../kubevision-scripts
 5196  ENABLE_KARGO=true ./start-akp.sh
 5197  git status
 5198  vim utils.sh
 5199  k get pod -l app.kubernetes.io/name=argocd-application-controller -n akuity
 5200  k get po -A
 5201  k describe po -n akuity argocd-application-controller-757769dc8-4qkkl
 5202  ls
 5203  sh stop-akp.sh
 5204  ls
 5205  vim utils.sh
 5206  ENABLE_KARGO=true ./start-akp.sh
 5207  vim utils.sh
 5208  git diff
 5209  vim utils.sh
 5210  ENABLE_KARGO=true ./start-akp.sh
 5211  kx
 5212  k get deployments.apps -A
 5213  kx
 5214  k get deployments.apps -A
 5215  k logs -n argocd-kxe13to84c1dkg4a k3s
 5216  k get po -n argocd-kxe13to84c1dkg4a
 5217  k logs -n argocd-kxe13to84c1dkg4a k3s-58757f5c89-2ctqb
 5218  k get deployments.apps -A
 5219  git status
 5220  k9s
 5221  kx
 5222  k9s
 5223  kx
 5224  k9s
 5225  kx
 5226  k9s
 5227  kx
 5228  k9s
 5229  k get po -A
 5230  k get po -A -oyaml > a.yaml
 5231  vim a.yaml
 5232  k get po -n argocd-kxe13to84c1dkg4a k3s-58757f5c89-2ctqb -oyal
 5233  k get po -n argocd-kxe13to84c1dkg4a k3s-58757f5c89-2ctqb -oyaml
 5234  k get po -n argocd-kxe13to84c1dkg4a k3s-58757f5c89-2ctqb -oyaml |grep image
 5235  k get po -n argocd-kxe13to84c1dkg4a k3s-58757f5c89-2ctqb -oyaml |grep -w image
 5236  k get po -n argocd-kxe13to84c1dkg4a k3s-webhook-8555c5c957-w
 5237  k get po -n argocd-kxe13to84c1dkg4a k3s-webhook-8555c5c957-wmwcn -yaml
 5238  k get po -n argocd-kxe13to84c1dkg4a k3s-webhook-8555c5c957-wmwcn -oyaml
 5239  k get po -n argocd-kxe13to84c1dkg4a k3s-webhook-8555c5c957-wmwcn -oyaml |grep -w image
 5240  k get po -n argocd-kxe13to84c1dkg4a k3s-webhook-8555c5c957-wfs5l -oyaml |grep -w image
 5241  k get po -n argocd-kxe13to84c1dkg4a k3s-58757f5c89-2ctqb -oyaml |grep -w image
 5242  cd /Users/<USER>/MyPro/akuity/akuity-platform && go vet ./internal/services/organizations/organizations.go
 5243  cd /Users/<USER>/MyPro/akuity/akuity-platform && go test ./internal/services/organizations/... -v
 5244  cd /Users/<USER>/MyPro/akuity/akuity-platform && go test ./internal/services/features/... -v
 5245  cd internal && go test -v ./services/features -run TestNewEnvSystemFeatureGates
 5246  git status
 5247  git diff
 5248  git status
 5249  git add internal
 5250  git status
 5251  git log
 5252  git status
 5253  git stash
 5254  git status
 5255  git branch
 5256  git tag
 5257  clear
 5258  git status
 5259  k get po -A
 5260  k9s
 5261  k get cm -n akuity-platform
 5262  k get cm -n akuity-platform portal-server -oyaml
 5263  k edit cm -n akuity-platform portal-server 
 5264  k get po -n akuity-platform
 5265  k delete po -n akuity-platform portal-server-55cff5447f-c6p25 aims-server-6fb47667c6-22hml
 5266  tiger
 5267  ping jump-tiger.com
 5268  tiger
 5269  rm -rf ~/Library/Application\ Support/Google/Chrome\nrm -rf ~/Library/Caches/Google/Chrome\nrm -rf ~/Library/Google/GoogleSoftwareUpdate\n
 5270  pwd
 5271  rm -rf ~/Library/Application\ Support/Google/Chrome
 5272  rm -rf Library/Caches/Google
 5273  cd  Library/Caches/Google
 5274  ls
 5275  cd Chrome
 5276  ls
 5277  cd ..
 5278  du hs .
 5279  cd ..
 5280  ls
 5281  cd Caches
 5282  ls
 5283  du -sh .
 5284  ls Library/Google
 5285  rm -rf Library/Google
 5286  rsync -a --progress camera admin@************:/vol1/1000/
 5287  cd ~/Desktop
 5288  ls
 5289  cd Desktop\ -\ Ming的MacBook\ Pro\ \(2\)
 5290  cd tmp
 5291  rsync -a --progress camera admin@************:/vol1/1000/
 5292  tiger
 5293  rsync -a --progress camera admin@************:/vol1/1000/
 5294  ak
 5295  cd ../SlackUp
 5296  ./SlackUp
 5297  tiger
 5298  rsync -a --progress camera admin@************:/vol1/1000/
 5299  cd ~/Desktop/Desktop\ -\ Ming的MacBook\ Pro\ \(2\)/tmp
 5300  rsync -a --progress camera admin@************:/vol1/1000/
 5301  ls
 5302  rsync -a --progress cellphone admin@************:/vol1/1000/
 5303  ls
 5304  cd cellphone
 5305  ls
 5306  cd ~/Pictures
 5307  ls
 5308  cd Photos\ Library.photoslibrary
 5309  ls
 5310  cd ..
 5311  ls
 5312  open .
 5313  pwd
 5314  open .
 5315  cd akuity-platform
 5316  ls
 5317  git branch
 5318  git branch -D   feat/filter-tree-view
 5319  git branch
 5320  git branch -D extension-config
 5321  git branch
 5322  git branch -D ai-contexts-fix
 5323  git pull
 5324  git log
 5325  clear
 5326  git status
 5327  git pull
 5328  ping github.com
 5329  ping goole.com
 5330  vim ~/.zshrc
 5331  source ~/.zshrc
 5332  ping google.com
 5333  telnet ************ 7890
 5334  ping http://************
 5335  ping ************
 5336  exit
 5337  telnet ************ 7890
 5338  env
 5339  vim ~/.zshrc
 5340  source ~/.zshrc
 5341  ping jump-tiger.com
 5342  tiger
 5343  ping github.com
 5344  git pull
 5345  git checkout enable-k3s-proxy
 5346  git checkout -b enable-k3s-proxy
 5347  cd /Users/<USER>/MyPro/akuity && go vet ./akuity-platform/internal/services/features/features.go
 5348  tail -n 50 /Users/<USER>/MyPro/akuity/akuity-platform/models/liquibase/changelogs/changelog-root.yaml
 5349  grep -n "changeSet:" /Users/<USER>/MyPro/akuity/akuity-platform/models/liquibase/changelogs/changelog-root.yaml | tail -n 1
 5350  tail -n 30 /Users/<USER>/MyPro/akuity/akuity-platform/models/liquibase/changelogs/changelog-root.yaml
 5351  tail -n 20 /Users/<USER>/MyPro/akuity/akuity-platform/models/liquibase/changelogs/changelog-root.yaml
 5352  cd akuity-platform
 5353  git status
 5354  git add internal models
 5355  git status
 5356  git add internal
 5357  git commit -m "feat: support enable k3s informer proxy option" -s
 5358  git push
 5359  git push --set-upstream origin enable-k3s-proxy
 5360  git pull
 5361  clear
 5362  cd ../kubevision-scripts
 5363  ENABLE_KARGO=true ./start-akp.sh
 5364  ls
 5365  cd manifests
 5366  ls
 5367  vim kargo.yaml
 5368  k get po -A
 5369  cd akuity-platform
 5370  cd models
 5371  make generate
 5372  git status
 5373  git diff sql
 5374  git add sql
 5375  git status
 5376  git commit -m "update db" -s
 5377  git push
 5378  ak 
 5379  cd ../SlackUp
 5380  ./SlackUp
 5381  ping portal-server.akuity-platform
 5382  cd ../akuity
 5383  cd akuity-platform
 5384  cd portal/ui
 5385  PORTAL_SERVER_URL=https://portal-server.akuity-platform pnpm run dev
 5386  cd ../
 5387  cd ../kubevision-scripts
 5388  k get po -A
 5389  k logs -n akuity-platform portal-server-f677c5c5b-rdvgn
 5390  k logs -n akuity-platform portal-server-f677c5c5b-rdvgn -w
 5391  k logs -n akuity-platform portal-server-f677c5c5b-rdvgn 
 5392  k get po -A
 5393  k9s
 5394  telent 127.0.0.1 15022
 5395  telnet 127.0.0.1 15022
 5396  cd ../kubevision-scripts
 5397  ./refresh-akp.sh
 5398  k9s
 5399  ./refresh-akp.sh
 5400  sh refresh-akp.sh
 5401  cd ../akuity
 5402  cd kubevision-scripts
 5403  sh refresh-akp.sh
 5404  k get po -A
 5405  k logs -n akuity-platform aims-server-67ccc57778-ghrxj
 5406  k logs -n akuity-platform aims-server-67ccc57778-ghrxj > a.log
 5407  vim a.log
 5408  k logs -n akuity-platform portal-server-7944b8c8cd-gh9fg
 5409  k logs -n akuity-platform portal-server-7944b8c8cd-gh9fg > b.log
 5410  vim b.log
 5411  k logs -n akuity-platform portal-server-7944b8c8cd-gh9fg > b.log
 5412  vim b.log
 5413  sh refresh-akp.sh
 5414  k get po -n akuity-platform
 5415  k logs -n akuity-platform portal-server-94ccbbf4f-wrwrb 
 5416  k logs -n akuity-platform portal-server-94ccbbf4f-wrwrb  >> b.log
 5417  vim b.log
 5418  k get po -n akuity-platform portal-server-94ccbbf4f-wrwrb
 5419  k get po -n akuity-platform portal-server-94ccbbf4f-wrwrb -oyaml
 5420  sh refresh-akp.sh
 5421  k9s
 5422  cd ..
 5423  cd akuity-platform
 5424  git status
 5425  git diff  internal/aimsapi/patch_feature_gates_v1.go
 5426  git checkout  internal/aimsapi/patch_feature_gates_v1.go
 5427  git diff internal/services/features/features.go
 5428  git checkout internal/services/features/features.go
 5429  git dff  internal/services/organizations/organizations.go
 5430  git diff  internal/services/organizations/organizations.go
 5431  git add internal
 5432  git status
 5433  cd ..
 5434  cd akuity-platform
 5435  ls
 5436  cd ../akuity-platform
 5437  git status
 5438  git diff
 5439  git status
 5440  git add models
 5441  git status
 5442  git commit -m "remote database migration" -s
 5443  cd models
 5444  make generate
 5445  git status
 5446  git add sql
 5447  git commit --amend
 5448  git push
 5449  git fetch origin
 5450  git merge origin/main
 5451  git push
 5452  tiger
 5453  brew install nmap
 5454  nmap -sP 192.168.1.0/24
 5455  tiger
 5456  ssh admin@192.165.1.43
 5457  ping ************
 5458  ssh admin@************
 5459  ssh admin@************
 5460  ssh admin@************
 5461  nmap -sP 192.168.1.0/24
 5462  clear
 5463  tiger
 5464  ping ptepractise.chenjunbo.top
 5465  tiger
 5466  ping jump-tiger.com
 5467  ak
 5468  cd akuity-platform
 5469  ls
 5470  git status
 5471  git fetch origin
 5472  git merge origin/main
 5473  git log
 5474  git status
 5475  ls
 5476  cd akuity-platform
 5477  make generate
 5478  make generate-in-container
 5479  git status
 5480  git checkout main
 5481  git status
 5482  git checkout api
 5483  git pull
 5484  git checkout -b extension-setting
 5485  git status
 5486  make generate-in-container
 5487  git status
 5488  git add aims api internal pkg portal
 5489  git status
 5490  git commit -m "add org id and instance id into extension settings" -s
 5491  git push
 5492  git push --set-upstream origin extension-setting
 5493  cd ../kubevision-scripts
 5494  sh refresh-akp.sh
 5495  cd ../akuity-platform
 5496  git status
 5497  git add internal
 5498  git status
 5499  git commit -m "update" -s
 5500  git push
 5501  git status
 5502  git branch
 5503  git checkout enable-k3s-proxy
 5504  git status
 5505  git fetch origin
 5506  git merge origin/main
 5507  git log
 5508  clear
 5509  k get po -A
 5510  k get job -A
 5511  k get po -A
 5512  cd models
 5513  ls
 5514  make generate
 5515  git status
 5516  git diff sql
 5517  git status
 5518  clear
 5519  cd ..
 5520  ls
 5521  sh refresh-akp.sh
 5522  cd ../kubevision-scripts
 5523  sh refresh-akp.sh
 5524  cd ../akuity-platform
 5525  ls
 5526  cd models
 5527  ls
 5528  cd ../
 5529  cd ../kubevision-scripts
 5530  ls
 5531  ./start-akp.sh
 5532  k get po -n akuity-platform
 5533  k logs -n akuity-platform portal-server-757c4c5fd8-tsbfz
 5534  akuity argocd apply -f /Users/<USER>/MyPro/akuity/kubevision-scripts/manifests/argocd.yaml --insecure-skip-tls-verify --org-name test-org
 5535  clear
 5536  k get po -n akuity-platform
 5537  k logs -n akuity-platform aims-server-68bbc549c5-gthc5
 5538  clear
 5539  k get po -n akuity-platform
 5540  k logs -n akuity-platform portal-server-757c4c5fd8-tsbfz
 5541  k logs -n akuity-platform portal-server-757c4c5fd8-tsbfz |grep error
 5542  k logs -n akuity-platform platform-controller-6799747bb6-84hj7
 5543  k logs -n akuity-platform platform-controller-6799747bb6-84hj7|grep error
 5544  ls
 5545  cd ../kubevision-scripts
 5546  ls
 5547  vim manifests/argocd.yaml
 5548  ls
 5549  vim manifests/argocd.yaml
 5550  vim manifests/argocd_resources.yaml
 5551  ls
 5552  sh start-akp.sh
 5553  cd akuity-platform
 5554  ls
 5555  cd models
 5556  make generate
 5557  git status
 5558  git diff sql
 5559  git add liquibase sql
 5560  git status
 5561  git commit -m "update db" -s
 5562  git status
 5563  git push
 5564  git pull
 5565  git log
 5566  clear
 5567  ls
 5568  cd ..
 5569  git status
 5570  ls
 5571  k get po -A
 5572  k89s
 5573  k9s
 5574  ENABLE_KARGO=true ./start-akp.sh
 5575  :pod all
 5576  cd ../
 5577  cd akuity-platform
 5578  ls
 5579  cd ../kubevision-scripts
 5580  ls
 5581  sh stop-akp.sh
 5582  k9s
 5583  ls
 5584  ENABLE_KARGO=true ./start-akp.sh
 5585  ls
 5586  vim manifests/kargo.yaml
 5587  sh stop-akp.sh
 5588  kx
 5589  k get po -A
 5590  cd ../
 5591  cd akuity-platform
 5592  ls
 5593  git status
 5594  git checkout main
 5595  git log
 5596  cd ../kubevision-scripts
 5597  ENABLE_KARGO=true ./start-akp.sh
 5598  k9s
 5599  k get po -A
 5600  k describe po -n akuity argocd-application-controller-8558799b9b-9mlh5
 5601  k get po -A
 5602  sudo ls
 5603  sudo tee -a /etc/hosts
 5604  k get po -A
 5605  k logs -n akuity-platform portal-server-746d445679-96mcv
 5606  cd ../
 5607  cd akuity-platform
 5608  ls
 5609  git branch
 5610  git checkout enable-k3s-proxy
 5611  cd ../kubevision-scripts
 5612  ./start-akp.sh
 5613  k9s
 5614  cd ..
 5615  git clone https://github.com/akuityio/akdev.git
 5616  ls
 5617  cd akdev
 5618  ls
 5619  make build
 5620  ls
 5621  cd dist
 5622  ls
 5623  chmod +x akdev
 5624  ./akdev awsconfig init --roles=view,edit,admin
 5625  curl "https://s3.amazonaws.com/aws-cli/awscli-bundle.zip" -o "awscli-bundle.zip"\nunzip awscli-bundle.zip\nsudo ./awscli-bundle/install -i /usr/local/aws -b /usr/local/bin/aws
 5626  python2
 5627  python
 5628  python3
 5629  sudo ln -s /usr/bin/python3 /usr/local/bin/python\n
 5630  python --version
 5631  python3 version
 5632  python3 --version
 5633  which python
 5634  python --version
 5635  sudo ln -s /usr/bin/python3 /usr/local/bin/python\n
 5636  sudo ln -s /opt/homebrew/bin/python3 /usr/local/bin/python
 5637  python --version
 5638  xcode-select --install
 5639  clear
 5640  ls
 5641  cat install.sh
 5642  ls
 5643  cat install.sh
 5644  /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"\n
 5645  brew install python
 5646  sudo ln -s $(which python3) /usr/local/bin/python
 5647  python --version
 5648  brew install awscli
 5649  aws sso login
 5650  clear
 5651  git statu
 5652  git status
 5653  git branch
 5654  git checkout main
 5655  git status
 5656  git pull
 5657  git log
 5658  clear
 5659  ls
 5660  rm -rf awscli-bundle
 5661  aws
 5662  tiger
 5663  ping jump-tiger.com
 5664  ping google.com
 5665  vim ~/.aws/credentials
 5666  sudo vim ~/.aws/credentials
 5667  mkdir -p ~/.aws
 5668  sudo vim ~/.aws/credentials
 5669  ak
 5670  cd akdev
 5671  ls
 5672  cd dist
 5673  ls
 5674  cp akdev /usr/local/bin
 5675  sudo cp akdev /usr/local/bin
 5676  akdev awsconfig init --roles=view,edit,admin
 5677  akdev kubeconfig init --roles=view,edit,admin
 5678  akdev kubeconfig init --roles=edit,admin
 5679  akdev kubeconfig init --roles=admin
 5680  aws -h
 5681  aws
 5682  aws help
 5683  aws sso login
 5684  akdev kubeconfig init --roles=admin
 5685  k get po -A
 5686  kx
 5687  k get po -A
 5688  ping google.com
 5689  kx
 5690  k get po -A
 5691  oidc-login
 5692  k get po -A
 5693  cat ~/.aws/credentials
 5694  cat ~/.aws/config
 5695  cd ak
 5696  ak
 5697  ls
 5698  cd ak
 5699  cd akuity-platform
 5700  ls
 5701  git status
 5702  git branch
 5703  git checkout enable-k3s-proxy
 5704  git fetch origin
 5705  git merge origin/main
 5706  cd models
 5707  ls
 5708  make generate
 5709  git status
 5710  git add liquibase/changelogs/changelog-root.yaml   sql/schema.sql
 5711  git status
 5712  cd ..
 5713  git status
 5714  git commit
 5715  git push 
 5716  clear
 5717  k get po -A
 5718  kx
 5719  k get po -A
 5720  ping 3783155d6148bb625638419347c61de2.gr7.us-west-2.eks.amazonaws.com
 5721  aws configure sso
 5722  k get po -A
 5723  kx
 5724  ping stage.akuity.io
 5725  k get po -A
 5726  ping google.com
 5727  kx
 5728  k get po -A
 5729  ping us-east-2.eks.amazonaws.com
 5730  k get po -A
 5731  kx
 5732  k get po -A
 5733  ping bff193467800a1518398a886062d24b4.yl4.us-east-2.eks.amazonaws.com/api
 5734  ping bff193467800a1518398a886062d24b4.yl4.us-east-2.eks.amazonaws.com
 5735  kubectl get po -A
 5736  ping BFF193467800A1518398A886062D24B4.yl4.us-east-2.eks.amazonaws.com
 5737  kubectl get po -A
 5738  ping BFF193467800A1518398A886062D24B4.yl4.us-east-2.eks.amazonaws.com
 5739  kubectl get po -A
 5740  cat ~/.kube/config
 5741  cat ~/.kube/config|grep oidc
 5742  brew install kubectl-oidc-login\n
 5743  brew install kubelogin
 5744  kubectl get po -A
 5745  clear
 5746  k get po -A
 5747  k get secrets -n akuity-platform
 5748  k get secrets -n akuity-platform db-secrets -oyaml
 5749  echo "dXNlcj1tYXN0ZXJ1c2VyIGRibmFtZT1wb3N0Z3JlcyBob3N0PWFrcC1wb3J0YWxkYi1zdGctdXNlMi5wcm94eS1jdmhsZG4zOHdzcmoudXMtZWFzdC0yLnJkcy5hbWF6b25hd3MuY29tIHBvcnQ9NTQzMiBzc2xtb2RlPXJlcXVpcmUgcGFzc3dvcmQ9MWhHZzZiQjJJWVlVMlQwaTRLSUtrcmNWSjVLeTRoaU9oQzNDRUM3" |base64 -d
 5750  brew install postgresql\n
 5751  pg_dump -h
 5752  pg_dump --help
 5753  pg_dump \\n  --username=masteruser \\n  --host=akp-portaldb-stg-use2.proxy-cvhldn38wsrj.us-east-2.rds.amazonaws.com \\n  --port=5432 \\n  --dbname=postgres \\n  --format=custom \\n  --file=postgres.dump \\n  --password=1hGg6bB2IYYU2T0i4KIKkrcVJ5Ky4hiOhC3CEC7 \\n  --sslmode=require
 5754  export Password="1hGg6bB2IYYU2T0i4KIKkrcVJ5Ky4hiOhC3CEC7"
 5755  export PGPASSWORD='1hGg6bB2IYYU2T0i4KIKkrcVJ5Ky4hiOhC3CEC7'
 5756  pg_dump \\n  --username=masteruser \\n  --host=akp-portaldb-stg-use2.proxy-cvhldn38wsrj.us-east-2.rds.amazonaws.com \\n  --port=5432 \\n  --dbname=postgres \\n  --format=custom \\n  --file=postgres.dump \\n  --sslmode=require
 5757  pg_dump \\n  --username=masteruser \\n  --host=akp-portaldb-stg-use2.proxy-cvhldn38wsrj.us-east-2.rds.amazonaws.com \\n  --port=5432 \\n  --dbname=postgres \\n  --format=custom \\n  --file=postgres.dump
 5758  cd ak
 5759  ak
 5760  cd akuity-platform
 5761  cd dist
 5762  ls
 5763  cd ..
 5764  ls
 5765  cd ..
 5766  ls
 5767  cd akdev
 5768  cd dist
 5769  ls
 5770  ls -lsht
 5771  cat postgres.dump
 5772  ls -lsht
 5773  ls -lsh
 5774  ls -lst
 5775  ls -lsht
 5776  ls -lst
 5777  ls -lsht
 5778  export PGPASSWORD='1hGg6bB2IYYU2T0i4KIKkrcVJ5Ky4hiOhC3CEC7'
 5779  pg_dump \\n  --host=akp-portaldb-stg-use2.proxy-cvhldn38wsrj.us-east-2.rds.amazonaws.com \\n  --port=5432 \\n  --username=masteruser \\n  --dbname=postgres \\n  --format=custom \\n  --file=selected_tables.dump \\n  --sslmode=require \\n  --table=k8s_resources_data \\n  --table=ai_conversation
 5780  pg_dump \\n  --host=akp-portaldb-stg-use2.proxy-cvhldn38wsrj.us-east-2.rds.amazonaws.com \\n  --port=5432 \\n  --username=masteruser \\n  --dbname=postgres \\n  --format=custom \\n  --file=selected_tables.dump \\n  --table=argo_cd_cluster_k8s_object \\n  --table=ai_conversation
 5781  ak
 5782  cd ../
 5783  ls
 5784  cd akuity
 5785  ls
 5786  cd ak
 5787  cd akdev
 5788  ls
 5789  cd dist
 5790  ls
 5791  ls -lsht
 5792  pg_dump \\n  --host=akp-portaldb-stg-use2.proxy-cvhldn38wsrj.us-east-2.rds.amazonaws.com \\n  --port=5432 \\n  --username=masteruser \\n  --dbname=postgres \\n  --format=custom \\n  --file=selected_tables.dump \\n  --table=argo_cd_cluster_k8s_object \\n  --table=ai_conversation
 5793  ls -lsht
 5794  ls -lsh
 5795  ls -lsht
 5796  ls -lsh
 5797  ls -ls
 5798  ls -lsh
 5799  tiger
 5800  ls -lsh
 5801  ak
 5802  cd ../SlackUp
 5803  ./SlackUp
 5804  ls
 5805  cd ~
 5806  ak
 5807  cd akdev
 5808  ls
 5809  cd dist
 5810  ls
 5811  pg_restore \\n  --dbname=postgres \\n  --username=postgres \\n  --host=localhost \\n  --port=5432 \\n  --verbose \\n  selected_tables.dump
 5812  PGPASSWORD='postgres' pg_restore \\n  --dbname=test_postgres \\n  --username=postgres \\n  --host=localhost \\n  --port=5432 \\n  --verbose \\n  selected_tables.dump
 5813  PGPASSWORD="postgres" pg_restore \\n  --dbname=test_postgres \\n  --username=postgres \\n  --host=localhost \\n  --port=5432 \\n  --verbose \\n  --no-owner \\n  --no-comments \\n  --disable-triggers \\n  selected_tables.dump
 5814  PGPASSWORD="postgres" pg_restore \\n  --dbname=test_postgres \\n  --username=postgres \\n  --host=localhost \\n  --port=5432 \\n  --port=5432 \\n  --verbose \\n  --data-only \\n  --no-owner \\n  --disable-triggers \\n  selected_tables.dump\n
 5815  psql
 5816  psql -U postgres -h localhost -d test_postgres -c \\n"TRUNCATE TABLE ai_conversation, argo_cd_cluster_k8s_object RESTART IDENTITY CASCADE;"\n
 5817  PGPASSWORD="postgres" pg_restore \\n  --dbname=test_postgres \\n  --username=postgres \\n  --host=localhost \\n  --port=5432 \\n  --verbose \\n  --data-only \\n  --no-owner \\n  --disable-triggers \\n  selected_tables.dump\n
 5818  psql -U postgres -h localhost -d test_postgres -c "SELECT COUNT(*) FROM ai_conversation;"\npsql -U postgres -h localhost -d test_postgres -c "SELECT COUNT(*) FROM argo_cd_cluster_k8s_object;"\n
 5819  ls
 5820  vim query.sql
 5821  psql -U postgres -h localhost -d test_postgres -c query.sql
 5822  ls
 5823  psql -U postgres -h localhost -d test_postgres -f query.sql
 5824  ls
 5825  vim local.sh
 5826  ls
 5827  vim local.sh
 5828  sh local.sh
 5829  psql -U masteruser -h akp-portaldb-stg-use2.proxy-cvhldn38wsrj.us-east-2.rds.amazonaws.com -d postgres -f query.sql
 5830  PGPASSWORD='1hGg6bB2IYYU2T0i4KIKkrcVJ5Ky4hiOhC3CEC7' psql -U masteruser -h akp-portaldb-stg-use2.proxy-cvhldn38wsrj.us-east-2.rds.amazonaws.com -d postgres -f query.sql
 5831  vim online.sh
 5832  PGPASSWORD='1hGg6bB2IYYU2T0i4KIKkrcVJ5Ky4hiOhC3CEC7' psql -U masteruser -h akp-portaldb-stg-use2.proxy-cvhldn38wsrj.us-east-2.rds.amazonaws.com -d postgres -f query.sql
 5833  vim query.sql
 5834  PGPASSWORD='1hGg6bB2IYYU2T0i4KIKkrcVJ5Ky4hiOhC3CEC7' psql -U masteruser -h akp-portaldb-stg-use2.proxy-cvhldn38wsrj.us-east-2.rds.amazonaws.com -d postgres -f query.sql
 5835  sh local.sh
 5836  sh online.sh
 5837  vim online.sh
 5838  vim query.sql
 5839  sh online.sh
 5840  k get po -A
 5841  k get po -n akuity-platform
 5842  k get po -n akuity-platform |grep post
 5843  ak
 5844  cd ../SlackUp
 5845  ./SlackUp
 5846  ak
 5847  cd kubevision-scripts
 5848  ls
 5849  cd manifests
 5850  ls
 5851  cat kargo.yaml
 5852  ls
 5853  cat argocd.yaml
 5854  cd ..
 5855  cd akuity-platform
 5856  PUSH_LATEST=true IMAGE_REPO=us-docker.pkg.dev/akuity/akp/akuity-platform TAG=latest make image
 5857  ping google.com
 5858  ssh admin@**************
 5859  ls ~/Desktop/Desktop\ -\ Ming的MacBook\ Pro\ \(2\)
 5860  scp ~/Desktop/Desktop\ -\ Ming的MacBook\ Pro\ \(2\)/mixed.yaml admin@**************:/vol1
 5861  wget https://github.com/MetaCubeX/metacubexd/archive/refs/heads/main.zip
 5862  scp main.zip admin@**************:/vol1
 5863  rm -rf main.zip
 5864  telnet ************** 9090
 5865  ssh admin@**************
 5866  ssh admin@************
 5867  ping ************
 5868  ssh admin@************
 5869  ping ************
 5870  ping ************
 5871  ping ************
 5872  ssh admin@************
 5873  telent ************ 9090
 5874  telnet ************ 9090
 5875  telnet ************ 9090
 5876  wget https://github.com/wnlen/clash-for-linux/archive/refs/heads/master.zip
 5877  rm -rf master.zip.1
 5878  scp main.zip admin@**************:/vol1
 5879  ls
 5880  scp ./main.zip admin@************:/vol1
 5881  ls
 5882  scp ./master.zip admin@************:/vol1
 5883  ssh admin@************
 5884  ping google.com
 5885  tiger
 5886  clar
 5887  clear
 5888  ls
 5889  tiger
 5890  sudo -i
 5891  tiger
 5892  ssh admin@************
 5893  vim server.go
 5894  go run server.go
 5895  curl  http://127.0.0.1:8080/
 5896  tiger
 5897  ls
 5898  vim set-wifi.sh
 5899  sh set-wifi.sh
 5900  sudo sh set-wifi.sh
 5901  ls
 5902  vim set-wifi.sh
 5903  cat set-wifi.sh
 5904  WIFI_SERVICE=$(networksetup -listallnetworkservices | grep -i "Wi-Fi")
 5905  SSID=$(networksetup -getairportnetwork "$WIFI_SERVICE" | awk -F': ' '{print $2}')
 5906  PROXY_IP="************"
 5907  PROXY_PORT="7890"
 5908  networksetup -setwebproxy "$WIFI_SERVICE" $PROXY_IP $PROXY_PORT
 5909  networksetup -setsecurewebproxy "$WIFI_SERVICE" $PROXY_IP $PROXY_PORT
 5910  networksetup -setsocksfirewallproxy "$WIFI_SERVICE" $PROXY_IP $PROXY_PORT
 5911  sh set-wifi.sh
 5912  vim setporxy.sh
 5913  telnet ************ 7890
 5914  vim setporxy.sh
 5915  ls
 5916  rm -rf set-wifi.sh
 5917  sh setporxy.sh
 5918  ping google.com
 5919  tiger
 5920  ssh -L 9190:127.0.0.1:9190 admin@************
 5921  ssh -N -L 9190:127.0.0.1:9190 admin@************
 5922  tiger
 5923  sh setporxy.sh
 5924  ping google.com
 5925  tiger
 5926  sh setporxy.sh
 5927  ping google.com
 5928  ping baidu.com
 5929  ping google.com
 5930  ip route show table all
 5931  ping ************
 5932  ping ************
 5933  ifconfig en0 | grep ether
 5934  tiger
 5935  ping baidu.com
 5936  ping google.com
 5937  ping baidu.com
 5938  ping google.com
 5939  arp -a
 5940  ping ************
 5941  tiger
 5942  curl -x http://************:7893 https://www.google.com
 5943  ls
 5944  sh setporxy.sh
 5945  sudo iptables -t nat -L -v -n --line-numbers
 5946  ping tiger
 5947  ping ************
 5948  ping ************
 5949  history|grep 192.168.31
 5950  ssh  admin@**************
 5951  ls
 5952  ssh admin@**************
 5953  ssh  admin@**************
 5954  ping **************
 5955  ssh admin@**************
 5956  sh setporxy.sh
 5957  ak
 5958  cd akuity-platform
 5959  git status
 5960  git fetch origin
 5961  git merge origin/main
 5962  git status
 5963  git push
 5964  git pull --rebase
 5965  git push
 5966  git log
 5967  git reset HEAD -i a10588f6a34d0ccdccc87f818aea2e91ecf9f48b --hard
 5968  git reset --hard a10588f6a34d0ccdccc87f818aea2e91ecf9f48b
 5969  git log
 5970  git pull 
 5971  git status
 5972  git log
 5973  git status
 5974  git checkout main
 5975  git pull 
 5976  git checkout -b proxy-tmp
 5977  git branch
 5978  git checkout enable-k3s-proxy
 5979  git branch
 5980  git checkout proxy-tmp
 5981  git cherry-pick f2790a495ac3b48605e5cd7c27a1a7d80676770c
 5982  git status
 5983  mv   internal/services/k8sresource/data.sql ~/.
 5984  git stauts
 5985  git status
 5986  git add internal models
 5987  git status
 5988  git cherry-pick --continue
 5989  git cherry-pick b405e592154f9c51fdfdb94f6adbba9e86805dd9
 5990  git status
 5991  git add models
 5992  git status
 5993  git cherry-pick --continue
 5994  git status
 5995  git log
 5996  git cherry-pick ca49dd3183bd66d22bbd2418279d20f268b5ce57
 5997  git status
 5998  git add internal models
 5999  git status
 6000  git cherry-pick --continue
 6001  git status
 6002  git cherry-pick 5e9fdc2e52df43cb5fcb3432af3cb54cfb481a1d
 6003  git stauts
 6004  git status
 6005  git add models
 6006  git status
 6007  cd models
 6008  make generate
 6009  git status
 6010  make generate
 6011  git status
 6012  git add sql
 6013  git status
 6014  git cherry-pick --continue
 6015  git log
 6016  git push
 6017  cd ..
 6018  PUSH_LATEST=true IMAGE_REPO=us-docker.pkg.dev/akuity/akp/akuity-platform TAG=latest make image
 6019  git status
 6020  git branch
 6021  git branch -D enable-k3s-proxy
 6022  git checkout -b enable-k3s-proxy
 6023  git push --force
 6024  git branch 
 6025  cd mode
 6026  cd akuity-platform/models
 6027  make generate
 6028  git status
 6029  git diff sql
 6030  git add liquibase sql
 6031  git status
 6032  git commit -m "update db" -s
 6033  git push
 6034  ls
 6035  ak
 6036  cd akuity-platform
 6037  ls
 6038  make generate
 6039  git status
 6040  git add liquibase sql
 6041  git status
 6042  git commit -m "update db" -s
 6043  git push 
 6044  k get po -A
 6045  cd ../kubevision-scripts
 6046  sh refresh-akp.sh
 6047  k get po -A
 6048  k9s
 6049  cd ..
 6050  cd akuity-platform
 6051  git checkout main
 6052  git status
 6053  git checkout *
 6054  git status
 6055  git checkout  ../internal/portalapi/organization/get_kubernetes_summary_v1.go
 6056  git status
 6057  cd ..
 6058  git status
 6059  git checkout main
 6060  git status
 6061  git checkout internal
 6062  git status
 6063  git checkout main
 6064  cd ../kubevision-scripts
 6065  sh refresh-akp.sh
 6066  k get po -A
 6067  k9s
 6068  cd ../akuity-platform
 6069  git status
 6070  git checkout internal/services/k8sresource/summary.sql
 6071  ggit status
 6072  git status
 6073  git checkout  internal/services/k8sresource/summary.sql
 6074  k get po -A
 6075  kx
 6076  k get po -A
 6077  k get po -A |grep post
 6078  cd ~
 6079  sh setporxy.sh
 6080  history |grep disk
 6081  diskutil list
 6082  sudo diskutil eraseDisk ExFAT MyDisk /dev/disk4
 6083  ak
 6084  cd ../SlackUp
 6085  ls
 6086  cat config.yaml
 6087  ls
 6088  vim config.yaml
 6089  ./SlackUp
 6090  history |grep psql
 6091  cd Desktop
 6092  ls
 6093  sh bench.sh
 6094  export PGPASSWORD='1hGg6bB2IYYU2T0i4KIKkrcVJ5Ky4hiOhC3CEC7'
 6095  sh bench.sh
 6096  sh bench.sh\
 6097  sh bench.sh
 6098  ls
 6099  sh bench.sh
 6100  git status
 6101  git checkout internal/portalapi
 6102  git status
 6103  sh bench.sh
 6104  vim bench.sh
 6105  sh bench.sh
 6106  git status
 6107  git diff internal
 6108  git status
 6109  git add internal
 6110  git status
 6111  git commit -m "optimize k8s summary sql query" -s
 6112  git pull --rebase
 6113  git log
 6114  git checkout -b k8s-summary
 6115  git push 
 6116  \n    git push --set-upstream origin k8s-summary\n
 6117  git status
 6118  git add internal
 6119  git status
 6120  git commit -m "remote calculte time limit" -s
 6121  git push
 6122  cd ../kubevision-scripts
 6123  ls
 6124  sh refresh-akp.sh
 6125  k9s
 6126  cd ~
 6127  ls
 6128  cd Desktop
 6129  ls
 6130  cat bench.sh
 6131  env
 6132  telnet localhost 5432
 6133  k get po -n akuity-platform
 6134  k logs -n akuity-platform postgres-7ccb65b454-whpdr
 6135  k get po -n akuity-platform
 6136  k delete po -n akuity-platform postgres-7ccb65b454-whpdr
 6137  k get po -n akuity-platform
 6138  k logs -n akuity-platform
 6139  k logs -n akuity-platform postgres-7ccb65b454-m2gxb
 6140  telnet localhost 5432
 6141  k get deploy -n akuity-platform
 6142  k get deploy -n akuity-platform postgres -oyaml
 6143  k get svc -n akuity-platform
 6144  ping ***************
 6145  clear
 6146  ak
 6147  cd ak
 6148  cd akuity-platform
 6149  ls
 6150  cd ../kubevision-scripts
 6151  ls
 6152  ENABLE_KARGO=true ./start-akp.sh
 6153  k get po -A
 6154  k get po -n akuity-platform
 6155  k describe po -n akuity-platform portaldb-update-79lws
 6156  k get po -n akuity-platform
 6157  k describe po -n akuity-platform portaldb-update-79lws
 6158  k get po -n akuity-platform
 6159  k describe po -n akuity-platform portaldb-update-79lws
 6160  docker pull quay.io/akuity/liquibase:4.32
 6161  k get po -A
 6162  cd ~/Desktop
 6163  ls
 6164  vim bench.sh
 6165  sh bench.sh
 6166  export PGPASSWORD='1hGg6bB2IYYU2T0i4KIKkrcVJ5Ky4hiOhC3CEC7'
 6167  sh bench.sh
 6168  kx
 6169  k top po -n akuity-platform
 6170  k top po -n akuity-platform -w
 6171  k top po -n akuity-platform 
 6172  k get po -n akuity-platform platform-controller-556ff6f589-n7hsv -oyaml
 6173  k top po -n akuity-platform 
 6174  kx
 6175  k get po -n akuity-platform
 6176  k top po -n akuity-platform
 6177  k get po -n akuity-platform platform-controller-556ff6f589-lqnd8 -oyaml
 6178  k top po -n akuity-platform
 6179  k logs -n akuity-platform platform-controller-556ff6f589-lqnd8
 6180  k logs -n akuity-platform platform-controller-556ff6f589-lqnd8 |grep error
 6181  k top po -n akuity-platform
 6182  k describe po -n akuity-platform platform-controller-556ff6f589-lqnd8
 6183  k top po -n akuity-platform
 6184  k get nodes 
 6185  k get po -n akuity-platform -owide
 6186  k describe nodes ip-10-4-97-73.us-east-2.compute.internal
 6187  k get po -n akuity-platform
 6188  k top po -n akuity-platform
 6189  k top po -n akuity-platform -owide
 6190  k get po -n akuity-platform -owide
 6191  k describe node ip-10-4-97-73.us-east-2.compute.internal
 6192  k top nodes
 6193  k top nodes -w
 6194  k top nodes --help
 6195  k top nodes --sort-by='cup'
 6196  k top nodes --sort-by=cup
 6197  k top nodes --sort-by='cpu'
 6198  k top pod -A --sort-by='cpu'
 6199  k top pod -A --sort-by='memory'
 6200  ping ************
 6201  ssh ming@************
 6202  ssh ming@************
 6203  telnet ************
 6204  telnet ************
 6205  telnet ************
 6206  ping ************
 6207  ping ************
 6208  ssh ming@************
 6209  vim ~/.ssh/known_hosts
 6210  ssh ming@************
 6211  ls
 6212  cd Downloads
 6213  ls
 6214  cd Desktop
 6215  ls
 6216  cd Desktop\ -\ Ming的MacBook\ Pro\ \(2\)
 6217  ls
 6218  cat b.yaml 
 6219  ls
 6220  scp mixed.yaml ming@************:~
 6221  ssh ming@************
 6222  ls
 6223  ssh ming@************
 6224  ls
 6225  cat a.log
 6226  clear
 6227  ssh admin@************
 6228  clear
 6229  ak
 6230  cd ../SlackUp
 6231  ls
 6232  ./SlackUp
 6233  cd ~
 6234  ls
 6235  ssh admin@************
 6236  ping ************
 6237  ssh ming@************
 6238  ssh ming@************
 6239  cd akuity-platform
 6240  git status
 6241  git checkout main
 6242  git stauts
 6243  git log
 6244  git reset HEAD~1 --hard
 6245  git pull
 6246  git status
 6247  git pull
 6248  git checkout -b k8s-summary-fix
 6249  clear
 6250  git status
 6251  git add internal
 6252  git status
 6253  git commit -m "fix: cve_count converting NULL to uint32 is unsupported" -s
 6254  git push
 6255  git push --set-upstream origin k8s-summary-fix
 6256  git status
 6257  git add internal
 6258  git status
 6259  git commit -m "update cve count" -s
 6260  git pull --rebase
 6261  git push 
 6262  ssh ming@************
 6263  iptables -t mangle -L PREROUTING -v --line-numbers\n
 6264  ssh ming@************
 6265  clear
 6266  ls
 6267  ssh admin@************
 6268  ak
 6269  cd akuity-platform
 6270  ls
 6271  git checkout main
 6272  git pull
 6273  git status
 6274  git log
 6275  cd ../kubevision-scripts
 6276  ls
 6277  sh refresh-akp.sh
 6278  git pull
 6279  clear
 6280  ls
 6281  cd manifests
 6282  ls
 6283  vim argocd.yaml
 6284  vim kargo.yaml
 6285  ls
 6286  vim argocd_resources.yaml
 6287  cat kargo.yaml
 6288  ls
 6289  grep version *.yaml
 6290  ENABLE_KARGO=true ./start-akp.sh
 6291  cd ..
 6292  ENABLE_KARGO=true ./start-akp.sh
 6293  ls
 6294  sh stop-akp.sh
 6295  kx
 6296  k get po -A
 6297  ls
 6298  ENABLE_KARGO=true ./start-akp.sh
 6299  k get po -A
 6300  k get po -A -w
 6301  k get po -A
 6302  k describe po -n kube-system coredns-56f6fc8fd7-kjqh2
 6303  docker pull rancher/mirrored-coredns-coredns:1.11.3
 6304  k get po -A
 6305  k get po -A -w
 6306  k get po -A 
 6307  k describe po -n akuity akuity-agent-68b7cb5b56-56hdz
 6308  docker pull quay.io/akuity/agent:0.5.60
 6309  k get po -A
 6310  k describe po -n akuity akuity-agent-68b7cb5b56-56hdz
 6311  docker pull "quay.io/akuity/agent:0.5.60
 6312  docker pull quay.io/akuity/agent:0.5.60
 6313  k get po -A
 6314  k describe po -n akuity argocd-application-controller-8558856f45-l2mqh
 6315  k get po -A
 6316  k describe po -n akuity argocd-notifications-controller-765b9f9f9b-wh47d
 6317  k get po -A
 6318  k delete po -A --all
 6319  k get po -A
 6320  k describe po -n akuity argocd-repo-server-b9956bc4-jmwcq
 6321  k get po -A
 6322  k describe po -n akuity akuity-agent-68b7cb5b56-58vx6
 6323  ls
 6324  sh stop-akp.sh
 6325  kx
 6326  sh stop-akp.sh
 6327  kx
 6328  k get po -A
 6329  history |grep context|grep delete
 6330  kx
 6331  ENABLE_KARGO=true ./start-akp.sh
 6332  k9s
 6333  kx
 6334  k get po -A
 6335  ENABLE_KARGO=true ./start-akp.sh
 6336  cd akuity-platform
 6337  make generate-in-container
 6338  git status
 6339  git diff  internal/services/accesscontrol/types_policy.go
 6340  make generate-in-container
 6341  clear
 6342  make generate-in-container
 6343  git status
 6344  git checkout -b treeview-add-delete
 6345  git status
 6346  git add aims api docs internal pkg portal 
 6347  git status
 6348  git commit -m "add treeview resource delete operation" -s
 6349  git checkout main
 6350  git status
 6351  make generate-in-container
 6352  cd portal/ui
 6353  pnpm install .
 6354  pnpm -i 
 6355  pnpm -i .
 6356  pnpm install .
 6357  cd ..
 6358  make generate-in-container
 6359  git branch
 6360  git checkout treeview-add-delete
 6361  make generate-in-container
 6362  git status
 6363  git add docs internal portal
 6364  git status
 6365  git commit --amend
 6366  git status
 6367  git diff
 6368  make generate-in-container
 6369  git status
 6370  git add aims api docs internal pkg portal
 6371  git status
 6372  git commit --amend
 6373  git status
 6374  git push
 6375  cd ../kubevision-scripts
 6376  sh refresh-akp.sh
 6377  make generate-in-container
 6378  cd ..
 6379  cd akuity-platform
 6380  make generate-in-container
 6381  git status
 6382  make format
 6383  git status
 6384  git add pkg api docs
 6385  git status
 6386  git commmit -m "change rpc name" -s
 6387  git commit -m "change rpc name" -s
 6388  git push
 6389  cd ../kubevision-scripts
 6390  sh refresh-akp.sh
 6391  ls
 6392  vim portal/ui/package.json
 6393  cd ..
 6394  cd akuity-platform
 6395  vim portal/ui/package.json
 6396  vim portal/ui/pnpm-lock.yaml
 6397  make generate-in-container
 6398  cd akuity-platform
 6399  git status
 6400  git add 
 6401  git diff 
 6402  git status
 6403  git add portal
 6404  git status
 6405  git diff
 6406  git status
 6407  git diff
 6408  git status
 6409  git diff
 6410  git status
 6411  git diff
 6412  git status
 6413  git diff
 6414  git status
 6415  git add internal
 6416  git status
 6417  git commit -m "change filename" -s
 6418  git push
 6419  git status
 6420  git checkout main
 6421  git pull
 6422  git status
 6423  git branch
 6424  git branch -D proxy-tmp
 6425  git branch
 6426  git branch -D enable-k3s-proxy
 6427  git branch
 6428  git branch -D k8s-summary
 6429  git branch -D k8s-summary-fix
 6430  git branch
 6431  git branch -D extension-setting
 6432  git status
 6433  git branch
 6434  git checkout treeview-add-delete
 6435  git status
 6436  make generate-in-container
 6437  git status
 6438  git add api docs internal pig
 6439  git add api docs internal pkg
 6440  git status
 6441  git commit --amend
 6442  git status
 6443  git log
 6444  git reset --soft HEAD~1
 6445  git status
 6446  git commit -m "use resource id to query resources" -s
 6447  git status
 6448  git push 
 6449  git pull --rebabse
 6450  git pull --rebase
 6451  git log
 6452  git status
 6453  git fetch origin
 6454  git merge origin/main
 6455  git push 
 6456  cd ../kubevision-scripts
 6457  sh refresh-akp.sh
 6458  git status
 6459  clear
 6460  git status
 6461  ls
 6462  cd ..
 6463  cd akuity-platform
 6464  git status
 6465  git add internal
 6466  git status
 6467  git commit -m "add treeview resource permission" -s
 6468  git push 
 6469  cd ../kubevision-scripts
 6470  sh refresh-akp.sh
 6471  cd ..
 6472  cd akuity-platform
 6473  git status
 6474  git add internal
 6475  git status
 6476  git commit -m "change perssion for tree view" -s
 6477  git push 
 6478  git status
 6479  k get po -A
 6480  kx
 6481  k get po -A
 6482  k describe po guestbook-ui-6cb57c694d-gmwjv
 6483  docker pull gcr.io/google-samples/gb-frontend:v5
 6484  k get po -A
 6485  k delete po guestbook-ui-6cb57c694d-gmwjv
 6486  k get po -A
 6487  k describe po guestbook-ui-6cb57c694d-fccpm
 6488  k get po -A
 6489  k get deploy  guestbook-ui
 6490  k get deploy  guestbook-ui -oyaml
 6491  k get po 
 6492  k describe po guestbook-ui-6cb57c694d-fccpm
 6493  k get po 
 6494  docker pull gcr.io/google-samples/gb-frontend:v5
 6495  k get po 
 6496  k3d image load
 6497  k get po 
 6498  k get po -A
 6499  clear
 6500  ls
 6501  clear
 6502  k3d image load
 6503  k3d image -h
 6504  k3d images
 6505  cd models
 6506  make generate
 6507  git status
 6508  clear
 6509  ls
 6510  cd ..
 6511  make generate-in-container
 6512  git status
 6513  mv akuity-platform/internal/portalapi/organization/delete_treeview_resource_v1.go akuity-platform/internal/portalapi/organization/delete_kubernates_resource_v1.go
 6514  mv internal/portalapi/organization/delete_treeview_resource_v1.go internal/portalapi/organization/delete_kubernates_resource_v1.go
 6515  git status
 6516  git add aims api internal pkg portal
 6517  git status
 6518  git add docs
 6519  git status
 6520  git commit -m "update function name" -s
 6521  git status
 6522  cd ../kubevision-scripts
 6523  sh refresh-akp.sh
 6524  git status
 6525  git push
 6526  git pull --rebase
 6527  git push
 6528  clear
 6529  ls
 6530  sh setporxy.sh
 6531  git status
 6532  rm -rf internal/portalapi/organization/delete_treeview_resource_v1.go
 6533  git status
 6534  git add internal
 6535  git commit -m "change function name" -s
 6536  git fetch origin
 6537  cd ..
 6538  ls
 6539  cd akuity-platform
 6540  git status
 6541  git fetch origin
 6542  git status
 6543  ls
 6544  git status
 6545  git fetch origin
 6546  git push
 6547  env
 6548  unset http_proxy\nunset https_proxy\nunset HTTP_PROXY\nunset HTTPS_PROXY
 6549  git push
 6550  curl -Iv https://github.com
 6551  git push
 6552  vim /etc/resolv.conf
 6553  sudo vim /etc/resolv.conf
 6554  git push
 6555  sh setporxy.sh
 6556  git fetch origin
 6557  ak
 6558  cd ../SlackUp
 6559  ./SlackUp
 6560  clear
 6561  env
 6562  env |grep proxy
 6563  ping github.com
 6564  cd ..
 6565  cd akuity/akuity-platform
 6566  git status
 6567  git fetch origin
 6568  git merge origin/main
 6569  git log
 6570  git status
 6571  git checkout main
 6572  git status
 6573  git branch
 6574  git pull 
 6575  git log
 6576  tiger
 6577  exit
 6578  clear
 6579  k get po -A
 6580  cd ..
 6581  cd akuity-platform
 6582  ls
 6583  cd portal/ui
 6584  PORTAL_SERVER_URL=https://portal-server.akuity-platform pnpm run dev
 6585  k get po -n akuity-platform
 6586  px
 6587  kx
 6588  k get po -A
 6589  k get secrets -n akuity-platform
 6590  k get secrets -n akuity-platform akuity-platform -oyaml
 6591  k logs -n akuity-platform portal-server-689566f7f5-knqnd
 6592  k logs -n akuity-platform portal-server-689566f7f5-knqnd |grep errror
 6593  k logs -n akuity-platform portal-server-689566f7f5-knqnd |grep error
 6594  k logs -n akuity-platform portal-server-689566f7f5-knqnd -f |grep error
 6595  k9s
 6596  ak
 6597  cd ..Sl
 6598  cd ../SlackUp
 6599  ./SlackUp
 6600  clear
 6601  git status
 6602  cd ../kubevision-scripts
 6603  sh refresh-akp.sh
 6604  ak
 6605  cd kubevision-scripts
 6606  sh refresh-akp.sh
 6607  cd ..
 6608  cd akuity-platform
 6609  PUSH_LATEST=true IMAGE_REPO=us-docker.pkg.dev/akuity/akp/akuity-platform TAG=latest make image
 6610  ping google.com
 6611  PUSH_LATEST=true IMAGE_REPO=us-docker.pkg.dev/akuity/akp/akuity-platform TAG=latest make image
 6612  curl -v https://auth.docker.io/token
 6613  PUSH_LATEST=true IMAGE_REPO=us-docker.pkg.dev/akuity/akp/akuity-platform TAG=latest make image
 6614  ping google.com
 6615  PUSH_LATEST=true IMAGE_REPO=us-docker.pkg.dev/akuity/akp/akuity-platform TAG=latest make image
 6616  docker pull gcr.io/google-samples/gb-frontend:v5
 6617  docker pull us-docker.pkg.dev/akuity/akp/akuity-platform
 6618  PUSH_LATEST=true IMAGE_REPO=us-docker.pkg.dev/akuity/akp/akuity-platform TAG=latest make image
 6619  export HTTPS_PROXY=http://************:7890\nexport HTTP_PROXY=http://************:7890
 6620  PUSH_LATEST=true IMAGE_REPO=us-docker.pkg.dev/akuity/akp/akuity-platform TAG=latest make image
 6621  cd ..
 6622  cd kubevision-scripts
 6623  sh refresh-akp.sh
 6624  cd ..
 6625  cd akuity-platform
 6626  git pull 
 6627  git status
 6628  cd ../kubevision-scripts
 6629  sh refresh-akp.sh
 6630  cd ..
 6631  cd akuity-platform
 6632  git log
 6633  cd ../kubevision-scripts
 6634  ls
 6635  sh stop-akp.sh
 6636  kx
 6637  ls
 6638  ENABLE_KARGO=true ./start-akp.sh
 6639  unset http_proxy\nunset https_proxy\nunset HTTP_PROXY\nunset HTTPS_PROXY
 6640  ENABLE_KARGO=true ./start-akp.sh
 6641  k get po -A
 6642  ENABLE_KARGO=true ./start-akp.sh
 6643  k get po -A
 6644  k describe po -n kube-system coredns-56f6fc8fd7-n8qb9
 6645  docker pull rancher/mirrored-coredns-coredns:1.11.3
 6646  k get po -A
 6647  k describe po -n akuity argocd-application-controller-6f4b9dcdd6-sqxn2
 6648  k get po -A
 6649  k get po -A -w
 6650  ls
 6651  grep 300 *.sh
 6652  vim utils.sh
 6653  ENABLE_KARGO=true ./start-akp.sh
 6654  k get po -A -w
 6655  export HTTPS_PROXY=http://************:7890\nexport HTTP_PROXY=http://************:7890
 6656  ENABLE_KARGO=true ./start-akp.sh
 6657  k get po -A 
 6658  k delete po -n kargo-ox0og4ot7mc9ako8 kargo-api-68f677d88b-pzg9v
 6659  k get po -A 
 6660  k describe po -n kargo-ox0og4ot7mc9ako8 kargo-api-68f677d88b-k6bxv
 6661  k get po -A 
 6662  kx
 6663  k get po -A 
 6664  k get po -A -w
 6665  k get po -A 
 6666  ENABLE_KARGO=true ./start-akp.sh
 6667  ./start-akp.sh
 6668  k get po -A 
 6669  kx
 6670  k get po -A
 6671  export HTTPS_PROXY=http://************:7890\nexport HTTP_PROXY=http://************:7890
 6672  ./start-akp.sh
 6673  k get po -A
 6674  which k3s
 6675  unset http_proxy\nunset https_proxy\nunset HTTP_PROXY\nunset HTTPS_PROXY
 6676  ./start-akp.sh
 6677  export HTTPS_PROXY=http://************:7890\nexport HTTP_PROXY=http://************:7890
 6678  ./start-akp.sh
 6679  unset http_proxy\nunset https_proxy\nunset HTTP_PROXY\nunset HTTPS_PROXY
 6680  ./start-akp.sh
 6681  export HTTPS_PROXY=http://************:7890\nexport HTTP_PROXY=http://************:7890
 6682  ./start-akp.sh
 6683  k get po -A
 6684  kx
 6685  k get po -a
 6686  k get po -A
 6687  unset http_proxy\nunset https_proxy\nunset HTTP_PROXY\nunset HTTPS_PROXY
 6688  k get po -A
 6689  akuity argocd apply -f /Users/<USER>/MyPro/akuity/kubevision-scripts/manifests/argocd.yaml --insecure-skip-tls-verify --org-name test-org
 6690  ak
 6691  cd akuity-platform
 6692  ls
 6693  cd portal/ui
 6694  PORTAL_SERVER_URL=https://portal-server.akuity-platform pnpm run dev
 6695  akuity argocd apply -f /Users/<USER>/MyPro/akuity/kubevision-scripts/manifests/argocd.yaml --insecure-skip-tls-verify --org-name test-org
 6696  vim /etc/hosts
 6697  sudo vim /etc/hosts
 6698  ls
 6699  kx
 6700  k get po -A
 6701  k delete po -n akuity --all --force --grace-periods 0
 6702  k delete po -n akuity --all --force --grace-period 0
 6703  k get po -A
 6704  k describe po -n akuity argocd-application-controller-6f4b9dcdd6-l247j
 6705  docker pull quay.io/akuity/argocd:v3.0.6-ak.58
 6706  k3s images load -h
 6707  k3d image load -h
 6708  k3d image load quay.io/akuity/argocd:v3.0.6-ak.58
 6709  kx
 6710  k3d image load quay.io/akuity/argocd:v3.0.6-ak.58 -c k3d-akuity-customer
 6711  k3d cluster list
 6712  k3d image load quay.io/akuity/argocd:v3.0.6-ak.58 -c akuity-customer
 6713  k get po -A
 6714  k describe po -n akuity argocd-application-controller-6f4b9dcdd6-l247j
 6715  k get po -n akuity
 6716  k delete po -n akuity argocd-application-controller-6f4b9dcdd6-l247j
 6717  k get po -n akuity
 6718  k describe po -n akuity argocd-application-controller-6f4b9dcdd6-v26hj
 6719  k get po -n akuity
 6720  k describe po -n akuity argocd-notifications-controller-765b9f9f9b-j5r65
 6721  k get po -n akuity
 6722  k describe po -n akuity argocd-repo-server-b9956bc4-k722x
 6723  k get po -n akuity
 6724  k delete po -n akuity --all --force --grace-period 0
 6725  k get po -n akuity
 6726  k get po -n akuity -w
 6727  k logs -n akuity akuity-agent-65d6f65c5-8xfdh
 6728  k get po -n akuity -w
 6729  cd ../kubevision-scripts
 6730  ls
 6731  vim utils.sh
 6732  ls
 6733  cd manifests
 6734  akuity argocd cluster get-agent-manifests --org-name test-org --instance-name test-argocd test-cluster --insecure-skip-tls-verify | kubectl apply -f -
 6735  ls
 6736  cd ..
 6737  ./start-akp.sh
 6738  vim utils.sh
 6739  REFRESH_AGENT_SERVER=false ./start-akp.sh
 6740  vim utils.sh
 6741  vim start-akp.sh
 6742  REFRESH_AGENT_SERVER=false ./start-akp.sh
 6743  vim .env
 6744  vim start-akp.sh
 6745  ./start-akp.sh REFRESH_AGENT_SERVER=false
 6746  vim utils.sh
 6747  ./start-akp.sh REFRESH_AGENT_SERVER=false
 6748  vim utils.sh
 6749  ./start-akp.sh REFRESH_AGENT_SERVER=false
 6750  vim utils.sh
 6751  ./start-akp.sh REFRESH_AGENT_SERVER=false
 6752  k get po -A
 6753  grep "k3d cluster create" *
 6754  grep "k3d cluster create" -rwn ./
 6755  ls
 6756  grep k3d *.sh
 6757  k get po -A
 6758  kx
 6759  k get po -A
 6760  kx
 6761  k get po -A
 6762  cd ..
 6763  git clone https://github.com/akuity/akp-demo.git
 6764  cd akp-demo
 6765  ls
 6766  argocd proj create -f ./akp-demo/bootstrap/argocd/00-project.yaml
 6767  ls
 6768  cd charts
 6769  ls
 6770  cd guestbook
 6771  ls
 6772  k get po -A
 6773  kx
 6774  k get po -A
 6775  ls
 6776  akuity argocd apply -f /Users/<USER>/MyPro/akuity/kubevision-scripts/manifests/argocd.yaml --insecure-skip-tls-verify --org-name test-org
 6777  ls
 6778  cd ..
 6779  ls
 6780  cd ..
 6781  ls
 6782  cd oom-demo
 6783  ls
 6784  cat manifest.yaml
 6785  k get po -A
 6786  kx
 6787  k get po -A
 6788  k describe po -n argocd-uakgv2fiappvyyt0 argocd-server-865fbbf867-kk29c
 6789  docker pull quay.io/akuity/argocd:v3.0.6-ak.58
 6790  k3d image load quay.io/akuity/argocd:v3.0.6-ak.58 -c akuity-customer
 6791  k3d cluster list
 6792  k get po -A
 6793  kx
 6794  k get po -A
 6795  kx
 6796  k get po -A
 6797  kx
 6798  k get po -A
 6799  kx
 6800  k get po -A
 6801  k describe po -n argocd-uakgv2fiappvyyt0 k3s-f7c79f968-x4vhl
 6802  k get po -A
 6803  k describe po -n argocd-uakgv2fiappvyyt0 k3s-f7c79f968-x4vhl
 6804  k get po -A
 6805  k describe po -n argocd-uakgv2fiappvyyt0 k3s-f7c79f968-x4vhl
 6806  k get po -A
 6807  kx
 6808  k get po -A
 6809  kx
 6810  k get po -A
 6811  kx
 6812  k get po -A
 6813  TOKEN="eyJhbGciOiJSUzI1NiIsImtpZCI6Ijg0N2QzOTE0MDg3ZGQ3YTBkOTJkMDUxZDA2MjczMjQwYzUxOTU1NmMifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NPNttr7bvCSpg7lRgSMvp-YfLCEdaaZHCWrL3oef2LXqHU4bjJcsbcNfCCoQ2-vNPQjcJdpbdXG2cvcz-02gHNr_xkkMPxLj_jeHj9pfALBrhcF6gsKSl3pZu-iu4deanqoTmcVhUiwUsgmNLuO2VjZ9rnWyDjXL9FGrpX8spGrJycN5k5a_fiVMSRPZPZZrnxLuuGvsiApnUEnsqOcI83bsDKTW2vb6cHvtwhK7pm16qSNGbq-msyX-iLHQSlP7SKBUycS_hXEW4aJvtM2zu-_dWw2awLRgZWLyZoDMdiU6nrFqF1a5577hmWLt8269jwQUaDyOtL6JOoGcWum7Jg" && curl -s -H "Authorization: Bearer $TOKEN" "https://portal-server.akuity-platform/api/v1/orgs/j2dnzwplvndfk5sq/argocd/instances/uakgv2fiappvyyt0/clusters/8ulqd0xxx4krk04b/manifests" -f| kubectl apply -f -
 6814  TOKEN="eyJhbGciOiJSUzI1NiIsImtpZCI6Ijg0N2QzOTE0MDg3ZGQ3YTBkOTJkMDUxZDA2MjczMjQwYzUxOTU1NmMifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NPNttr7bvCSpg7lRgSMvp-YfLCEdaaZHCWrL3oef2LXqHU4bjJcsbcNfCCoQ2-vNPQjcJdpbdXG2cvcz-02gHNr_xkkMPxLj_jeHj9pfALBrhcF6gsKSl3pZu-iu4deanqoTmcVhUiwUsgmNLuO2VjZ9rnWyDjXL9FGrpX8spGrJycN5k5a_fiVMSRPZPZZrnxLuuGvsiApnUEnsqOcI83bsDKTW2vb6cHvtwhK7pm16qSNGbq-msyX-iLHQSlP7SKBUycS_hXEW4aJvtM2zu-_dWw2awLRgZWLyZoDMdiU6nrFqF1a5577hmWLt8269jwQUaDyOtL6JOoGcWum7Jg" && curl -s -H "Authorization: Bearer $TOKEN" "https://portal-server.akuity-platform/api/v1/orgs/j2dnzwplvndfk5sq/argocd/instances/uakgv2fiappvyyt0/clusters/8ulqd0xxx4krk04b/manifests" -k| kubectl apply -f -
 6815  k get po -A
 6816  ls
 6817  vim /etc/hosts
 6818  sudo vim /etc/hosts
 6819  k get po -A
 6820  ./SlackUp
 6821  cd ~
 6822  ls
 6823  sh setporxy.sh
 6824  tiger
 6825  exit
 6826  ls
 6827  cd Desktop
 6828  ls
 6829  cd Desktop\ -\ ming的MacBook\ Pro
 6830  ls
 6831  cd Desktop
 6832  ls
 6833  cd Desktop\ -\ ming的MacBook\ Pro
 6834  ls
 6835  sort -f 1.txt | uniq -i\n
 6836  sort -f 1.txt | uniq -i |shuf\n
 6837  sort -u 1.txt | shuf
 6838  python3 -c "import random; lines = list(dict.fromkeys(open('1.txt').readlines())); random.shuffle(lines); print(''.join(lines))"\n
 6839  python3 -c "import random; lines = list(dict.fromkeys(open('1.txt').readlines())); random.shuffle(lines); print(''.join(lines))" > a.txt\n
 6840  vim a.txt
 6841  cd Desktop
 6842  ls
 6843  cd PTE
 6844  ls
 6845  cd 25PTE学习资料
 6846  ls
 6847  cd Volcabulary
 6848  ls
 6849  vim ra.txt
 6850  cat ra.txt
 6851  sed 's/^[0-9]\+\.//' ra.txt > cleaned.txt\n
 6852  ls
 6853  cat cleaned.txt
 6854  sed 's/^[0-9]\+\.//' ra.txt
 6855  sed -E 's/^[0-9]+\.//' ra.txt
 6856  sed -E 's/^[0-9]+\.//' ra.txt > cleaned.txt
 6857  cat cleaned.txt|less
 6858  rm -rf ra.txt
 6859  mv cleaned.txt ra.txt
 6860  ls
 6861  ra-wrong-pronounce.txt
 6862  touch ra-wrong-pronounce.txt
 6863  vim ra-wong-pronuance.txt
 6864  mv ra-wong-pronuance.txt ra-wrong-pronounce.txt
 6865  sh setporxy.sh
 6866  ak
 6867  cd ../SlackUp
 6868  cd ..
 6869  cd akuity
 6870  cd akuity-platform
 6871  cd ../kubevision-scripts
 6872  sh refresh-akp.sh
 6873  export HTTPS_PROXY=http://************:7890\nexport HTTP_PROXY=http://************:7890
 6874  sh refresh-akp.sh
 6875  ak
 6876  cd akuity-platform
 6877  cd ../kubevision-scripts
 6878  sh refresh-akp.sh
 6879  export HTTPS_PROXY=http://************:7890\nexport HTTP_PROXY=http://************:7890
 6880  sh refresh-akp.sh
 6881  cd akuity-platform
 6882  git status
 6883  git branch
 6884  git checkout -b runbook-generate
 6885  git status
 6886  git add internal
 6887  git status
 6888  git commit -m "generate runbook through AI conversation" -s
 6889  git checkout main
 6890  git status
 6891  sh refresh-akp.sh
 6892  kx
 6893  k get po -A
 6894  k delete po -n akuity akuity-agent-65d6f65c5-8xfdh akuity-agent-65d6f65c5-btp4f
 6895  k get po -A
 6896  kx
 6897  k get po -A
 6898  cd ak
 6899  ak
 6900  cd kubevision-scripts
 6901  sh refresh-akp.sh
 6902  unset http_proxy\nunset https_proxy\nunset HTTP_PROXY\nunset HTTPS_PROXY
 6903  sh refresh-akp.sh
 6904  git status
 6905  git branch
 6906  git checkout runbook-generate
 6907  git status
 6908  sh refresh-akp.sh
 6909  clear
 6910  ls
 6911  sh refresh-akp.sh
 6912  clear
 6913  ls
 6914  sh refresh-akp.sh
 6915  ls
 6916  ak
 6917  ls
 6918  cd akuity-platform
 6919  cd ../kubevision-scripts
 6920  ls
 6921  sh refresh-akp.sh
 6922  clear
 6923  k get po -A
 6924  kx
 6925  k get po -A
 6926  k logs -n akuity akuity-agent-65d6f65c5-9kfx4
 6927  k get po -A
 6928  ls
 6929  sh stop-akp.sh
 6930  git status
 6931  git diff manifests
 6932  git diff utils.sh
 6933  git checkout utils.sh
 6934  git status
 6935  cd ..
 6936  cd akuity-platform
 6937  cd ..
 6938  cd kubevision-scripts
 6939  ls
 6940  ENABLE_KARGO=false ./start-akp.sh
 6941  kx
 6942  k get po -A
 6943  ENABLE_KARGO=false ./start-akp.sh
 6944  kx
 6945  k get po -A
 6946  kx
 6947  k get po -A
 6948  kx
 6949  k get po -A
 6950  k delete ns akuity --force --grace-period 0
 6951  k get po -A
 6952  kx
 6953  k get po -A
 6954  kx
 6955  TOKEN="eyJhbGciOiJSUzI1NiIsImtpZCI6IjJjYzNlYzYwMGUzZTBiYzRlODNmNjQ2YjVhZTZlYjU3ZDVkN2EyNzUifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.EsnlnZpqOIL1mdzizS18nV7tUmKzipSQzsE0Macsy4XtwhgwZKCeByRahf18I-CihxqrTwDnklenO9HOYibPQ67m93v6jEWH1b1wj8Wxiawbzt81bzhShLGUkoE3vfLjPdHoNlUkpdodziIMf4hLwmSFfw1OjA8ZhO8kYwCIjaUM8i5vIW03HsPrNlImWSsdXyD-4GwcNFfzelUmO8SMRIeemkEmB0j7PCCQ3D-rPSpEJR0cQajjsvfoIgaTWDlqB39CudhQxa3Vs5FdmPCJdChkmIhz-H7MBG5AaHsxrM_thL8ru58r47vO4WELWLd5nAdoKuG6EvPyNKjtpff9cQ" && curl -s -H "Authorization: Bearer $TOKEN" "https://portal-server.akuity-platform/api/v1/orgs/6xxe21nbb65nl2xj/argocd/instances/eliij4q5cpasjs6g/clusters/288uy8hmcfz3fjhb/manifests" -k | kubectl apply -f -
 6956  k get po -A
 6957  vim /etc/hosts
 6958  sudo vim /etc/hosts
 6959  clear
 6960  ls
 6961  k get po -A
 6962  sh refresh-akp.sh
 6963  cd ../kubevision-scripts
 6964  sh refresh-akp.sh
 6965  k get po -A
 6966  sh refresh-akp.sh
 6967  k get po -A
 6968  sh refresh-akp.sh
 6969  k9s
 6970  sh refresh-akp.sh
 6971  k get po -n akuity-platform
 6972  k logs -n akuity-platform runbook 
 6973  k get po -n akuity-platform
 6974  k logs -n akuity-platform platform-controller-c75987d68-dr9fj
 6975  k logs -n akuity-platform platform-controller-c75987d68-dr9fj|grep runbook
 6976  k get po -n akuity-platform
 6977  k logs -n akuity-platform portal-server-7b6b57c9fd-k6fbl
 6978  k logs -n akuity-platform portal-server-7b6b57c9fd-k6fbl|grep runbook
 6979  k logs -n akuity-platform portal-server-7b6b57c9fd-k6fbl
 6980  k get po -n akuity-platform
 6981  k logs -n akuity-platform platform-controller-c75987d68-dr9fj
 6982  k logs -n akuity-platform platform-controller-c75987d68-dr9fj |grep runbook
 6983  k logs -n akuity-platform platform-controller-c75987d68-dr9fj
 6984  k logs -n akuity-platform platform-controller-c75987d68-dr9fj -f
 6985  clear
 6986  ls
 6987  sh refresh-akp.sh
 6988  k get po -n akuity-platform \
 6989  k get po -n akuity-platform 
 6990  k logs -n akuity-platform platform-controller-54c44d68f5-nvjr2|grep context
 6991  k logs -n akuity-platform platform-controller-54c44d68f5-nvjr2|grep "Conversation context"
 6992  sh refresh-akp.sh
 6993  cd ..
 6994  cd akuity-platform
 6995  git status
 6996  rm -rf        internal/services/ai/functions/oom.md
 6997  git status
 6998  git add internal
 6999  git status
 7000  git log
 7001  git commit --amend
 7002  git push
 7003  cd ../kubevision-scripts
 7004  sh refresh-akp.sh
 7005  cd ../akuity-platform
 7006  git status
 7007  cd ../kubevision-scripts
 7008  sh refresh-akp.sh
 7009  git status
 7010  cd ../
 7011  ls
 7012  cd akuity-platform
 7013  git status
 7014  git diff
 7015  git status
 7016  git add internal
 7017  git status
 7018  git commit -m "modify the solution section" -s
 7019  git push
 7020  sh refresh-akp.sh
 7021  cd ../kubevision-scripts
 7022  sh refresh-akp.sh
 7023  clear
 7024  git status
 7025  cd ../akuity-platform
 7026  git status
 7027  git add internal
 7028  git status
 7029  git commit -m "update generateRunbook function" -s
 7030  git push 
 7031  git log
 7032  git pull --rebase
 7033  git merge origin/main
 7034  git push
 7035  cd ../kubevision-scripts
 7036  sh refresh-akp.sh
 7037  sh setporxy.sh
 7038  sh refresh-akp.sh
 7039  unset http_proxy\nunset https_proxy\nunset HTTP_PROXY\nunset HTTPS_PROXY
 7040  sh refresh-akp.sh
 7041  ak
 7042  cd kubevision-scripts
 7043  unset http_proxy\nunset https_proxy\nunset HTTP_PROXY\nunset HTTPS_PROXY
 7044  sh refresh-akp.sh
 7045  k9s
 7046  clear
 7047  sh refresh-akp.sh
 7048  cd ..
 7049  ls
 7050  cd ak
 7051  cd akuity-platform
 7052  git status
 7053  git add internal
 7054  git status
 7055  git reset --soft HEAD~1
 7056  git status
 7057  git commit -m "move runbook into incident" -s
 7058  git reset --soft HEAD~1
 7059  git status
 7060  rm internal/services/ai/functions/generate_runbook_prompt.md internal/services/ai/functions/oom.md
 7061  git status
 7062  git add internal
 7063  git status
 7064  git log
 7065  git status
 7066  git commit -m "move runbook into incident" -s
 7067  git status
 7068  git log
 7069  git fetch origin
 7070  git merge origin/main
 7071  git log
 7072  git push
 7073  git pull --rebase
 7074  git log
 7075  git status
 7076  git add internal
 7077  git status
 7078  git rebase --continue
 7079  git status
 7080  git log
 7081  git push
 7082  git log
 7083  git reset -i b286d0d5d9b982194b9320cba47ef546bcc20235 --hard
 7084  git reset b286d0d5d9b982194b9320cba47ef546bcc20235 --hard
 7085  git log
 7086  git status
 7087  rm -rf internal/services/ai/functions/generate_runbook.go
 7088  git status
 7089  git add internal
 7090  git status
 7091  git log
 7092  git commit --amend
 7093  git push --force
 7094  git pull 
 7095  cd ../kubevision-scripts
 7096  sh refresh-akp.sh
 7097  git status
 7098  cd ../akuity-platform
 7099  git status
 7100  sh refresh-akp.sh
 7101  git status
 7102  cd /Users/<USER>/MyPro/akuity/akuity-platform && go build ./internal/services/ai/functions
 7103  sh refresh-akp.sh
 7104  git diff  internal/services/ai/functions/search.go\n
 7105  clear
 7106  cd /Users/<USER>/MyPro/akuity/akuity-platform && go build ./internal/services/ai/functions
 7107  sh refresh-akp.sh
 7108  k9s
 7109  cd /Users/<USER>/MyPro/akuity/akuity-platform && go build ./internal/services/ai/functions
 7110  ak
 7111  ls
 7112  cd  kubevision-scripts
 7113  sh refresh-akp.sh
 7114  clear
 7115  k9s
 7116  git status
 7117  clear
 7118  ls
 7119  git status
 7120  git diff  internal/services/ai/functions/incident.go
 7121  git status
 7122  git add internal
 7123  git status
 7124  git commit -m "remove ParamsSchema" -s
 7125  git status
 7126  git push 
 7127  git status
 7128  git diff  internal/services/ai/functions/prompt.md
 7129  git checkout  internal/services/ai/functions/prompt.md
 7130  clear
 7131  ls
 7132  cd /Users/<USER>/MyPro/akuity/akuity-platform && go build ./internal/services/ai/functions
 7133  git status
 7134  sh refresh-akp.sh
 7135  git checkout internal/services/ai/functions/controller.go
 7136  sh refresh-akp.sh
 7137  git status
 7138  git add internal
 7139  git status
 7140  git commit -m "change to just use prompt instead of function tool" -s
 7141  sh refresh-akp.sh
 7142  git stauts
 7143  git status
 7144  git push 
 7145  git status
 7146  git add internal
 7147  git status
 7148  git commit -m "adjust generate runbook prompt" -s
 7149  git push
 7150  sh refresh-akp.sh
 7151  git status
 7152  git add internal
 7153  git status 
 7154  git commit -m "update prompt" -s
 7155  git push
 7156  clear
 7157  ls
 7158  cat h.log
 7159  ls
 7160  rm h.log
 7161  ls
 7162  cat portal-log.sh
 7163  sh portal-log.sh
 7164  clear
 7165  ls
 7166  cd ..
 7167  ls
 7168  cat portal-server-log.sh
 7169  sh portal-server-log.sh
 7170  clear
 7171  ls
 7172  sh setporxy.sh
 7173  ak
 7174  cd ../SlackUp
 7175  ls
 7176  ./SlackUp
 7177  tiger
 7178  git checkout main
 7179  git branch
 7180  git branch -D runbook-generate
 7181  git pull
 7182  git checkout -b web-search
 7183  cd /Users/<USER>/MyPro/akuity/akuity-platform && go test -v internal/utils/ai/anthropic.go
 7184  cd /Users/<USER>/MyPro/akuity/akuity-platform && go build ./...
 7185  tiger
 7186  git pull
 7187  tiger
 7188  ping ************
 7189  ls
 7190  vim test.sh
 7191  sh test.sh
 7192  cat test.sh
 7193  ping api.anthropic.com
 7194  curl https://api.anthropic.com/v1/messages \\n    --header "x-api-key: ************************************************************************************************************" \\n    --header "anthropic-version: 2023-06-01" \\n    --header "content-type: application/json" \\n    --data '{\n        "model": "claude-opus-4-20250514",\n        "max_tokens": 1024,\n        "messages": [\n            {\n                "role": "user",\n                "content": "How do I update a web app to TypeScript 5.5?"\n            }\n        ],\n        "tools": [{\n            "type": "web_search_20250305",\n            "name": "web_search",\n            "max_uses": 5\n        }]\n    }'
 7195  curl https://api.anthropic.com/v1/messages \\n    --header "x-api-key: sk-ant-api03-3MfZyBvBhHOJHegFqQviB_L15gwSoHRGUO3DBVGt8Xyurmo9SO4UKjmFJxc9zc1qkVS6brrV5u70OLbC27Pug-f6GBlQAA" \\n    --header "anthropic-version: 2023-06-01" \\n    --header "content-type: application/json" \\n    --data '{\n        "model": "claude-opus-4-20250514",\n        "max_tokens": 1024,\n        "messages": [\n            {\n                "role": "user",\n                "content": "How do I update a web app to TypeScript 5.5?"\n            }\n        ],\n        "tools": [{\n            "type": "web_search_20250305",\n            "name": "web_search",\n            "max_uses": 5\n        }]\n    }'
 7196  curl https://api.anthropic.com/v1/messages \\n    --header "x-api-key: ************************************************************************************************************" \\n    --header "anthropic-version: 2023-06-01" \\n    --header "content-type: application/json" \\n    --data '{\n        "model": "claude-opus-4-20250514",\n        "max_tokens": 1024,\n        "messages": [\n            {\n                "role": "user",\n                "content": "How do I update a web app to TypeScript 5.5?"\n            }\n        ],\n        "tools": [{\n            "type": "web_search_20250305",\n            "name": "web_search",\n            "max_uses": 5\n        }]\n    }'
 7197  ls
 7198  vim test.sh
 7199  rm -rf test.sh
 7200  vim claude.sh
 7201  cat claude.sh
 7202  ANTHROPIC_API_KEY=************************************************************************************************************
 7203  sh claude.sh
 7204  ping google.com
 7205  ls
 7206  sh setporxy.sh
 7207  ping google.com
 7208  ANTHROPIC_API_KEY=************************************************************************************************************
 7209  sh claude.sh
 7210  cat claude.sh
 7211  curl https://api.anthropic.com/v1/messages \\n    --header "x-api-key: $ANTHROPIC_API_KEY" \\n    --header "anthropic-version: 2023-06-01" \\n    --header "content-type: application/json" \\n    --data '{\n        "model": "claude-opus-4-20250514",\n        "max_tokens": 1024,\n        "messages": [\n            {\n                "role": "user",\n                "content": "How do I update a web app to TypeScript 5.5?"\n            }\n        ],\n        "tools": [{\n            "type": "web_search_20250305",\n            "name": "web_search",\n            "max_uses": 5\n        }]\n    }'
 7212  curl https://api.anthropic.com/v1/message \\n    --header "x-api-key: $ANTHROPIC_API_KEY" \\n    --header "anthropic-version: 2023-06-01" \\n    --header "content-type: application/json" \\n    --data '{\n        "model": "claude-opus-4-20250514",\n        "max_tokens": 1024,\n        "messages": [\n            {\n                "role": "user",\n                "content": "How do I update a web app to TypeScript 5.5?"\n            }\n        ],\n        "tools": [{\n            "type": "web_search_20250305",\n            "name": "web_search",\n            "max_uses": 5\n        }]\n    }'
 7213  curl https://api.anthropic.com/v1/messages \\n    --header "x-api-key: $ANTHROPIC_API_KEY" \\n    --header "anthropic-version: 2023-06-01" \\n    --header "content-type: application/json" \\n    --data '{\n        "model": "claude-opus-4-20250514",\n        "max_tokens": 1024,\n        "messages": [\n            {\n                "role": "user",\n                "content": "How do I update a web app to TypeScript 5.5?"\n            }\n        ],\n        "tools": [{\n            "type": "web_search_20250305",\n            "name": "web_search",\n            "max_uses": 5\n        }]\n    }'
 7214  curl https://api.anthropic.com/v1/messages \\n    --header "x-api-key: $ANTHROPIC_API_KY" \\n    --header "anthropic-version: 2023-06-01" \\n    --header "content-type: application/json" \\n    --data '{\n        "model": "claude-opus-4-20250514",\n        "max_tokens": 1024,\n        "messages": [\n            {\n                "role": "user",\n                "content": "How do I update a web app to TypeScript 5.5?"\n            }\n        ],\n        "tools": [{\n            "type": "web_search_20250305",\n            "name": "web_search",\n            "max_uses": 5\n        }]\n    }'
 7215  git status
 7216  git diff internal/utils/ai/anthropic.go
 7217  ping google.com
 7218  tiger
 7219  ping ************
 7220  git diff  internal/utils/ai/types.go
 7221  clear
 7222  ls
 7223  vim claude.sh
 7224  cat claude.sh
 7225  ANTHROPIC_API_KEY=************************************************************************************************************
 7226  vim claude.sh
 7227  sh claude.sh
 7228  vim claude.sh
 7229  sh claude.sh
 7230  cat claude.sh
 7231  ANTHROPIC_API_KEY=************************************************************************************************************\ncurl https://api.anthropic.com/v1/messages \\n    --header "x-api-key: $ANTHROPIC_API_KEY" \\n    --header "anthropic-version: 2023-06-01" \\n    --header "content-type: application/json" \\n    --data '{\n        "model": "claude-opus-4-20250514",\n        "max_tokens": 1024,\n        "messages": [\n            {\n                "role": "user",\n                "content": "How do I update a web app to TypeScript 5.5?"\n            }\n        ],\n        "tools": [{\n            "type": "web_search_20250305",\n            "name": "web_search",\n            "max_uses": 5\n        }]\n    }'
 7232  ANTHROPIC_API_KEY=************************************************************************************************************
 7233  echo $ANTHROPIC_API_KEY
 7234  curl https://api.anthropic.com/v1/messages \\n    --header "x-api-key: $ANTHROPIC_API_KEY" \\n    --header "anthropic-version: 2023-06-01" \\n    --header "content-type: application/json" \\n    --data '{\n        "model": "claude-opus-4-20250514",\n        "max_tokens": 1024,\n        "messages": [\n            {\n                "role": "user",\n                "content": "How do I update a web app to TypeScript 5.5?"\n            }\n        ],\n        "tools": [{\n            "type": "web_search_20250305",\n            "name": "web_search",\n            "max_uses": 5\n        }]\n    }'
 7235  clearf
 7236  clear
 7237  cd akuity-platform
 7238  ls
 7239  git status
 7240  git diff internal/services/ai/functions/search.go
 7241  PUSH_LATEST=true IMAGE_REPO=us-docker.pkg.dev/akuity/akp/akuity-platform TAG=latest make image
 7242  cd ../kubevision-scripts
 7243  sh refresh-akp.sh
 7244  k get secrets -A
 7245  kx
 7246  k get secrets -A
 7247  k get secrets -n akuity-platform
 7248  k get secrets -n akuity-platform akuity-platform -oyaml
 7249  k get po -A
 7250  k edit secrets -n akuity-platform akuity-platform
 7251  echo ************************************************************************************************************ |base64
 7252  k edit secrets -n akuity-platform akuity-platform
 7253  k9s
 7254  k get po -n akuity-platform
 7255  k logs -n akuity-platform platform-controller-64896495d8-zgxgc
 7256  k logs -n akuity-platform platform-controller-64896495d8-zgxgc|grep error
 7257  k logs -n akuity-platform platform-controller-64896495d8-zgxgc > a.log
 7258  vim a.log
 7259  grep Calling a.log
 7260  k get po -n akuity-platform
 7261  k logs -n akuity-platform portal-server-6b47ff987d-7tbwf|grep Calling
 7262  k logs -n akuity-platform portal-server-6b47ff987d-7tbwf
 7263  clear
 7264  sh refresh-akp.sh
 7265  cd ..
 7266  ls
 7267  cd akuity-platform
 7268  ls
 7269  git status
 7270  git add internal
 7271  git status
 7272  git log
 7273  git commit -m "add claude model support for document search" -s
 7274  git checkout main
 7275  git status
 7276  cd ../
 7277  cd kubevision-scripts
 7278  sh refresh-akp.sh
 7279  k get secrets -n akuity-platform
 7280  k get secrets -n akuity-platform akuity-platform -oyaml
 7281  k get po -n akuity-platform
 7282  k logs -n akuity-platform platform-controller-79cd6b4855-fctgq 
 7283  k get secrets -n akuity-platform
 7284  k get secrets -n akuity-platform akuity-platform -oyaml
 7285  cat claude.sh
 7286  ANTHROPIC_API_KEY=************************************************************************************************************\ncurl https://api.anthropic.com/v1/messages \\n    --header "x-api-key: $ANTHROPIC_API_KEY" \\n    --header "anthropic-version: 2023-06-01" \\n    --header "content-type: application/json" \\n    --data '{\n        "model": "claude-opus-4-20250514",\n        "max_tokens": 1024,\n        "messages": [\n            {\n                "role": "user",\n                "content": "How do I update a web app to TypeScript 5.5?"\n            }\n        ],\n        "tools": [{\n            "type": "web_search_20250305",\n            "name": "web_search",\n            "max_uses": 5\n        }]\n    }'
 7287  echo '************************************************************************************************************' |base64
 7288  echo '****************************************************************************************************************************************************" |base64 -d
 7289  echo "****************************************************************************************************************************************************" |base64 -d
 7290  k edit secrets -n akuity-platform akuity-platform
 7291  cat claude.sh
 7292  echo '************************************************************************************************************' |base64
 7293  echo '************************************************************************************************************' |base64
 7294  sh refresh-akp.sh
 7295  k9s
 7296  sh refresh-akp.sh
 7297  k9s
 7298  ak
 7299  ls
 7300  mkdir claude
 7301  cd claude
 7302  ls
 7303  vim main.go
 7304  go run main.go
 7305  go mod init anthropic
 7306  go mod tidy
 7307  vim main.go
 7308  go run main.go
 7309  clear
 7310  k edit secrets -n akuity-platform akuity-platform
 7311  echo '************************************************************************************************************************************************' |base64 -d
 7312  cd ..
 7313  cd akuity-platform
 7314  git branch
 7315  git checkout treeview-add-delete
 7316  k edit secrets -n akuity-platform akuity-platform
 7317  git checkout main
 7318  ak
 7319  cd kubevision-scripts
 7320  sh refresh-akp.sh
 7321  k edit secrets -n akuity-platform akuity-platform
 7322  git status
 7323  git checkout internal
 7324  git branch
 7325  git checkout web-search
 7326  git status
 7327  git add internal
 7328  git status 
 7329  git log
 7330  git commit --amend
 7331  git push
 7332  sh refresh-akp.sh
 7333  clear
 7334  git status
 7335  git checkout main
 7336  cd ..
 7337  ls
 7338  cd akuity-platform
 7339  git branch
 7340  git checkout treeview-add-delete
 7341  git pull
 7342  git status
 7343  git pull
 7344  git log
 7345  git reset f861400d213f31795c624f7bcfcf8d1726383f9e --hard
 7346  git pull
 7347  git log
 7348  clear
 7349  cd ../kubevision-scripts
 7350  sh refresh-akp.sh
 7351  cd ..
 7352  ls
 7353  cd akuity-platform
 7354  cd portal/ui
 7355  PORTAL_SERVER_URL=https://portal-server.akuity-platform pnpm run dev
 7356  ping eliij4q5cpasjs6g.cd.portal-server.akuity-platform
 7357  git branch
 7358  clear
 7359  git branch
 7360  git status
 7361  git branch
 7362  git checkout web-search
 7363  git stauts
 7364  clear
 7365  ls
 7366  git status
 7367  cd kubevision-scripts
 7368  sh refresh-akp.sh
 7369  k9s
 7370  sh refresh-akp.sh
 7371  k get po -n akuity-platform
 7372  k logs -n akuity-platform platform-controller-8d7f99cbb-xxvgl platform-controller
 7373  k logs -n akuity-platform platform-controller-8d7f99cbb-xxvgl platform-controller|grep web_search
 7374  sh refresh-akp.sh
 7375  k logs -n akuity-platform platform-controller-79fb466bd-xlz99 platform-controller|grep web_search
 7376  k logs -n akuity-platform platform-controller-79fb466bd-xlz99 platform-controller|grep claude-sonnet-4-20250514
 7377  sh refresh-akp.sh
 7378  k logs -n akuity-platform platform-controller-556c9d6f8-mv76t 
 7379  k logs -n akuity-platform platform-controller-556c9d6f8-mv76t  |grep web_search
 7380  sh refresh-akp.sh
 7381  k logs -n akuity-platform platform-controller-74db85c8d8-m7zzw|grep web_search
 7382  k logs -n akuity-platform platform-controller-74db85c8d8-m7zzw|grep vae
 7383  sh refresh-akp.sh
 7384  k logs -n akuity-platform platform-controller-85fd4dcc5-6zcmk|grep vae5
 7385  k logs -n akuity-platform platform-controller-85fd4dcc5-6zcmk|grep vae5 |grep -v nil
 7386  k logs -n akuity-platform platform-controller-85fd4dcc5-6zcmk|grep vae
 7387  k logs -n akuity-platform platform-controller-85fd4dcc5-6zcmk|grep -w vae
 7388  sh refresh-akp.sh
 7389  k logs -n akuity-platform platform-controller-7859476844-5mkr8|grep vae
 7390  k logs -n akuity-platform platform-controller-7859476844-5mkr8|grep "model not empty"
 7391  k logs -n akuity-platform platform-controller-7859476844-5mkr8 > al.log
 7392  vim al.log
 7393  sh refresh-akp.sh
 7394  cd ..
 7395  cd akuity-platform
 7396  ls
 7397  git status
 7398  git diff
 7399  git add internal
 7400  git status
 7401  git commit -m "add buildAnthropicMessageNewParams function" -s
 7402  git status
 7403  git push
 7404  cd ../kubevision-scripts
 7405  sh refresh-akp.sh
 7406  ls
 7407  cd akuity-platform
 7408  ls
 7409  git status
 7410  git diff
 7411  git add internal
 7412  git status
 7413  git commit -m "add some annotation" -s
 7414  git push
 7415  ls
 7416  sh setporxy.sh
 7417  ak
 7418  cd ../SlackUp
 7419  ls
 7420  ./SlackUp
 7421  git branch
 7422  git checkout main
 7423  git branch -D web-search
 7424  git pull
 7425  git fetch origin
 7426  git checkout -b feat/ai-incidents-auto-creation origin/feat/ai-incidents-auto-creation
 7427  git fetch origin
 7428  git merge origin/main
 7429  git status
 7430  clear
 7431  ls
 7432  git status
 7433  git checkout -b save-runbook
 7434  cd ..
 7435  cd kubevision-scripts
 7436  ls
 7437  sh refresh-akp.sh
 7438  unset http_proxy\nunset https_proxy\nunset HTTP_PROXY\nunset HTTPS_PROXY
 7439  sh refresh-akp.sh
 7440  export HTTPS_PROXY=http://************:7890\nexport HTTP_PROXY=http://************:7890
 7441  sh refresh-akp.sh
 7442  helm template ./charts/akuity-platform \\n  --values ./charts/akuity-platform/test/values-dev.yaml \\n  --set image.password=$DOCKER_PASSWORD \\n  --set image.repository=us-docker.pkg.dev/akuity/akp/akuity-platform \\n  --set image.tag=latest \\n  --set portal.imagePullPolicy=IfNotPresent \\n  --set platformController.imagePullPolicy=IfNotPresent \\n  --namespace akuity-platform \\n  | kubectl apply -f -
 7443  cd ..
 7444  cd akuity-platform
 7445  helm template ./charts/akuity-platform \\n  --values ./charts/akuity-platform/test/values-dev.yaml \\n  --set image.password=$DOCKER_PASSWORD \\n  --set image.repository=us-docker.pkg.dev/akuity/akp/akuity-platform \\n  --set image.tag=latest \\n  --set portal.imagePullPolicy=IfNotPresent \\n  --set platformController.imagePullPolicy=IfNotPresent \\n  --namespace akuity-platform \\n  | kubectl apply -f -
 7446  k9s
 7447  cd portal/ui
 7448  ls
 7449  cd portal/ui
 7450  PORTAL_SERVER_URL=https://portal-server.akuity-platform pnpm run dev
 7451  cd ..
 7452  ls
 7453  cd ../kubevision-scripts
 7454  ENABLE_KARGO=false ./start-akp.sh
 7455  export HTTPS_PROXY=http://************:7890\nexport HTTP_PROXY=http://************:7890
 7456  ENABLE_KARGO=false ./start-akp.sh
 7457  ping goole.com
 7458  ping google.com
 7459  ENABLE_KARGO=false ./start-akp.sh
 7460  cd portal/ui
 7461  pnpm install -i .
 7462  pnpm install -i 
 7463  ping google.com
 7464  sh refresh-akp.sh
 7465  ENABLE_KARGO=false ./start-akp.sh
 7466  export HTTPS_PROXY=http://************:7890\nexport HTTP_PROXY=http://************:7890
 7467  ENABLE_KARGO=false ./start-akp.sh
 7468  unset http_proxy\nunset https_proxy\nunset HTTP_PROXY\nunset HTTPS_PROXY
 7469  ENABLE_KARGO=false ./start-akp.sh
 7470  kx
 7471  k get crd
 7472  kx
 7473  k get crd
 7474  kx
 7475  k get cr -A
 7476  k get crd -A
 7477  clear
 7478  cd ..
 7479  cd akuity-platform
 7480  git status
 7481  curl -fsSL https://download.aicodemirror.com/env_deploy/env-install.sh | bash
 7482  npm install -g @anthropic-ai/claude-code
 7483  claude -v
 7484  curl -fsSL https://download.aicodemirror.com/env_deploy/env-deploy.sh | bash -s -- "sk-ant-api03-Vbv6SbSCwJEJrFM3xUVG8LI2WQFW-ZzgbIe-Kp4qaS8bFJe2XGL-Aczyd88mxL4t4wFb30TrIBMD2wBqwhlO0w"
 7485  ak
 7486  cd akuity-platform
 7487  claude
 7488  echo 'Terminal capability test'
 7489  cd ../kubevision-scripts
 7490  sh refresh-akp.sh
 7491  export HTTPS_PROXY=http://************:7890\nexport HTTP_PROXY=http://************:7890
 7492  sh refresh-akp.sh
 7493  kx
 7494  k get po -A
 7495  kx
 7496  k get po -A
 7497  tiger
 7498  echo '****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'|base64 -d
 7499  ping cgroup.node1.s.nodelist-airport.com
 7500  cd ..
 7501  ls
 7502  cd python
 7503  ls
 7504  cd share
 7505  ls
 7506  cd ..
 7507  python3 sharefile.py --port 9010 --dir ~/Desktop/Desktop\ -\ Ming的MacBook\ Pro\ \(2\)/
 7508  tiger
 7509  ls
 7510  cd Desktop
 7511  ls
 7512  cd Desktop\ -\ Ming的MacBook\ Pro\ \(2\)/
 7513  ls
 7514  scp mixed718.yaml ming@************:~
 7515  scp mixed718.yaml ming@************:~
 7516  cat ~/.zshrc
 7517  cat ~/.zshrc |grep tiger
 7518  scp mixed718.yaml <EMAIL>:~
 7519  cd ~
 7520  sh setporxy.sh
 7521  ls
 7522  git status
 7523  vim a.md
 7524  claude
 7525  ak
 7526  cd akuity-platform
 7527  git status
 7528  cd ..
 7529  ls
 7530  cd akuity-platform
 7531  ls
 7532  git status
 7533  git add internal
 7534  git status
 7535  git commit -m "1" -s
 7536  git status
 7537  cd ../kubevision-scripts
 7538  sh refresh-akp.sh
 7539  export HTTPS_PROXY=http://************:7890\nexport HTTP_PROXY=http://************:7890
 7540  sh refresh-akp.sh
 7541  echo 'Terminal capability test'
 7542  export HTTPS_PROXY=http://************:7890\nexport HTTP_PROXY=http://************:7890
 7543  sh refresh-akp.sh
 7544  unset http_proxy\nunset https_proxy\nunset HTTP_PROXY\nunset HTTPS_PROXY
 7545  sh refresh-akp.sh
 7546  cd ..
 7547  cd akuity-platform
 7548  git status
 7549  git checkout main
 7550  git branch
 7551  cd ../kubevision-scripts
 7552  sh refresh-akp.sh
 7553  ENABLE_KARGO=false ./start-akp.sh
 7554  k9s
 7555  kx
 7556  k9s
 7557  ls
 7558  cd ~
 7559  ls
 7560  cd MyPro
 7561  ls
 7562  cd golang
 7563  ls
 7564  cd ClashToSS
 7565  ls
 7566  cat src.yaml
 7567  ls
 7568  cat main.go
 7569  ls
 7570  ./convert
 7571  cat src.yaml
 7572  ls
 7573  mv src.yaml src.yaml.bak
 7574  cp ~/Desktop/Desktop\ -\ Ming的MacBook\ Pro\ \(2\)/mixed718.yaml ./src.yaml
 7575  ./convert
 7576  clear
 7577  kx
 7578  k getns
 7579  k get ns
 7580  kx
 7581  k get ns
 7582  TOKEN="eyJhbGciOiJSUzI1NiIsImtpZCI6IjIzZGRlZDI3ZTVjYmJkMDRlYzhlZDg5NGYzMjMzZmFiMDNmOWVhM2EifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************.tN7zr2PXrZdrGgbbH7wGkYO3IryqWCLsd_n9FKDVcXFZBi85jkeam86J8hP4Rr9llMc5beQeJBuGHCi_8T2qYMc4heCfpGI3PgCytXnVuLKn6tc1RkSvo1vLZCt_pehgr5eHrkkNd6HgcC3u7vOW0jBQ5koPreoZmTM_g7Rkc_3oq9hG_HDKCiBjcgSL0P76G-CGXDK095KNEheRzdtn2d9TzwHXHSJ97tkb1YiiHqI2dPrP6y6xF_wQyKY7UbYArat726cWhYYoW_8XG-PwvjSLP3NPRrdvFwp9zDWQ4xLaj83IWwbmmK_3ls-b67-n8xcmK0HEAeOpNJKHm8alvw" && curl -s -H "Authorization: Bearer $TOKEN" "https://portal-server.akuity-platform/api/v1/orgs/6xxe21nbb65nl2xj/argocd/instances/eliij4q5cpasjs6g/clusters/288uy8hmcfz3fjhb/manifests?skipNamespace=true" | kubectl delete -f - && kubectl delete ns akp-demo
 7583  k get ns
 7584  k get po -A
 7585  k delete ns akuity-agent
 7586  k get po -A
 7587  kx
 7588  k get po -A
 7589  k get po -A -w
 7590  k get po -A
 7591  k3d image load quay.io/akuity/argocd:v3.0.6-ak.58 -c akuity-customer
 7592  k describe po -n argocd-kncruku0bwocnhom argocd-server-76448d69b9-7d6hb
 7593  k get po -A
 7594  kx
 7595  TOKEN="eyJhbGciOiJSUzI1NiIsImtpZCI6IjIzZGRlZDI3ZTVjYmJkMDRlYzhlZDg5NGYzMjMzZmFiMDNmOWVhM2EifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************.tN7zr2PXrZdrGgbbH7wGkYO3IryqWCLsd_n9FKDVcXFZBi85jkeam86J8hP4Rr9llMc5beQeJBuGHCi_8T2qYMc4heCfpGI3PgCytXnVuLKn6tc1RkSvo1vLZCt_pehgr5eHrkkNd6HgcC3u7vOW0jBQ5koPreoZmTM_g7Rkc_3oq9hG_HDKCiBjcgSL0P76G-CGXDK095KNEheRzdtn2d9TzwHXHSJ97tkb1YiiHqI2dPrP6y6xF_wQyKY7UbYArat726cWhYYoW_8XG-PwvjSLP3NPRrdvFwp9zDWQ4xLaj83IWwbmmK_3ls-b67-n8xcmK0HEAeOpNJKHm8alvw" && curl -s -H "Authorization: Bearer $TOKEN" "https://portal-server.akuity-platform/api/v1/orgs/6xxe21nbb65nl2xj/argocd/instances/kncruku0bwocnhom/clusters/ljl7il7xxlda2s04/manifests" | kubectl apply -f -
 7596  TOKEN="eyJhbGciOiJSUzI1NiIsImtpZCI6IjIzZGRlZDI3ZTVjYmJkMDRlYzhlZDg5NGYzMjMzZmFiMDNmOWVhM2EifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************.tN7zr2PXrZdrGgbbH7wGkYO3IryqWCLsd_n9FKDVcXFZBi85jkeam86J8hP4Rr9llMc5beQeJBuGHCi_8T2qYMc4heCfpGI3PgCytXnVuLKn6tc1RkSvo1vLZCt_pehgr5eHrkkNd6HgcC3u7vOW0jBQ5koPreoZmTM_g7Rkc_3oq9hG_HDKCiBjcgSL0P76G-CGXDK095KNEheRzdtn2d9TzwHXHSJ97tkb1YiiHqI2dPrP6y6xF_wQyKY7UbYArat726cWhYYoW_8XG-PwvjSLP3NPRrdvFwp9zDWQ4xLaj83IWwbmmK_3ls-b67-n8xcmK0HEAeOpNJKHm8alvw" && curl -s -H "Authorization: Bearer $TOKEN" "https://portal-server.akuity-platform/api/v1/orgs/6xxe21nbb65nl2xj/argocd/instances/kncruku0bwocnhom/clusters/ljl7il7xxlda2s04/manifests" -k | kubectl apply -f -
 7597  k get po -A
 7598  k describe po -n akuity-agent argocd-repo-server-db67645c4-glrxx
 7599  k3d image load quay.io/akuity/argocd:v3.0.11-ak.59 -c akuity-customer
 7600  k get po -A
 7601  k describe po -n akuity-agent argocd-repo-server-db67645c4-glrxx
 7602  k get po -A
 7603  vim /etc/hosts
 7604  sudo vim /etc/hosts
 7605  echo 'Terminal capability test'
 7606  git status
 7607  git diff 
 7608  cd ../kubevision-scripts
 7609  sh refresh-akp.sh
 7610  kx
 7611  k get po -n akuity-platform
 7612  k logs -n akuity-platform platform-controller-6bbd46554-4chr4
 7613  k logs -n akuity-platform platform-controller-6bbd46554-4chr4 |grep spec
 7614  k logs -n akuity-platform platform-controller-6bbd46554-4chr4 > a.log
 7615  vim a.log
 7616  sh refresh-akp.sh
 7617  rm -rf a.log
 7618  k logs -n akuity-platform platform-controller-68d9fdcdf7-mw79t > a.log
 7619  vim a.log
 7620  open a.log
 7621  clear
 7622  git status
 7623  cd ..
 7624  cd akuity-platform
 7625  git status
 7626  git diff  internal/services/ai/reposet/reposet.go
 7627  cd ../kubevision-scripts
 7628  sh refresh-akp.sh
 7629  ls
 7630  rm -rf src.yaml
 7631  vim src.yaml
 7632  ./convert
 7633  echo '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' |base64 -d
 7634  ./convert
 7635  ping hk01.653ebeeb-5f60-4be5-958d-7bf4839cdccd.ef6616fd-5b06-482f-9d64-183745659cbf.byteprivatelink.com
 7636  cd ~
 7637  sh setporxy.sh
 7638  ls
 7639  vim src.yaml
 7640  rm -rf src.yaml
 7641  vim src.yaml
 7642  ./convert
 7643  cat src.yaml
 7644  cat src.yaml.bak|less
 7645  ls
 7646  vim main.go
 7647  cd Desktop/Desktop\ -\ Ming的MacBook\ Pro\ \(2\)/
 7648  ls
 7649  open .
 7650  ls
 7651  mv mixed718\ copy.yaml mihomo.yaml
 7652  ping hk01.653ebeeb-5f60-4be5-958d-7bf4839cdccd.ef6616fd-5b06-482f-9d64-183745659cbf.byteprivatelink.com
 7653  ping cgroup.node3.s.nodelist-airport.com
 7654  echo '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'|base64 -d
 7655  tiger
 7656  ls
 7657  scp mihomo.yaml <EMAIL>:~
 7658  cd ..
 7659  cd golang
 7660  ls
 7661  cd ClashToSS
 7662  ls
 7663  rm -rf src.yaml
 7664  cp ~/Desktop/Desktop\ -\ Ming的MacBook\ Pro\ \(2\)
 7665  cp ~/Desktop/Desktop\ -\ Ming的MacBook\ Pro\ \(2\)/mihomo.yaml src.yaml
 7666  ./convert
 7667  tiger
 7668  cd Desktop
 7669  ls
 7670  cd Desktop\ -\ Ming的MacBook\ Pro\ \(2\)
 7671  scp mihomo.yaml <EMAIL>:~
 7672  ls
 7673  rm -rf mixed718.yaml
 7674  cat mihomo.yaml
 7675  clear
 7676  ls
 7677  vim single.yaml
 7678  cd ~/MyPro/python
 7679  ls
 7680  python3 sharefile.py --port 9010 --dir ~/Desktop/Desktop\ -\ Ming的MacBook\ Pro\ \(2\)/
 7681  clear
 7682  exit
 7683  tiger
 7684  node --eval 'const os = require("os"); const fs = require("fs"); const path = require("path"); const homeDir = os.homedir(); const filePath = path.join(homeDir, ".claude.json"); if (fs.existsSync(filePath)) {const content = JSON.parse(fs.readFileSync(filePath, "utf-8")); fs.writeFileSync(filePath, JSON.stringify({ ...content, hasCompletedOnboarding: true }, null, 2), "utf-8");} else {fs.writeFileSync(filePath, JSON.stringify({ hasCompletedOnboarding: true }), "utf-8");}'
 7685  vim ~/.zshrc
 7686  ls
 7687  cd MyPro
 7688  cd akuity
 7689  ls
 7690  cd akuity-platform
 7691  ls
 7692  claude
 7693  source ~/.zshrc
 7694  claude
 7695  echo 'Terminal capability test'
 7696  cd MyPro
 7697  ls
 7698  cd k9s
 7699  ls
 7700  cd ..
 7701  ls
 7702  cd python
 7703  ls
 7704  cd stock
 7705  ls
 7706  cat test.py
 7707  cd ..
 7708  ls
 7709  cd ..
 7710  ls
 7711  cd ..
 7712  cd golang
 7713  ls
 7714  mkdir AutoProxy
 7715  cd AutoProxy
 7716  claude
 7717  ping api.moonshot.cn
 7718  claude
 7719  s
 7720  vim ~/.zshrc
 7721  unset http_proxy\nunset https_proxy\nunset HTTP_PROXY\nunset HTTPS_PROXY
 7722  claude
 7723  npm install -g @anthropic-ai/claude-code
 7724  node --eval 'const os = require("os"); const fs = require("fs"); const path = require("path"); const homeDir = os.homedir(); const filePath = path.join(homeDir, ".claude.json"); if (fs.existsSync(filePath)) {const content = JSON.parse(fs.readFileSync(filePath, "utf-8")); fs.writeFileSync(filePath, JSON.stringify({ ...content, hasCompletedOnboarding: true }, null, 2), "utf-8");} else {fs.writeFileSync(filePath, JSON.stringify({ hasCompletedOnboarding: true }), "utf-8");}'
 7725  source ~/.zshrc
 7726  claude
 7727  vim ~/.claude.json
 7728  vim test.py
 7729  cat ~/.zshrc
 7730  python3 test.py
 7731  vim test.py
 7732  bash -c "$(curl -fsSL https://raw.githubusercontent.com/LLM-Red-Team/kimi-cc/main/install.sh)"
 7733  ak
 7734  ls
 7735  cd ..
 7736  ls
 7737  cd golang
 7738  ls
 7739  cd AutoProxy
 7740  Claude
 7741  unset http_proxy\nunset https_proxy\nunset HTTP_PROXY\nunset HTTPS_PROXY
 7742  clear
 7743  ping google.com
 7744  Claude
 7745  ping api.moonshot.cn
 7746  bash -c "$(curl -fsSL https://raw.githubusercontent.com/LLM-Red-Team/kimi-cc/main/install.sh)"
 7747  cat ~/.zshrc
 7748  cat ~/.zshrc |grep sk-ZBSCP9LXWFYMS9ld0r1AQxwnKqZ3KwejQ5VMx8Ien1vHvq57
 7749  bash -c "$(curl -fsSL https://raw.githubusercontent.com/LLM-Red-Team/kimi-cc/main/install.sh)"
 7750  vim ~/.zshrc
 7751  bash -c "$(curl -fsSL https://raw.githubusercontent.com/LLM-Red-Team/kimi-cc/main/install.sh)"
 7752  vim ~/.zshrc
 7753  bash -c "$(curl -fsSL https://raw.githubusercontent.com/LLM-Red-Team/kimi-cc/main/install.sh)"
 7754  vim ~/.zshrc
 7755  ak
 7756  cd ../
 7757  ls
 7758  cd golang
 7759  ls
 7760  cd AutoProxy
 7761  ls
 7762  claude
 7763  bash -c "$(curl -fsSL https://raw.githubusercontent.com/LLM-Red-Team/kimi-cc/main/install.sh)"
 7764  vim ~/.zshrc
 7765  bash -c "$(curl -fsSL https://raw.githubusercontent.com/LLM-Red-Team/kimi-cc/main/install.sh)"
 7766  exit
 7767  ak
 7768  cd ../golang
 7769  auto
 7770  ls
 7771  cd AutoProxy
 7772  ls
 7773  source ~/.zshrc
 7774  claude
 7775  tiger
 7776  ls
 7777  cd MyPro
 7778  echo 'Terminal capability test'
 7779  ls
 7780  echo 'Terminal capability test'
 7781  cd golang
 7782  ls
 7783  mkdir ProxyFree
 7784  echo 'Terminal capability test'
 7785  go mod tidy
 7786  (export GOPROXY=https://goproxy.cn && go mod tidy
 7787  export GOPROXY=https://goproxy.cn && go mod tidy
 7788  go build -o transparent-proxy main.go
 7789  chmod +x build.sh
 7790  make build
 7791  ls
 7792  make 
 7793  make build
 7794  sh build.sh
 7795  brew install x86_64-linux-gnu-gcc
 7796  sh build.sh
 7797  brew install FiloSottile/musl-cross/musl-cross
 7798  ls
 7799  ls -lsht
 7800  ./autoproxy -h
 7801  brew install FiloSottile/musl-cross/musl-cross
 7802  sh build.sh
 7803  brew tap messense/macos-cross-toolchains
 7804  brew install x86_64-unknown-linux-gnu
 7805  sh build.sh
 7806  export CC_x86_64_unknown_linux_gnu=x86_64-unknown-linux-gnu-gcc\nexport CXX_x86_64_unknown_linux_gnu=x86_64-unknown-linux-gnu-g++\nexport AR_x86_64_unknown_linux_gnu=x86_64-unknown-linux-gnu-ar
 7807  sh build.sh
 7808  tiger
 7809  ls
 7810  rm -rf transparent-proxy
 7811  cd ..
 7812  tar xcvf ProxyFree.tar.gz ProxyFree
 7813  tar -xcvf  ProxyFree.tar.gz ProxyFree
 7814  tar -zcvf  ProxyFree.tar.gz ProxyFree
 7815  ls
 7816  scp ProxyFree.tar.gz <EMAIL>:~
 7817  cd MyPro
 7818  ls
 7819  cd golang 
 7820  ls
 7821  tiger
 7822  ls
 7823  ping ************
 7824  wget https://github.com/clash-verge-rev/clash-verge-rev/releases/download/v2.0.2/Clash.Verge_2.0.2_aarch64.dmg
 7825  open .
 7826  cd ~/Desktop
 7827  ls
 7828  cd Desktop\ -\ Ming的MacBook\ Pro\ \(2\)
 7829  ls
 7830  open .
 7831  vim yiyuan.yaml
 7832  clear
 7833  cd ../kubevision-scripts
 7834  sh refresh-akp.sh
 7835  unset http_proxy\nunset https_proxy\nunset HTTP_PROXY\nunset HTTPS_PROXY
 7836  sh refresh-akp.sh
 7837  k get po -A
 7838  echo 'Terminal capability test'
 7839  clear
 7840  git status
 7841  git diff
 7842  cd ../kubevision-scripts
 7843  ls
 7844  sh refresh-akp.sh
 7845  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 7846  git status
 7847  cd ../kubevision-scripts
 7848  cd ..
 7849  ls
 7850  cd akuity-platform
 7851  git status
 7852  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 7853  git status
 7854  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 7855  clear
 7856  git status
 7857  git checkout aims
 7858  git status
 7859  git checkout  portal
 7860  git status
 7861  git log
 7862  git branch
 7863  git status
 7864  rm -rf         internal/services/ai/functions/a.json
 7865  git status
 7866  rm -rf  portal/ui/src/feature/ai-support-engineer/components/runbook-viewer/  aims/ui/src/ai-support-engineer/runbook-viewer.tsx  aims/ui/src/ai-support-engineer/runbook-viewer.less
 7867  git status
 7868  rm -rf RUNBOOK_STORE_FEATURE.md STORE_RUNBOOK_SIMPLE.md test-runbook-feature.md
 7869  git status
 7870  rm -rf a.md
 7871  ls
 7872  git status
 7873  rm -rf a.log bug.json
 7874  rm c.log
 7875  git status
 7876  git add internal
 7877  git status
 7878  git commit -m "add store runbook support" -s
 7879  git status
 7880  clear
 7881  ls
 7882  cat install.sh
 7883  ls
 7884  rm -rf install.sh
 7885  git status
 7886  echo 'Terminal capability test'
 7887  ls
 7888  clear
 7889  ls
 7890  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 7891  clear
 7892  git status
 7893  git diff  aims/ui/src/ai-support-engineer/runbook-viewer.tsx
 7894  git status
 7895  git diff aims/ui/src/ai-support-engineer/message-bubble.less
 7896  git diff   aims/ui/src/ai-support-engineer/message-bubble.tsx
 7897  git diff portal/ui/src/feature/ai-support-engineer/components/message-bubble/message-bubble.tsx
 7898  rm test-runbook-feature.md
 7899  git status
 7900  git diff portal/ui/src/feature/ai-support-engineer/components/message-bubble/message-bubble.tsx
 7901  clear
 7902  git status
 7903  rm -rf RUNBOOK_STORE_FEATURE.md
 7904  git status
 7905  git add aims portal 
 7906  git status
 7907  git commit -m "add store runbook button" -s
 7908  git status
 7909  git branch
 7910  git checkout -b store-runbook
 7911  git push
 7912  make generate-in-container
 7913  clear
 7914  git status
 7915  git diff portal/ui/src/feature/ai-support-engineer/components/message-bubble/message-bubble.tsx
 7916  git diff portal/ui/src/feature/ai-support-engineer/components/runbook-viewer/runbook-viewer.tsx
 7917  git status
 7918  clear
 7919  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 7920  clear
 7921  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 7922  clear
 7923  echo 'Terminal capability test'
 7924  git status
 7925  make generate-in-container
 7926  unset http_proxy\nunset https_proxy\nunset HTTP_PROXY\nunset HTTPS_PROXY
 7927  make generate-in-container
 7928  docker pull mirror.gcr.io/node:23.1.0-bookworm-slim
 7929  make generate-in-container
 7930  docker pull golang:1.24.4-bullseye
 7931  make generate-in-container
 7932  ping google.com
 7933  ping baidu.com
 7934  make generate-in-container
 7935  ping google.com
 7936  docker pull  mirror.gcr.io/bufbuild/buf:1.46.0
 7937  ping google.com
 7938  make generate-in-container
 7939  docker pull docker.io/golangci/golangci-lint:v2.1.6
 7940  make generate-in-container
 7941  git status
 7942  make generate-in-container
 7943  git diff
 7944  git diff aims/ui/src/lib/apiclient/organization/v1/organization_pb.ts
 7945  git diff portal/ui/src/feature/ai-support-engineer/components/message-bubble/message-bubble.tsx
 7946  git diff portal/ui/src/feature/ai-support-engineer/components/runbook-viewer/runbook-viewer.tsx
 7947  cd ../kubevision-scripts
 7948  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 7949  git status
 7950  git diff
 7951  git diff   portal/ui/src/feature/ai-support-engineer/components/runbook-viewer/runbook-viewer.tsx
 7952  ak
 7953  cd akuity-platform
 7954  ls
 7955  claude
 7956  clear
 7957  git status
 7958  git add aims api docs internal portal
 7959  git status
 7960  git add pkg
 7961  git status
 7962  git commit -m "modify runbook structure" -s
 7963  git status
 7964  git diff
 7965  git diff internal/services/ai/service.go
 7966  git status
 7967  git checkout  internal/services/ai/service.go
 7968  git status
 7969  git diff  portal/ui/src/lib/apiclient/organization/v1/organization_pb.ts
 7970  git checkout portal/ui/src/lib/apiclient/organization/v1/organization_pb.ts
 7971  git status
 7972  git diff portal/ui/src/feature/ai-support-engineer/components/message-bubble/message-bubble.tsx
 7973  git checkout  portal/ui/src/feature/ai-support-engineer/components/message-bubble/message-bubble.tsx
 7974  git status
 7975  git diff  portal/ui/src/feature/ai-support-engineer/components/runbook-viewer/runbook-viewer.tsx
 7976  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 7977  claude
 7978  cd ../
 7979  ls
 7980  cd akuity-platform
 7981  ls
 7982  git status
 7983  git diff
 7984  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 7985  git status
 7986  git diff portal/ui/src/feature/ai-support-engineer/components/runbook-viewer/runbook-viewer.tsx
 7987  clear
 7988  git status
 7989  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 7990  git status
 7991  git diff 
 7992  git status
 7993  git add portal
 7994  git status
 7995  git log
 7996  git commit --amend
 7997  git status
 7998  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 7999  git status
 8000  git diff
 8001  git status
 8002  git add aims
 8003  git status
 8004  git add portal
 8005  git status
 8006  git commit --amend
 8007  git status
 8008  git checkout -b runbook
 8009  git status
 8010  git log
 8011  git reset HEAD~1 --hard
 8012  git log
 8013  git reset HEAD~1 --hard
 8014  git log
 8015  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 8016  git log
 8017  git branch
 8018  git checkout save-runbook
 8019  git log
 8020  git branch
 8021  git checkout store-runbook
 8022  git log
 8023  git checkout runbook
 8024  git cherry-pick be1c7f18665cef56be136687b18d7e63fca95da1
 8025  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 8026  git branch
 8027  git checkout store-runbook
 8028  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 8029  git status
 8030  git diff
 8031  git status
 8032  git diff
 8033  clear
 8034  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 8035  git status
 8036  git checkout portal
 8037  git status
 8038  make format
 8039  git status
 8040  git add internal
 8041  git status
 8042  git commit --amend
 8043  git status
 8044  git push
 8045  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 8046  tiger
 8047  ping ************
 8048  ping *************
 8049  diskutil list
 8050  sudo diskutil eraseDisk ExFAT MyDisk /dev/disk8
 8051  ping google.com
 8052  unset http_proxy\nunset https_proxy\nunset HTTP_PROXY\nunset HTTPS_PROXY
 8053  ping google.com
 8054  git status
 8055  git diff
 8056  git add internal
 8057  git add models
 8058  git status
 8059  git commit -m "generate runbook name" -s
 8060  git push
 8061  sh refresh-akp.sh
 8062  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 8063  k get po -n akuity-platform
 8064  kx
 8065  k get po -A
 8066  kx
 8067  k get po -A
 8068  k delete po -n akuity-agent --all
 8069  k get po -A
 8070  clear
 8071  k get po -A
 8072  k describe po -n akuity-agent argocd-application-controller-5bfccd69f8-bbvvq
 8073  k get po -A
 8074  k describe po -n akuity-agent akuity-agent-688b755c7-nqgdj
 8075  k get po -A
 8076  k delete ns akuity-agent
 8077  TOKEN="eyJhbGciOiJSUzI1NiIsImtpZCI6ImZlNGM2YWZhYTUwOWIzMzM1OTYxYzY1YzMyMjg3MmIzMTQ4MDRkMDUifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************.UK0JRG7uIOn2NOrbzicgj8bTBL0VjSllFQzbfJAy7GZXiNiiW0FGiFrwNH_neeFnyIHs-YgTd9j_CwrDxa97Y2DWDDWuZXXHaHwy5-dWncd0QGvbNPYcn4sy8cNj3OlWRWKgEX-UA3_F23ldSYK5Pmw4B-NpwkifhbB2P5l9Ru4cHR4h4Lcaj9GVFz1BUtYi7GVtS4tJgL-bomgvWxBI_rz-UAD0VCLqDDtPtqhDzCjIb2gQie6Y4dsugmumeOjLDidx9Bl_CM-TycwihefNfEom8z3GYCzJDKQBwkk4u81B_6KJFsyIUmVpx9_Jcr7g_zK4nzl2hrPBqZIIMmRgcw" && curl -s -H "Authorization: Bearer $TOKEN" "https://portal-server.akuity-platform/api/v1/orgs/6xxe21nbb65nl2xj/argocd/instances/kncruku0bwocnhom/clusters/ljl7il7xxlda2s04/manifests?skipNamespace=true" | kubectl delete -f - && kubectl delete ns akp-demo
 8078  k delete ns akuity-agent
 8079  k get po -A
 8080  TOKEN="eyJhbGciOiJSUzI1NiIsImtpZCI6ImZlNGM2YWZhYTUwOWIzMzM1OTYxYzY1YzMyMjg3MmIzMTQ4MDRkMDUifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************.UK0JRG7uIOn2NOrbzicgj8bTBL0VjSllFQzbfJAy7GZXiNiiW0FGiFrwNH_neeFnyIHs-YgTd9j_CwrDxa97Y2DWDDWuZXXHaHwy5-dWncd0QGvbNPYcn4sy8cNj3OlWRWKgEX-UA3_F23ldSYK5Pmw4B-NpwkifhbB2P5l9Ru4cHR4h4Lcaj9GVFz1BUtYi7GVtS4tJgL-bomgvWxBI_rz-UAD0VCLqDDtPtqhDzCjIb2gQie6Y4dsugmumeOjLDidx9Bl_CM-TycwihefNfEom8z3GYCzJDKQBwkk4u81B_6KJFsyIUmVpx9_Jcr7g_zK4nzl2hrPBqZIIMmRgcw" && curl -s -H "Authorization: Bearer $TOKEN" "https://portal-server.akuity-platform/api/v1/orgs/6xxe21nbb65nl2xj/argocd/instances/kncruku0bwocnhom/clusters/ljl7il7xxlda2s04/manifests?skipNamespace=true" -k | kubectl delete -f - && kubectl delete ns akp-demo
 8081  k get po -A
 8082  kx
 8083  k get po -A
 8084  k get po -A -w
 8085  kx
 8086  TOKEN="eyJhbGciOiJSUzI1NiIsImtpZCI6ImZlNGM2YWZhYTUwOWIzMzM1OTYxYzY1YzMyMjg3MmIzMTQ4MDRkMDUifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************.UK0JRG7uIOn2NOrbzicgj8bTBL0VjSllFQzbfJAy7GZXiNiiW0FGiFrwNH_neeFnyIHs-YgTd9j_CwrDxa97Y2DWDDWuZXXHaHwy5-dWncd0QGvbNPYcn4sy8cNj3OlWRWKgEX-UA3_F23ldSYK5Pmw4B-NpwkifhbB2P5l9Ru4cHR4h4Lcaj9GVFz1BUtYi7GVtS4tJgL-bomgvWxBI_rz-UAD0VCLqDDtPtqhDzCjIb2gQie6Y4dsugmumeOjLDidx9Bl_CM-TycwihefNfEom8z3GYCzJDKQBwkk4u81B_6KJFsyIUmVpx9_Jcr7g_zK4nzl2hrPBqZIIMmRgcw" && curl -s -H "Authorization: Bearer $TOKEN" "https://portal-server.akuity-platform/api/v1/orgs/6xxe21nbb65nl2xj/argocd/instances/7rh45ob9euvvj5xp/clusters/i00o1mu6nyjc2azv/manifests" -k | kubectl apply -f -
 8087  k get po -A
 8088  k get po -A -w
 8089  k get po -A 
 8090  k get po -A -w
 8091  k describe po -n akuity-agent argocd-notifications-controller-6cc4f7ff65-bmk8p
 8092  k get po -A -w
 8093  k describe po -n akuity-agent argocd-repo-server-db67645c4-8q5tb
 8094  clear
 8095  ls
 8096  k get po -A
 8097  k delete po -n akuity-agent --all
 8098  k get po -A
 8099  TOKEN="eyJhbGciOiJSUzI1NiIsImtpZCI6ImZlNGM2YWZhYTUwOWIzMzM1OTYxYzY1YzMyMjg3MmIzMTQ4MDRkMDUifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************.UK0JRG7uIOn2NOrbzicgj8bTBL0VjSllFQzbfJAy7GZXiNiiW0FGiFrwNH_neeFnyIHs-YgTd9j_CwrDxa97Y2DWDDWuZXXHaHwy5-dWncd0QGvbNPYcn4sy8cNj3OlWRWKgEX-UA3_F23ldSYK5Pmw4B-NpwkifhbB2P5l9Ru4cHR4h4Lcaj9GVFz1BUtYi7GVtS4tJgL-bomgvWxBI_rz-UAD0VCLqDDtPtqhDzCjIb2gQie6Y4dsugmumeOjLDidx9Bl_CM-TycwihefNfEom8z3GYCzJDKQBwkk4u81B_6KJFsyIUmVpx9_Jcr7g_zK4nzl2hrPBqZIIMmRgcw" && curl -s -H "Authorization: Bearer $TOKEN" "https://portal-server.akuity-platform/api/v1/orgs/6xxe21nbb65nl2xj/argocd/instances/7rh45ob9euvvj5xp/clusters/i00o1mu6nyjc2azv/manifests" -k | kubectl apply -f -
 8100  k get po -A
 8101  kx
 8102  k3d cluster delete akuity-customer
 8103  kx
 8104  k3d cluster create akuity-customer
 8105  k get po -A
 8106  ls
 8107  k get po -A
 8108  TOKEN="eyJhbGciOiJSUzI1NiIsImtpZCI6ImZlNGM2YWZhYTUwOWIzMzM1OTYxYzY1YzMyMjg3MmIzMTQ4MDRkMDUifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************.UK0JRG7uIOn2NOrbzicgj8bTBL0VjSllFQzbfJAy7GZXiNiiW0FGiFrwNH_neeFnyIHs-YgTd9j_CwrDxa97Y2DWDDWuZXXHaHwy5-dWncd0QGvbNPYcn4sy8cNj3OlWRWKgEX-UA3_F23ldSYK5Pmw4B-NpwkifhbB2P5l9Ru4cHR4h4Lcaj9GVFz1BUtYi7GVtS4tJgL-bomgvWxBI_rz-UAD0VCLqDDtPtqhDzCjIb2gQie6Y4dsugmumeOjLDidx9Bl_CM-TycwihefNfEom8z3GYCzJDKQBwkk4u81B_6KJFsyIUmVpx9_Jcr7g_zK4nzl2hrPBqZIIMmRgcw" && curl -s -H "Authorization: Bearer $TOKEN" "https://portal-server.akuity-platform/api/v1/orgs/6xxe21nbb65nl2xj/argocd/instances/7rh45ob9euvvj5xp/clusters/i00o1mu6nyjc2azv/manifests" -k| kubectl apply -f -
 8109  k get po -A
 8110  k get po -A -w
 8111  k get po -A
 8112  k get po -A -w
 8113  k get po -A
 8114  k describe po -n akuity-agent argocd-repo-server-db67645c4-swctd
 8115  ak
 8116  cd kubevision-scripts
 8117  ls
 8118  ./stop-akp.sh
 8119  ENABLE_KARGO=false ./start-akp.sh
 8120  k get po -A
 8121  k get po -A -w
 8122  k get po -A
 8123  k get secret -A
 8124  k get po -A
 8125  k get secret -A
 8126  k get po -A
 8127  k describe po -n akuity argocd-repo-server-b9956bc4-mc2t9
 8128  k get po -A
 8129  k describe po -n akuity argocd-repo-server-b9956bc4-mc2t9
 8130  k3d image load quay.io/akuity/argocd:v3.0.6-ak.58 -c akuity-customer
 8131  k get po -A
 8132  kx
 8133  k get po -A
 8134  kx
 8135  k get po -A
 8136  kx
 8137  k get po -A
 8138  k get secrets -A
 8139  kx
 8140  k get secrets -A
 8141  k get po -A
 8142  kx
 8143  k get po -A
 8144  TOKEN="eyJhbGciOiJSUzI1NiIsImtpZCI6ImM4MmYwMjliZDIyN2YxMTc4Y2FlOTlhZmI1Y2M3MzJhYzFmYjk5NTkifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.W2e0FqLippUccQMnca5iLaKuWRvF1f0wyZA7xLzoBgAEd7GGf1z2Le9E2a1SwvDWJKUFIdDBY-a9EFUSN0yj97NreLjpX2PEZTfPaIE6lX9bSzNWPdrdfXRtMH2JKb9pIPVy4y3WMvCaSxfTyJAM4YoEDAdFtFRVt_QTxVsVltrGCN90Uoa08ELd4c3u9iD4vr4UcbMEoZvwWZcnGPt2lbp4UYko7xgVDzJxNLbqy2eS68ehOITLQ-keAEViOKf9p4picV-lb8jlIwHdyMX8wsu1PyiJrGx2S6YUIUgWjmao-nNt48mPYkZSFqdsuS2b71Lf8MgZan2fYXxf-Vr47g" && curl -s -H "Authorization: Bearer $TOKEN" "https://portal-server.akuity-platform/api/v1/orgs/p2curjd0rwxa8gk6/argocd/instances/w493ufh5fvx1kkag/clusters/ijidf10etcbzmfyk/manifests" -k | kubectl apply -f -
 8145  k get po -A
 8146  k describe po -n akuity-agent argocd-repo-server-db67645c4-hpr8f
 8147  k get cm -A
 8148  k get po -A
 8149  k get cm -A
 8150  k describe po -n akuity-agent argocd-repo-server-db67645c4-hpr8f
 8151  k get po -A
 8152  k describe po -n akuity-agent argocd-repo-server-db67645c4-hpr8f
 8153  k get po -A
 8154  k delete ns akuity
 8155  vim /etc/hosts
 8156  sudo vim /etc/hosts
 8157  clear
 8158  git status
 8159  clear
 8160  ls
 8161  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 8162  ping google.com
 8163  git status
 8164  git diff 
 8165  git diff portal
 8166  git checkout portal
 8167  git status
 8168  clear
 8169  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 8170  git status
 8171  git diff
 8172  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 8173  clear
 8174  ls
 8175  clear
 8176  git status
 8177  git diff portal
 8178  git add internal portal
 8179  git status
 8180  git commit -m "disable restore runbook when restored" -s
 8181  git status]
 8182  git status
 8183  git push
 8184  git fetch origin
 8185  git merge origin/main
 8186  make generate-in-container
 8187  git status
 8188  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 8189  clear
 8190  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 8191  k9s
 8192  git status
 8193  git diff 
 8194  git diff  > diff.log
 8195  git pull
 8196  git log
 8197  git status
 8198  git add manifests
 8199  git status
 8200  git commit -m "modify version" -s
 8201  git pull
 8202  git pull --rebase
 8203  git log
 8204  ENABLE_KARGO=false ./start-akp.sh
 8205  ls
 8206  ./stop-akp.sh
 8207  ENABLE_KARGO=false ./start-akp.sh
 8208  kx
 8209  k get po -A
 8210  kx
 8211  k get po -A
 8212  kx
 8213  k get po -A
 8214  kx
 8215  k get po -A
 8216  TOKEN="******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" && curl -s -H "Authorization: Bearer $TOKEN" "https://portal-server.akuity-platform/api/v1/orgs/c7izp6dcuvuhepus/argocd/instances/q5urhdgegkwuxhy5/clusters/wnzwpximjhbun66c/manifests" -k | kubectl apply -f -
 8217  k get po -A
 8218  k delete ns akuity
 8219  sudo vim /etc/hosts
 8220  git status
 8221  git diff internal/services/ai/prompt.md
 8222  git status
 8223  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 8224  cd ..
 8225  cd akuity-platform
 8226  cd portal/ui
 8227  pnpm install .
 8228  npnm build .
 8229  npm build .
 8230  pnpm build .
 8231  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 8232  exit
 8233  cd ..
 8234  cd akuity-platform
 8235  ls
 8236  git status
 8237  git diff pkg/api/gen/organization/v1/organization.pb.go
 8238  clear
 8239  ls
 8240  cd aims
 8241  ls
 8242  cd ui
 8243  ls
 8244  pnpm i 
 8245  cd ..
 8246  cd portal
 8247  pnpm i
 8248  cd ui
 8249  pnpm i
 8250  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 8251  clear
 8252  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 8253  git status
 8254  git add pkg
 8255  git status
 8256  git add aims api docs internal models portal
 8257  git status
 8258  git commit
 8259  git status
 8260  git push
 8261  k9s
 8262  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 8263  git status
 8264  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 8265  git diff
 8266  clear
 8267  k get po -n akuity-platform
 8268  git diff
 8269  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 8270  git status
 8271  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 8272  rm -rf aims/ui/src/ai-support-engineer/a.json
 8273  git status
 8274  git add aims internal
 8275  git staus
 8276  git status
 8277  git add internal
 8278  git status
 8279  git commit -m "modify prompt" -s
 8280  cd portal/ui
 8281  pnpm run lint --fix
 8282  git status
 8283  cd ../
 8284  cd ..
 8285  cd aims
 8286  pnpm run lint --fix
 8287  cd ui
 8288  pnpm run lint --fix
 8289  pnpm run lint 
 8290  pwd
 8291  cd ../..
 8292  ls
 8293  cd aims/ui/src/ai-support-engineer/
 8294  ls
 8295  pnpm run lint --fix
 8296  pnpm run lint 
 8297  git status
 8298  cd ..
 8299  git status
 8300  cd ..
 8301  git status
 8302  git diff
 8303  git add aims portal
 8304  git status
 8305  git commit -m "fix linter" -s
 8306  git push
 8307  echo 'Terminal capability test'
 8308  ls
 8309  cd MyPro
 8310  ls
 8311  cd golang
 8312  ls
 8313  cd ..
 8314  ls
 8315  echo 'Terminal capability test'
 8316  pip install -r requirements.txt
 8317  pip3 
 8318  pip3 install -r requirements.txt
 8319  python3 start.py
 8320  pip3 install -r requirements.txt\n➜  StockAlert 
 8321  pip3 install -r requirements.txt
 8322  chmod +x setup_venv.sh\n./setup_venv.sh
 8323  /Library/Developer/CommandLineTools/usr/bin/python3 /Users/<USER>/.vscode/extensions/ms-python.python-2025.10.1-darwin-arm64/python_files/printEnvVariablesToFile.py /Users/<USER>/.vscode/extensions/ms-python.python-2025.10.1-darwin-arm64/python_files/deactivate/zsh/envVars.txt
 8324  tiger
 8325  vim .ssh/known_hosts
 8326  tiger
 8327  vim ~/.ssh/known_hosts
 8328  tiger
 8329  vim ~/.ssh/known_hosts
 8330  tiger
 8331  scp ~/Downloads/mihomo-linux-amd64-alpha-63ad95e.deb admin@tiger:~
 8332  scp ~/Downloads/mihomo-linux-amd64-alpha-63ad95e.deb admin@************:~
 8333  scp ~/Downloads/mihomo-linux-amd64-alpha-63ad95e.deb admin@************:/home/
 8334  scp ~/Downloads/mihomo-linux-amd64-alpha-63ad95e.deb admin@************:/home
 8335  scp ~/Downloads/mihomo-linux-amd64-alpha-63ad95e.deb admin@************:/home/<USER>
 8336  ak
 8337  cd ../SlackUp
 8338  ls
 8339  ./SlackUp
 8340  tiger
 8341  wget https://github.com/wnlen/clash-for-linux/archive/refs/heads/master.zip
 8342  ls
 8343  rm -rf master.zip.1
 8344  scp master.zip admin@************:~
 8345  tiger
 8346  cd Downloads
 8347  scp metacubexd-gh-pages.zip admin@************:~
 8348  cd ../kubevision-scripts
 8349  sh refresh-akp.sh
 8350  export HTTPS_PROXY=http://************:7890\nexport HTTP_PROXY=http://************:7890
 8351  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 8352  c
 8353  clear
 8354  k9s
 8355  cd ..
 8356  ls
 8357  cd akuity-platform
 8358  git status
 8359  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 8360  make generate-in-container
 8361  git status
 8362  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 8363  git status
 8364  git diff portal/ui/src/lib/apiclient/organization/v1/organization_pb.ts
 8365  git status
 8366  git checkout portal/ui/src/lib/apiclient/organization/v1/organization_pb.ts
 8367  git status
 8368  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 8369  export HTTPS_PROXY=http://************:7890\nexport HTTP_PROXY=http://************:7890
 8370  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 8371  sudo vim /etc/hosts
 8372  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 8373  ls
 8374  git remote -v
 8375  cd ..
 8376  ls
 8377  cd argo-cd
 8378  git remote -v
 8379  export HTTPS_PROXY=http://************:7890\nexport HTTP_PROXY=http://************:7890
 8380  clear
 8381  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 8382  git status
 8383  clear
 8384  ak
 8385  cd akuity-platform
 8386  clause
 8387  claude
 8388  git status
 8389  make generate-in-container
 8390  git status
 8391  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 8392  ssh root@*************
 8393  ssh-copy-id <EMAIL>
 8394  ssh-copy-id root@*************
 8395  vim ~/.zshrc
 8396  source ~/.zshrc
 8397  dev
 8398  exit
 8399  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 8400  git status
 8401  git diff internal/services/ai/prompt.md
 8402  vim internal/services/ai/prompt.md
 8403  git status
 8404  git add aims api docs internal models pkg 
 8405  git status
 8406  git add portal
 8407  git status
 8408  git commit -m "record runbook is stored" -s
 8409  git reset --soft HEAD~1
 8410  git status
 8411  rm -rf aims/ui/src/ai-support-engineer/a.json
 8412  git status
 8413  git add   aims/ui/src/ai-support-engineer/a.json
 8414  git commit -m "record runbook is stored" -s
 8415  git push
 8416  dev
 8417  cat ~/.zshrc
 8418  git status
 8419  git diff
 8420  ak
 8421  cd akuity-platform
 8422  ls
 8423  claude
 8424  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 8425  cd ..
 8426  cd akuity-platform
 8427  ls
 8428  cd portal/ui
 8429  pnpm install .
 8430  cd ../..
 8431  cd aims/ui
 8432  pnpm install .
 8433  cd ..
 8434  cd ../kubevision-scripts
 8435  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 8436  clear
 8437  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 8438  git status
 8439  rm -rf  internal/services/ai/test.json
 8440  git add aims api docs internal models pkg portal
 8441  git status
 8442  git commit -m "update set runbook stored logic" -s
 8443  git push
 8444  k9s
 8445  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 8446  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && ./refresh-akp.sh
 8447  unset http_proxy\nunset https_proxy\nunset HTTP_PROXY\nunset HTTPS_PROXY
 8448  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && ./refresh-akp.sh
 8449  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 8450  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && ./refresh-akp.sh
 8451  git status
 8452  git checkout internal/services/ai/service.go
 8453  git status
 8454  clear
 8455  cd ../kubevision-scripts
 8456  cd /Users/<USER>/MyPro/akuity/kubevision-scripts &&  ./refresh-akp.sh
 8457  echo 'Terminal capability test'
 8458  git status
 8459  cd /Users/<USER>/MyPro/akuity/kubevision-scripts &&  ./refresh-akp.sh
 8460  git status
 8461  git diff internal/services/ai/prompt.md
 8462  git checkout internal
 8463  git status
 8464  cd /Users/<USER>/MyPro/akuity/kubevision-scripts &&  ./refresh-akp.sh
 8465  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 8466  cd /Users/<USER>/MyPro/akuity/kubevision-scripts &&  ./refresh-akp.sh
 8467  git status
 8468  git diff internal/services/ai/service.go
 8469  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 8470  rm -rf   internal/services/ai/a.json  internal/services/ai/test.json
 8471  git status
 8472  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 8473  tiger
 8474  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 8475  git satus
 8476  echo 'Terminal capability test'
 8477  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 8478  export HTTPS_PROXY=http://************:7890\nexport HTTP_PROXY=http://************:7890
 8479  ping doker.io
 8480  ping docker.io
 8481  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 8482  git status
 8483  rm -rf    internal/services/ai/a.json
 8484  git status
 8485  git diff internal/services/ai/service.go
 8486  git status
 8487  git add amis internal portal
 8488  git status
 8489  git add aims internal portal
 8490  git status
 8491  git commit -m "modify runbookview layout" -s
 8492  git push
 8493  clear
 8494  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && ./refresh-akp.sh
 8495  unset http_proxy\nunset https_proxy\nunset HTTP_PROXY\nunset HTTPS_PROXY
 8496  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && ./refresh-akp.sh
 8497  git status
 8498  git add internal
 8499  git status
 8500  git diff
 8501  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 8502  unset http_proxy\nunset https_proxy\nunset HTTP_PROXY\nunset HTTPS_PROXY
 8503  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 8504  git status
 8505  git diff
 8506  git add internal portal
 8507  git status
 8508  git commit -m "update runbook viewer" -s
 8509  git push
 8510  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 8511  ping registry-1.docker.io
 8512  ping google.com
 8513  ping registry-1.docker.io
 8514  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 8515  git status
 8516  git diff
 8517  git status
 8518  git diff portal
 8519  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 8520  cd portal/ui
 8521  ARGOCD_SERVER=https://q5urhdgegkwuxhy5.cd.portal-server.akuity-platform EXTENSIONS_SERVER_URL=$ARGOCD_SERVER npm run dev:extensions
 8522  ls
 8523  git status
 8524  git add portal
 8525  git status
 8526  git commit -m "update runbook layout" -s
 8527  git push 
 8528  clear
 8529  git status
 8530  make generate-in-container
 8531  docker pull mirror.gcr.io/golang:1.23.2-bookworm
 8532  unset http_proxy\nunset https_proxy\nunset HTTP_PROXY\nunset HTTPS_PROXY
 8533  docker pull mirror.gcr.io/golang:1.23.2-bookworm
 8534  make generate-in-container
 8535  docker pull bufbuild/buf:1.41.0
 8536  docker pull docker.io/bufbuild/buf:1.41.0
 8537  make generate-in-container
 8538  git status
 8539  make generate-in-container
 8540  git status
 8541  git diff aims/ui/package.json
 8542  git diff aims/ui/pnpm-lock.yaml
 8543  git diff aims/ui/src/ai-support-engineer/message-bubble.less
 8544  git diff aims/ui/src/ai-support-engineer/message-bubble.tsx
 8545  git status
 8546  git add aims portal
 8547  git status
 8548  git add portal
 8549  git status
 8550  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 8551  git status
 8552  git commit -m "remove unsed codes" -s
 8553  git push
 8554  git fetch origin
 8555  git merge origin/main
 8556  make generate-in-container
 8557  git status
 8558  git add pkg
 8559  git status
 8560  ls internal/services/ai/a.json
 8561  rm -rf internal/services/ai/a.json
 8562  git status
 8563  git add internal
 8564  git status
 8565  git commit
 8566  git status
 8567  git push
 8568  cd /Users/<USER>/MyPro/akuity/kubevision-scripts &&  ./refresh-akp.sh
 8569  k9s
 8570  cd /Users/<USER>/MyPro/akuity/kubevision-scripts &&  ./refresh-akp.sh
 8571  make generate-in-container
 8572  cd /Users/<USER>/MyPro/akuity/kubevision-scripts &&  ./refresh-akp.sh
 8573  cd portal/ui
 8574  pnpm run lint
 8575  pnpm run lint --fix
 8576  git status
 8577  cd ..
 8578  cd aims/ui
 8579  pnpm run lint --fix
 8580  git status
 8581  cd ..
 8582  git status
 8583  git diff aims
 8584  git diff internal/
 8585  git add aims internal models portal
 8586  git status
 8587  git commit -m "fix linter error" -s
 8588  git push
 8589  clear
 8590  PUSH_LATEST=true IMAGE_REPO=us-docker.pkg.dev/akuity/akp/akuity-platform TAG=latest make image
 8591  ping google.com
 8592  PUSH_LATEST=true IMAGE_REPO=us-docker.pkg.dev/akuity/akp/akuity-platform TAG=latest make image
 8593  cat ~/.zshrc
 8594  dev
 8595  ping *************
 8596  ssh root@*************
 8597  ping *************
 8598  PUSH_LATEST=true IMAGE_REPO=us-docker.pkg.dev/akuity/akp/akuity-platform TAG=latest make image
 8599  ping *************
 8600  ssh root@*************
 8601  ping *************
 8602  cd /Users/<USER>/MyPro/akuity/kubevision-scripts &&  ./refresh-akp.sh
 8603  k9s
 8604  ping *************
 8605  telnet ************* 22
 8606  ping google.com
 8607  git status
 8608  git diff internal/services/ai/service.go
 8609  git checkout  internal/services/ai/service.go
 8610  git status
 8611  git diff internal/services/ai/functions/incident.go
 8612  git diff models/models/ai_conversation_ext.go
 8613  git checkout models/models/ai_conversation_ext.go
 8614  git status
 8615  git diff internal/services/ai/prompt.md
 8616  git status
 8617  git diff internal/services/ai/functions/incident.go
 8618  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 8619  ./refresh-akp.sh
 8620  docker pull golang:alpine
 8621  ./refresh-akp.sh
 8622  git status
 8623  git diff
 8624  git status
 8625  git add internal models
 8626  git status
 8627  git commit -m "modify prompt" -s
 8628  git push
 8629  git status
 8630  git add models
 8631  git status
 8632  git commit -m "modify AIResponse struct" -s
 8633  git push 
 8634  ./refresh-akp.sh
 8635  clear
 8636  ls
 8637  git status
 8638  clear
 8639  ./refresh-akp.sh
 8640  git status
 8641  git diff
 8642  git add models
 8643  git status
 8644  git commit -m "modify AIResponse struct" -s
 8645  git push
 8646  clear
 8647  git status
 8648  ./refresh-akp.sh
 8649  clear
 8650  ls
 8651  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 8652  git status
 8653  git diff
 8654  git diff internal/services/ai/functions/incident.go
 8655  git checkout internal/services/ai/functions/incident.go
 8656  git status
 8657  git add models portal 
 8658  git status
 8659  git commit -m "modify frontend layout" -s
 8660  git push 
 8661  git status
 8662  tiger
 8663  scp  admin@************:/etc/mihomo/config.yaml ./
 8664  open .
 8665  scp ls
 8666  ls
 8667  scp config.yaml admin@************:/etc/mihomo/
 8668  scp config.yaml admin@************:~
 8669  ping http://************/
 8670  ping ************
 8671  ssh root@************
 8672  ssh admin@************
 8673  tiger
 8674  ping google.com
 8675  ping baidu.com
 8676  docker pull golang
 8677  ls
 8678  cd /etc/mihomo
 8679  tiger
 8680  ping baidu.com
 8681  ping google.com
 8682  clear
 8683  ls
 8684  rm -rf master.zip
 8685  ls
 8686  ak
 8687  cd akuity-platform
 8688  ls
 8689  cd ../
 8690  ls
 8691  cd kubevision-scripts
 8692  ls
 8693  sh ./refresh-akp.sh
 8694  export HTTPS_PROXY=http://************:7890\nexport HTTP_PROXY=http://************:7890
 8695  sh ./refresh-akp.sh
 8696  unset http_proxy\nunset https_proxy\nunset HTTP_PROXY\nunset HTTPS_PROXY
 8697  sh ./refresh-akp.sh
 8698  dev
 8699  cat ~/.zshrc
 8700  ssh root@*************
 8701  ping *************
 8702  ssh root@*************
 8703  unset http_proxy\nunset https_proxy\nunset HTTP_PROXY\nunset HTTPS_PROXY
 8704  ssh root@*************
 8705  unset http_proxy\nunset https_proxy\nunset HTTP_PROXY\nunset HTTPS_PROXY
 8706  ssh root@*************
 8707  clear
 8708  sh refresh-akp.sh
 8709  cd ../kubevision-scripts
 8710  sh refresh-akp.sh
 8711  ak
 8712  cd ../SlackUp
 8713  ./SlackUp
 8714  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 8715  k get po -A
 8716  kx
 8717  k get po -A
 8718  k delete po -n akuity-agent --all
 8719  k get po -A
 8720  k describe po -n akuity-agent akuity-agent-68d8b6b896-hgnr8
 8721  k get po -A
 8722  TOKEN="eyJhbGciOiJSUzI1NiIsImtpZCI6ImZmODZlM2FhOWZhZmU1ZDkxMzkyMTgyZGZlN2ZhYmZiYjU3NzkxNWQifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.J5OkHxQJ01cytOIFJOls93PyN1RSgTYRDS11uFovoAvGDCvE_myM0gr1_H-b9jsFPyW6218luW5gMREAy4m5FKVkDJpdeEgHo2t6HnoTh47IMhiLI-Tl1Jgk_0NzLRx9EN3penMk_-Ar6JlpyrEl8IyjZcucf3B7M3DziTK5A7ulAMsdYiA7iiOdR5NJJgQKZcAloa1DeiT2Rs-md5EPIbiBTTfs9AHXwmabNIByFx1yZkb-5bJrabgoPmyFl7bTqMj2MA26QqeWhkO8HUki0ih35vuBMWntAvoMU1bY3x7BS6dVECNCtM_pRlJCBn52GBfc3q_xTzcdwRPCUQB0ww" && curl -s -H "Authorization: Bearer $TOKEN" "https://portal-server.akuity-platform/api/v1/orgs/c7izp6dcuvuhepus/argocd/instances/q5urhdgegkwuxhy5/clusters/wnzwpximjhbun66c/manifests?skipNamespace=true" | kubectl delete -f - && kubectl delete ns akp-demo
 8723  TOKEN="eyJhbGciOiJSUzI1NiIsImtpZCI6ImZmODZlM2FhOWZhZmU1ZDkxMzkyMTgyZGZlN2ZhYmZiYjU3NzkxNWQifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.J5OkHxQJ01cytOIFJOls93PyN1RSgTYRDS11uFovoAvGDCvE_myM0gr1_H-b9jsFPyW6218luW5gMREAy4m5FKVkDJpdeEgHo2t6HnoTh47IMhiLI-Tl1Jgk_0NzLRx9EN3penMk_-Ar6JlpyrEl8IyjZcucf3B7M3DziTK5A7ulAMsdYiA7iiOdR5NJJgQKZcAloa1DeiT2Rs-md5EPIbiBTTfs9AHXwmabNIByFx1yZkb-5bJrabgoPmyFl7bTqMj2MA26QqeWhkO8HUki0ih35vuBMWntAvoMU1bY3x7BS6dVECNCtM_pRlJCBn52GBfc3q_xTzcdwRPCUQB0ww" && curl -s -H "Authorization: Bearer $TOKEN" "https://portal-server.akuity-platform/api/v1/orgs/c7izp6dcuvuhepus/argocd/instances/q5urhdgegkwuxhy5/clusters/wnzwpximjhbun66c/manifests?skipNamespace=true" | kubectl delete -f -k - && kubectl delete ns akp-demo
 8724  TOKEN="eyJhbGciOiJSUzI1NiIsImtpZCI6ImZmODZlM2FhOWZhZmU1ZDkxMzkyMTgyZGZlN2ZhYmZiYjU3NzkxNWQifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.J5OkHxQJ01cytOIFJOls93PyN1RSgTYRDS11uFovoAvGDCvE_myM0gr1_H-b9jsFPyW6218luW5gMREAy4m5FKVkDJpdeEgHo2t6HnoTh47IMhiLI-Tl1Jgk_0NzLRx9EN3penMk_-Ar6JlpyrEl8IyjZcucf3B7M3DziTK5A7ulAMsdYiA7iiOdR5NJJgQKZcAloa1DeiT2Rs-md5EPIbiBTTfs9AHXwmabNIByFx1yZkb-5bJrabgoPmyFl7bTqMj2MA26QqeWhkO8HUki0ih35vuBMWntAvoMU1bY3x7BS6dVECNCtM_pRlJCBn52GBfc3q_xTzcdwRPCUQB0ww" && curl -s -H "Authorization: Bearer $TOKEN" "https://portal-server.akuity-platform/api/v1/orgs/c7izp6dcuvuhepus/argocd/instances/q5urhdgegkwuxhy5/clusters/wnzwpximjhbun66c/manifests?skipNamespace=true" | kubectl delete -f -k && kubectl delete ns akp-demo
 8725  k get po -A
 8726  TOKEN="eyJhbGciOiJSUzI1NiIsImtpZCI6ImZmODZlM2FhOWZhZmU1ZDkxMzkyMTgyZGZlN2ZhYmZiYjU3NzkxNWQifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.J5OkHxQJ01cytOIFJOls93PyN1RSgTYRDS11uFovoAvGDCvE_myM0gr1_H-b9jsFPyW6218luW5gMREAy4m5FKVkDJpdeEgHo2t6HnoTh47IMhiLI-Tl1Jgk_0NzLRx9EN3penMk_-Ar6JlpyrEl8IyjZcucf3B7M3DziTK5A7ulAMsdYiA7iiOdR5NJJgQKZcAloa1DeiT2Rs-md5EPIbiBTTfs9AHXwmabNIByFx1yZkb-5bJrabgoPmyFl7bTqMj2MA26QqeWhkO8HUki0ih35vuBMWntAvoMU1bY3x7BS6dVECNCtM_pRlJCBn52GBfc3q_xTzcdwRPCUQB0ww" && curl -s -H "Authorization: Bearer $TOKEN" "https://portal-server.akuity-platform/api/v1/orgs/c7izp6dcuvuhepus/argocd/instances/q5urhdgegkwuxhy5/clusters/wnzwpximjhbun66c/manifests?skipNamespace=true" | kubectl delete -f - && kubectl delete ns akp-demo
 8727  kx
 8728  k get po -A
 8729  kx
 8730  k get po -A
 8731  k delete ns akuity-agent
 8732  k get po -A
 8733  kx
 8734  k get po -A
 8735  k get po -A -w
 8736  k get po -A
 8737  k describe po -n argocd-4m9h4tc7b5bnjk8a argocd-application-controller-5c5d9c7496-zblql
 8738  k get po -A
 8739  k describe po -n argocd-4m9h4tc7b5bnjk8a argocd-application-controller-5c5d9c7496-zblql
 8740  k3d image load quay.io/akuity/argocd:v3.0.11-ak.60 -c orbstack
 8741  k get po -A
 8742  kx
 8743  k3d image load quay.io/akuity/argocd:v3.0.11-ak.60 -c orbstack
 8744  k3d cluster list
 8745  k3d image load quay.io/akuity/argocd:v3.0.11-ak.60 -c akuity-customer
 8746  docker pull akuity/argocd:v3.0.11-ak.60
 8747  docker pull quay.io/akuity/argocd:v3.0.11-ak.60
 8748  clear
 8749  k3d cluster list
 8750  k get po -A
 8751  kx
 8752  TOKEN="eyJhbGciOiJSUzI1NiIsImtpZCI6ImZmODZlM2FhOWZhZmU1ZDkxMzkyMTgyZGZlN2ZhYmZiYjU3NzkxNWQifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.J5OkHxQJ01cytOIFJOls93PyN1RSgTYRDS11uFovoAvGDCvE_myM0gr1_H-b9jsFPyW6218luW5gMREAy4m5FKVkDJpdeEgHo2t6HnoTh47IMhiLI-Tl1Jgk_0NzLRx9EN3penMk_-Ar6JlpyrEl8IyjZcucf3B7M3DziTK5A7ulAMsdYiA7iiOdR5NJJgQKZcAloa1DeiT2Rs-md5EPIbiBTTfs9AHXwmabNIByFx1yZkb-5bJrabgoPmyFl7bTqMj2MA26QqeWhkO8HUki0ih35vuBMWntAvoMU1bY3x7BS6dVECNCtM_pRlJCBn52GBfc3q_xTzcdwRPCUQB0ww" && curl -s -H "Authorization: Bearer $TOKEN" "https://portal-server.akuity-platform/api/v1/orgs/c7izp6dcuvuhepus/argocd/instances/4m9h4tc7b5bnjk8a/clusters/uf181r6kcah2vtzg/manifests" -k| kubectl apply -f -
 8753  k get po -A
 8754  k get po -A -w
 8755  k get po -A
 8756  k get po -A -w
 8757  k get po -n akuity-agent argocd-notifications-controller-69dc7fc645-7q95z
 8758  k describe po -n akuity-agent argocd-notifications-controller-69dc7fc645-7q95z
 8759  k get po -A -w
 8760  k get po -A 
 8761  k describe po -n akuity-agent argocd-repo-server-6d4d9875f8-r5jjb
 8762  k get po -A 
 8763  k describe po -n akuity-agent argocd-repo-server-6d4d9875f8-r5jjb
 8764  k get po -A 
 8765  k describe po -n akuity-agent argocd-notifications-controller-69dc7fc645-7q95z
 8766  k get po -A 
 8767  k get cm -A
 8768  k get po -A 
 8769  k describe po -n akuity-agent argocd-notifications-controller-69dc7fc645-7q95z
 8770  k get po -A 
 8771  k describe po -n akuity-agent argocd-repo-server-6d4d9875f8-
 8772  k describe po -n akuity-agent argocd-repo-server-6d4d9875f8
 8773  argocd-tls-certs-cm argocd-gpg-keys-cm
 8774  k get po -A 
 8775  k describe po -n akuity-agent argocd-repo-server-6d4d9875f8
 8776  k get po -A 
 8777  cd ../
 8778  cd akuity
 8779  cd kubevision-scripts
 8780  ls
 8781  ./stop-akp.sh
 8782  ENABLE_KARGO=false ./start-akp.sh
 8783  kx
 8784  k get po -A
 8785  k3d cluster -h
 8786  k3d cluster list
 8787  k3d cluster delete akuity-customer
 8788  k3d cluster list
 8789  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 8790  git status
 8791  git diff
 8792  git add internal
 8793  git status 
 8794  git commit -m "retry when having existing runbook name" -s
 8795  git status
 8796  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 8797  k9s
 8798  sh refresh-akp.sh
 8799  git status
 8800  git diff
 8801  git add internal
 8802  git status
 8803  git commit -m "change the grpc code to expose failure reasons" -s
 8804  git push
 8805  git pull --rebase
 8806  git push
 8807  git status
 8808  git stash 
 8809  ls
 8810  ./stop-akp.sh; 
 8811  ENABLE_KARGO=false ./start-akp.sh
 8812  ping google.com
 8813  docker pull golang
 8814  ping google.com
 8815  tiger
 8816  curl https://github.com
 8817  ping github.com
 8818  cd ak
 8819  ak
 8820  cd akuity-platform
 8821  git status
 8822  git pull
 8823  env |grep proxy
 8824  ak
 8825  cd akuity-platform
 8826  git pull
 8827  ping google.com
 8828  docker pull golang
 8829  sudo tcpdump -i any host github.com or host docker.io\n
 8830  docker run busybox wget https://github.com
 8831  tiger
 8832  ifconfig |grep inet
 8833  tiger
 8834  ping google.com
 8835  tiger
 8836  ssh -D 7893 admin@************ -N
 8837  mkdir pf
 8838  cd pf
 8839  vim proxy.conf
 8840  ifconfig
 8841  sudo pfctl -f ~/proxy.conf
 8842  ls
 8843  sudo pfctl -f ./proxy.conf
 8844  sudo pfctl -e
 8845  sudo pfctl -s rdr
 8846  sudo pfctl -s nat
 8847  exit
 8848  ping google.com
 8849  docker pull golang
 8850  tiger
 8851  ping ************
 8852  tiger
 8853  brew install wireguard-tools
 8854  /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
 8855  brew install wireguard-tools wireguard-go
 8856  vim /etc/wireguard/wg0.conf
 8857  sudo vim /etc/wireguard/wg0.conf
 8858  sudo mkdir -p /etc/wireguard
 8859  sudo vim /etc/wireguard/wg0.conf
 8860  sudo wg-quick up /etc/wireguard/wg0.conf
 8861  sudo vim /etc/wireguard/wg0.conf
 8862  sudo wg-quick up /etc/wireguard/wg0.conf
 8863  ping google.com
 8864  docker pull golang
 8865  clear
 8866  ls
 8867  ping google.com
 8868  ping github.com
 8869  clear
 8870  ls
 8871  ping google.com
 8872  ls
 8873  ak
 8874  ls
 8875  cd akuity-platform
 8876  ls
 8877  cd ../
 8878  ls
 8879  cd kubevision-scripts
 8880  ls
 8881  sh stop-akp.sh
 8882  ENABLE_KARGO=false ./start-akp.sh
 8883  unset http_proxy\nunset https_proxy\nunset HTTP_PROXY\nunset HTTPS_PROXY
 8884  ENABLE_KARGO=false ./start-akp.sh
 8885  docker pull  docker.io/library/golang:1.24.4
 8886  ENABLE_KARGO=false ./start-akp.sh
 8887  tiger
 8888  k9s
 8889  tiger
 8890  ENABLE_KARGO=false ./start-akp.sh
 8891  k get po -A
 8892  k describe po -n argocd-nvus1lsrti3lllit argocd-application-controller-dd49556d4-w4xxk
 8893  k get po -A
 8894  k describe po -n argocd-nvus1lsrti3lllit argocd-server-799ccd857c-2cqlz
 8895  k get po -A
 8896  k get po -A -w
 8897  git status
 8898  k get po -A
 8899  ls
 8900  rm -rf pf
 8901  ls
 8902  cp /etc/wireguard/wg0.conf ~/Desktop
 8903  sudo wg-quick down /etc/wireguard/wg0.conf
 8904  dev
 8905  cat ~/.zshrc
 8906  dev
 8907  tiger
 8908  dev
 8909  9. RUNBOOK NAMING: Be specific with names using pattern `[resource]-[specific-issue]-[action]` (e.g., `pod-oom-fix`, `service-selector-mismatch`). If `store-runbook` returns name conflict error, automatically generate a more specific name and retry.
 8910  echo 'Terminal capability test'
 8911  sh refresh-akp.sh
 8912  k get po -n akuity-platform
 8913  kx
 8914  k get po -n akuity-platform
 8915  k logs -n akuity-platform platform-controller-796b5ddf5c-ddrpt
 8916  tiger
 8917  sh refresh-akp.sh
 8918  cd /etc
 8919  ls
 8920  cd wireguard
 8921  ks
 8922  ls
 8923  open .
 8924  ping google.com
 8925  cd client
 8926  sh build.sh
 8927  ls
 8928  cd TunnelKext
 8929  ls
 8930  cd ..
 8931  ls
 8932  sh build.sh
 8933  ls
 8934  cd TunnelKext
 8935  ls
 8936  cd ..
 8937  ls
 8938  sh control.sh status
 8939  sh control.sh start
 8940  tiger
 8941  sudo visudo
 8942  pwd
 8943  tiger
 8944  pip install pyyaml
 8945  pip3 install pyyaml
 8946  telnet *************** 23550
 8947  ssh admin@***************:23550
 8948  ssh admin@***************
 8949  echo 'Terminal capability test'
 8950  tiger
 8951  ping google.com
 8952  ping github.com
 8953  ping youtube.com
 8954  ping google.com
 8955  ping youtube.com
 8956  ping baidu.com
 8957  ping docker.com
 8958  unset http_proxy\nunset https_proxy\nunset HTTP_PROXY\nunset HTTPS_PROXY
 8959  ping docker.com
 8960  tiger
 8961  ping google.com
 8962  pwd
 8963  tiger
 8964  curl -X POST http://localhost:5000/switch -H "Content-Type: application/json" -d '{"mode": "yuan"}'\n
 8965  curl -X POST http://localhost:5000/switch -H "Content-Type: application/json" -d '{"mode": "gong"}'\n
 8966  curl -X POST http://localhost:5000/switch \\n  -H "Content-Type: application/json" \\n  -d '{"mode": "yuan"}'
 8967  brew install iperf3
 8968  cat ~/.zshrc|grep dev
 8969  iperf3 -c *************
 8970  iperf3 -s -p 52015
 8971  iperf3 -c ************* -p 52015
 8972  scp ~/Desktop/1.zip root@*************:/
 8973  dev
 8974  ping *************
 8975  dev
 8976  scp ~/Desktop/1.zip root@*************:/
 8977  ls
 8978  traceroute *************
 8979  ssh -p 23547 root@***************
 8980  cd ../kubevision-scripts
 8981  sh refresh-akp.sh
 8982  clear
 8983  k9s
 8984  sh refresh-akp.sh
 8985  k get job -n akuity-platform
 8986  k get cronjobs.batch -A
 8987  ls
 8988  git status
 8989  cp  internal/services/ai/functions/incident.go  internal/services/ai/functions/incident.go.bak
 8990  git checkout  internal/services/ai/functions/incident.go
 8991  sh refresh-akp.sh
 8992  git status
 8993  git log
 8994  sh refresh-akp.sh
 8995  git status
 8996  git diff internal/services/ai/prompt.md
 8997  git status
 8998  rm -rf  internal/services/ai/functions/incident.go.bak
 8999  git status
 9000  git add internal
 9001  git status
 9002  git commit --amend
 9003  git log
 9004  git status
 9005  git log
 9006  git pull --rebase
 9007  git status
 9008  git fetch origin
 9009  git pull --rebase
 9010  git push
 9011  git pull
 9012  git status
 9013  git log
 9014  clear
 9015  echo 'Terminal capability test'
 9016  sh refresh-akp.sh
 9017  git status
 9018  git diff 
 9019  git status
 9020  git add internal
 9021  git status
 9022  git commit -m "modify the name confict retry prompt" -s
 9023  git push 
 9024  git fetch origin
 9025  git merge origin/main
 9026  git push
 9027  git status
 9028  git add internal
 9029  git status
 9030  git add internal
 9031  git status
 9032  git commit -m "update prompt" -s
 9033  git push
 9034  git status
 9035  clear
 9036  git status
 9037  git checkout main
 9038  git pull
 9039  git log
 9040  git reset HEAD~1 --hard
 9041  git pull
 9042  git fetch origin
 9043  clear
 9044  sh refresh-akp.sh
 9045  git tatus
 9046  git status
 9047  sh refresh-akp.sh
 9048  clear
 9049  git status
 9050  git pull
 9051  sh refresh-akp.sh
 9052  git log
 9053  git pull
 9054  git log
 9055  git status
 9056  git diff
 9057  git checkout internal
 9058  git pull
 9059  sh refresh-akp.sh
 9060  clear
 9061  dev
 9062  exit
 9063  dev
 9064  clear
 9065  git status
 9066  sh refresh-akp.sh
 9067  ping baidu.com
 9068  echo 'Terminal capability test'
 9069  ping google.com
 9070  docker pull busybox
 9071  clear
 9072  docker images
 9073  docker image prune -a
 9074  docker images
 9075  clear
 9076  sh refresh-akp.sh
 9077  clear
 9078  git sttus
 9079  git status
 9080  git diff
 9081  git checkout internal
 9082  git status
 9083  clear
 9084  git status
 9085  git branch
 9086  git checkout store-runbook
 9087  git status
 9088  clear
 9089  git status
 9090  clear
 9091  sh refresh-akp.sh
 9092  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 9093  sh refresh-akp.sh
 9094  git status
 9095  git add internal 
 9096  git status
 9097  git commit -m "update prompt" -s
 9098  git push
 9099  sh refresh-akp.sh
 9100  git status
 9101  git add internal
 9102  git status
 9103  git commit -m "update prompt" -s
 9104  git push
 9105  sh refresh-akp.sh
 9106  git status
 9107  git diff
 9108  git status
 9109  git add internal
 9110  git status
 9111  git commit -m "update prompt" -s
 9112  git push
 9113  git pull --rebase
 9114  git status
 9115  git push
 9116  ping google.com
 9117  sh refresh-akp.sh
 9118  ping http://***************/
 9119  clear
 9120  ls
 9121  cd models
 9122  make generate-in-container
 9123  make generate
 9124  ping google.como
 9125  make generate
 9126  docker pull busybox
 9127  make generate
 9128  docker pull busybox
 9129  docker pull golang
 9130  make generate
 9131  cd models
 9132  ls
 9133  make generate
 9134  ak
 9135  cd akuity-platform
 9136  ls
 9137  cd models
 9138  make generate
 9139  ping google.com
 9140  make generate
 9141  docker pull akuity/liquibase:4.29
 9142  docker pull quay.io/akuity/liquibase:4.29
 9143  make generate
 9144  docker pull quay.io/akuity/liquibase:4.29
 9145  ak
 9146  cd akp-demo
 9147  cd ..
 9148  cd akuity-platform
 9149  cd models
 9150  make generate
 9151  git status
 9152  make generate
 9153  git status
 9154  make generate
 9155  git status
 9156  cd ..
 9157  cd ../kubevision-scripts
 9158  ls
 9159  sh ./stop-akp.sh
 9160  ENABLE_KARGO=false ./start-akp.sh
 9161  k get po -A
 9162  k get po -A -w
 9163  echo 'Terminal capability test'
 9164  k get po -A
 9165  sh refresh-akp.sh
 9166  k9s
 9167  sh refresh-akp.sh
 9168  git satus
 9169  git status
 9170  git log
 9171  git status
 9172  git stash
 9173  git status
 9174  git stash
 9175  git status
 9176  git pull
 9177  git status
 9178  git diff
 9179  git status
 9180  git diff
 9181  git status
 9182  git add aims internal
 9183  git status
 9184  git commit -m "update prompt" -s
 9185  git push
 9186  clear
 9187  git stash list
 9188  git status
 9189  git add internal
 9190  git status
 9191  git commit -m "update prompt" -s
 9192  git push
 9193  git stash pop stash@{0}
 9194  git status
 9195  sh refresh-akp.sh
 9196  clear
 9197  ls
 9198  cat diff.log
 9199  ls
 9200  rm diff.log
 9201  ls
 9202  git status
 9203  clear
 9204  ls
 9205  clear
 9206  ls
 9207  git status
 9208  git add internal models
 9209  git status
 9210  git commit -m "use runbook" -s
 9211  git status
 9212  git checkout main
 9213  git pull
 9214  git status
 9215  git log
 9216  clear
 9217  git tatus
 9218  git status
 9219  git checkut -b use-runbook
 9220  git checkout -b use-runbook
 9221  git checkout -b test-runbook
 9222  cd models
 9223  make generate
 9224  cd ..
 9225  vim internal/services/ai/functions/runbooks.go
 9226  echo 'Terminal capability test'
 9227  git status
 9228  cd ..
 9229  claude
 9230  clear
 9231  ls
 9232  git status
 9233  git diff
 9234  git status
 9235  git diff  internal/services/ai/functions/controller.go
 9236  git diff internal/services/ai/service.go
 9237  git status
 9238  git checkout  internal/services/ai/service.go
 9239  clause
 9240  claude
 9241  git satus
 9242  git status
 9243  git diff internal/services/ai/functions/runbooks.go
 9244  clear
 9245  git status
 9246  git diff
 9247  git add api internal models
 9248  git status
 9249  git stash
 9250  git status
 9251  git log
 9252  git tatus
 9253  git status
 9254  git add api
 9255  git status
 9256  make generate-in-container
 9257  git status
 9258  git diff
 9259  git status
 9260  git add *
 9261  git status
 9262  git commit -m "add api definition files" -s
 9263  git push
 9264  clear
 9265  ls
 9266  cd models
 9267  make generate
 9268  git status
 9269  git add liquibase models sql
 9270  git status
 9271  git commit -m "add runbooks column in ai_conversation table" -s
 9272  git push
 9273  clear
 9274  git stash list
 9275  git sttaus
 9276  git status
 9277  cd ..
 9278  git status
 9279  git sttaus
 9280  git status
 9281  git add internal
 9282  git status
 9283  git commit -m "add runbook function call" -s
 9284  git fetch origin
 9285  git satus
 9286  git status
 9287  ls
 9288  ak
 9289  cd ak
 9290  cd akuity-platform
 9291  ls
 9292  git status
 9293  git checkout main
 9294  git stauts
 9295  git status
 9296  git pull
 9297  git checkout -b store-runbook-fix
 9298  git status
 9299  sh refresh-akp.sh
 9300  git status
 9301  git diff
 9302  git checkout internal
 9303  git status
 9304  sh refresh-akp.sh
 9305  git status
 9306  sh refresh-akp.sh
 9307  git status
 9308  git diff
 9309  sh refresh-akp.sh
 9310  git status
 9311  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 9312  git status
 9313  git add internal
 9314  git status
 9315  git commit -m "update store runbook prompt" -s
 9316  git push
 9317  git push --set-upstream origin store-runbook-fix
 9318  tiger
 9319  ak
 9320  cd ak
 9321  cd akuity-platform
 9322  ls
 9323  git checkout main
 9324  git status
 9325  git pull
 9326  cd ../
 9327  cd kubevision-scripts
 9328  sh refresh-akp.sh
 9329  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 9330  kx
 9331  k get po -A
 9332  TOKEN="eyJhbGciOiJSUzI1NiIsImtpZCI6IjA3ZDBlMDg4NWZkMTQ0YTlhN2E5MWZiNTM1NjNkZDE0ZjMzMjliMjUifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.c0cnv-EaIJsb6GWKsOa8hlQDGkBJRlox6FOuVT53mNoIS_r4FBt6Yvp_tvTRhheNoJ9sQFmipgnwB3E35bpJFVZCw6GNDp7rXQMcpMsKbhTCMXGTR4S_Mt0W_D5CUpZItzvv5u-I1J7B0ahJ3bubLc7OBWKHbXnTCzPZOcDJ95Kniie8Cnyyq-eyBvLdV1d-nYRDnN_UvceJdh0JAIXWYqC9lkrhpX2DmFM0o8dOMY0IFPW7qU54u1Qj6M8NVE8SD1rlpwuYqvA_hgNRyBmpUQft1VHxngYX8BURerbk-Pb2PuhAcLgu5dgvPPvvBZ6_RJvqtV2mS2q5LKIxQkTMDQ" && curl -s -H "Authorization: Bearer $TOKEN" "https://portal-server.akuity-platform/api/v1/orgs/sphl8ck4z9ntiwxq/argocd/instances/x9decdz0ysnh48iu/clusters/rd9nkodeob72cs51/manifests" -k | kubectl apply -f -
 9333  k get po -A
 9334  k get po -A -w
 9335  k get po -A
 9336  k delete ns akuity
 9337  vim /etc/hosts
 9338  sudo vim /etc/hosts
 9339  sh refresh-akp.sh
 9340  git status
 9341  git checkout internal
 9342  git status
 9343  git fetch origin
 9344  sh refresh-akp.sh
 9345  git status
 9346  tiger
 9347  ak
 9348  cd ../SlackUp
 9349  ./SlackUp
 9350  sh refresh-akp.sh
 9351  git pull
 9352  sh refresh-akp.sh
 9353  git pull
 9354  git status
 9355  sh refresh-akp.sh
 9356  git pull
 9357  sh refresh-akp.sh
 9358  git pull
 9359  git status
 9360  git diff
 9361  git diff internal/services/ai/functions/prompt.md
 9362  git checkout  internal/services/ai/functions/prompt.md
 9363  git diff internal
 9364  git status
 9365  git add internal
 9366  git status
 9367  git rm  internal/services/ai/a.json
 9368  git status
 9369  rm  internal/services/ai/a.json
 9370  git status
 9371  git add internal
 9372  git status
 9373  git commit -m "update prompt" -s
 9374  git pull --rebase
 9375  sh refresh-akp.sh
 9376  git push
 9377  sh refresh-akp.sh
 9378  ping google.com
 9379  sh refresh-akp.sh
 9380  tiger
 9381  sh refresh-akp.sh
 9382  ping google.com
 9383  sh refresh-akp.sh
 9384  ping github.com
 9385  sh refresh-akp.sh
 9386  git pull 
 9387  sh refresh-akp.sh
 9388  clear
 9389  ls
 9390  clear
 9391  git checkout main
 9392  git branch -D improve-runbook-prompt
 9393  git status
 9394  git pull
 9395  echo 'Terminal capability test'
 9396  git checkout -b ai-mesasge-refactor
 9397  git status
 9398  git diff internal/services/ai/service.go
 9399  git status
 9400  ping google.com
 9401  sh refresh-akp.sh
 9402  git status
 9403  git diff   internal/services/ai/incident.go
 9404  sh refresh-akp.sh
 9405  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 9406  make generate-in-container
 9407  git status
 9408  git checkout portal/ui/src/feature/ai-support-engineer/components/message-bubble/message-bubble.tsx
 9409  git status
 9410  git checkout portal/ui/src/feature/ai-support-engineer/components/message-bubble/message-bubble.tsx
 9411  git status
 9412  git diff portal/ui/src/feature/ai-support-engineer/components/message-bubble/message-bubble.tsx
 9413  git status
 9414  git diff portal/ui/src/feature/ai-support-engineer/components/message-bubble/message-bubble.tsx
 9415  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 9416  k9s
 9417  sh refresh-akp.sh
 9418  clear
 9419  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 9420  sh refresh-akp.sh
 9421  git status
 9422  rm -rf     docs/additional-content-examples.md\n        docs/backend-runbook-logic-update.md\n        docs/final-refactor-summary.md\n        docs/frontend-fixes-summary.md
 9423  rm -rf docs/additional-content-examples.md
 9424  rm -rf docs/backend-runbook-logic-update.md
 9425  rm -rf         docs/final-refactor-summary.md  docs/frontend-fixes-summary.md
 9426  git status
 9427  rm -rf docs/additional-content-examples.md    docs/backend-runbook-logic-update.md         docs/final-refactor-summary.md    docs/frontend-fixes-summary.md         docs/prompt-updates-summary.md 
 9428  git status
 9429  echo 'Terminal capability test'
 9430  tiger
 9431  echo 'Terminal capability test'
 9432  clear
 9433  git status
 9434  cd ../kubevision-scripts
 9435  sh refresh-akp.sh
 9436  clear
 9437  git status
 9438  git diff
 9439  git diff  internal/services/ai/functions/prompt.md
 9440  git status
 9441  cd ..
 9442  ls
 9443  cd akuity-platform
 9444  git status
 9445  git diff   internal/services/ai/functions/prompt.md
 9446  rm -rf   docs/additional-content-examples.md         docs/backend-runbook-logic-update.md        docs/final-refactor-summary.md         docs/frontend-fixes-summary.md         docs/prompt-updates-summary.md
 9447  git status
 9448  git add aims api docs internal models pkg portal internal
 9449  git status
 9450  git restore --staged >
 9451  git restore --staged *
 9452  git status
 9453  git commit -m "tmp" -s
 9454  git status
 9455  git log
 9456  git reset HEAD~1 --hard
 9457  git status
 9458  clear
 9459  git status
 9460  make generate
 9461  make generate-in-container
 9462  git status
 9463  git diff api/proto/organization/v1/organization.proto
 9464  git status
 9465  git add aims api docs pkg portal
 9466  git status
 9467  git commit -m "modify AIMessage proto" -s
 9468  git push
 9469  git push --set-upstream origin ai-mesasge-refactor
 9470  sh refresh-akp.sh
 9471  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 9472  sh refresh-akp.sh
 9473  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 9474  git satus
 9475  git status
 9476  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 9477  sh refresh-akp.sh
 9478  git status
 9479  git diff
 9480  git checkout internal/services/ai/prompt.md
 9481  git status
 9482  git diff internal/services/ai/functions/prompt.md
 9483  git status
 9484  git diff *.md
 9485  git diff internal/services/ai/functions/prompt.md
 9486  git status
 9487  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 9488  git add internal portal
 9489  git status
 9490  git commit -m "modify logic for runbook send to frontend" -s
 9491  git push
 9492  git status
 9493  k9s
 9494  make format
 9495  git statsu
 9496  git status
 9497  make generate-in-container
 9498  git sttaus
 9499  git status
 9500  git diff
 9501  git add api pkg
 9502  git status
 9503  git commit -m "fix linter error" -s
 9504  git push
 9505  git pull --rebase
 9506  git push
 9507  git status
 9508  git satus
 9509  git status
 9510  git add internal
 9511  git status
 9512  git commit -m "update" -s
 9513  git push
 9514  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 9515  echo 'Terminal capability test'
 9516  git status
 9517  git diff 
 9518  gitstatu
 9519  git status
 9520  rm -rf   internal/services/ai/a.json
 9521  git status
 9522  sh refresh-akp.sh
 9523  git diff
 9524  sh refresh-akp.sh
 9525  git status
 9526  git add internal
 9527  git status
 9528  git commit -m "update logic when load history conversation from db" -s
 9529  git push
 9530  sh refresh-akp.sh
 9531  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 9532  git status
 9533  git pull
 9534  git fetch origin
 9535  git merge origin/main
 9536  make generate-in-container
 9537  docker pull mirror.gcr.io/bufbuild/buf:1.46.0
 9538  make generate-in-container
 9539  git status
 9540  git add aims api docs internal portal pkg
 9541  git status
 9542  git commit -m "generate protobuf" -s
 9543  git push
 9544  git status
 9545  ./refresh-akp.sh
 9546  git status
 9547  git diff
 9548  git add internal
 9549  git status
 9550  git commit -m "update" -s
 9551  git status
 9552  git push
 9553  cd ../kubevision-scripts
 9554  ls
 9555  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 9556  make generate-in-container
 9557  ./refresh-akp.sh
 9558  git status
 9559  rm    internal/services/ai/a.json
 9560  git tsatus
 9561  git status
 9562  git add aims api docs pkg portal
 9563  git status
 9564  git commit -m "update argo cd proto" -s
 9565  git status
 9566  git push
 9567  ./refresh-akp.sh
 9568  git status
 9569  clear
 9570  make generate-in-container
 9571  clear
 9572  git status
 9573  git diff internal
 9574  git status
 9575  git diff internal/services/ai/service.go
 9576  git status
 9577  git add aims api docs internal pkg portal
 9578  git status
 9579  git commit -m "modify runbook display logic" -s
 9580  git status
 9581  git push
 9582  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 9583  git status
 9584  git add internal
 9585  git status
 9586  git commit -m "update" -s
 9587  git push
 9588  sh refresh-akp.sh
 9589  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 9590  ping google.com
 9591  clear
 9592  ls
 9593  clear
 9594  ls
 9595  cat a.log
 9596  clear
 9597  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 9598  ping google.com
 9599  cd ../kubevision-scripts
 9600  sh refresh-akp.sh
 9601  git status
 9602  sh refresh-akp.sh
 9603  git status
 9604  git add internal
 9605  git status
 9606  git commit -m "update" -s
 9607  git push
 9608  git pull
 9609  git log
 9610  clear
 9611  git status
 9612  git diff
 9613  git status
 9614  git add portal
 9615  git status
 9616  git commit -m "fix linter error" -s
 9617  git push
 9618  git status
 9619  git add internal
 9620  git status
 9621  git commit -m "update" -s
 9622  git push
 9623  git status
 9624  git diff  internal/services/ai/incident.go
 9625  git status
 9626  git checkout internal/services/ai/service.go
 9627  git checkout internal/services/ai/functions/prompt.md
 9628  git status
 9629  git diff
 9630  git status
 9631  git diff
 9632  sh refresh-akp.sh
 9633  git status
 9634  git diff internal/services/ai/prompt.md
 9635  git status
 9636  git add internal
 9637  git status
 9638  git commit -m "update prompt" -s
 9639  git push
 9640  git apply changes.patch
 9641  ls
 9642  git status
 9643  ls /tmp/codegen.patch
 9644  ls -lsht /tmp/codegen.patch
 9645  date
 9646  git apply  /tmp/codegen.patch
 9647  git apply  /tmp/codegen.patch --allow-empty 
 9648  git diff --exit-code -- . > /tmp/codegen.patch
 9649  git apply  /tmp/codegen.patch 
 9650  git diff --exit-code -- . > /tmp/codegen.patch
 9651  git apply  /tmp/codegen.patch --allow-empty 
 9652  make generate-in-container
 9653  git apply ~/Downloads/codegen.patch
 9654  git push
 9655  git status
 9656  rm         portal/ui/src/feature/ai-support-engineer/components/runbook-viewer/a.json
 9657  git status
 9658  git add docs portal
 9659  git status
 9660  git commit -m "fix linter error" -s
 9661  git push
 9662  git satus
 9663  git status
 9664  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 9665  cd portal
 9666  cd ui
 9667  pnpm i
 9668  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 9669  ls
 9670  cd ..
 9671  cd aims
 9672  ls
 9673  cd ui
 9674  pnpm i
 9675  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 9676  cd ..
 9677  ls
 9678  cd ..
 9679  ls
 9680  cd ..
 9681  cd akuity-platform
 9682  git fetch origin
 9683  git merge origin/main
 9684  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 9685  clear
 9686  git status
 9687  git log
 9688  make generate-in-container
 9689  git status
 9690  git add internal portal
 9691  git status
 9692  git commit -m "update" -s
 9693  git pull --rebase
 9694  git status
 9695  git log
 9696  make generate-in-container
 9697  git reset --soft HEAD~1
 9698  git status
 9699  git log
 9700  git stash
 9701  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 9702  git status
 9703  git diff
 9704  git status
 9705  git add internal portal
 9706  git status
 9707  git log
 9708  git commit -m "fix frontend logic" -s
 9709  git push
 9710  git status
 9711  git diff
 9712  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 9713  tiger
 9714  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 9715  ping google.com
 9716  ping github.com
 9717  ping youtube.com
 9718  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 9719  ping youtube.com
 9720  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 9721  ak
 9722  cd akuity-platform
 9723  cd ../kubevision-scripts
 9724  sh refresh-akp.sh
 9725  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 9726  sh refresh-akp.sh
 9727  ls
 9728  cd portal/ui
 9729  pnpm run lint
 9730  pnpm run lint --fix
 9731  git status
 9732  pnpm run lint
 9733  cd ../
 9734  cd ..
 9735  cd aims/ui
 9736  pnpm run lint
 9737  sh refresh-akp.sh
 9738  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 9739  sh refresh-akp.sh
 9740  ping google.cmo
 9741  ping google.com
 9742  ls
 9743  ak
 9744  cd rpl
 9745  ls
 9746  cd akuity-platform
 9747  ls
 9748  cd ../kubevision-scripts
 9749  sh refresh-akp.sh
 9750  echo 'Terminal capability test'
 9751  ping google.com
 9752  exit
 9753  ping github.com
 9754  ping google.com
 9755  ping youtube.com
 9756  ping google.com
 9757  sh refresh-akp.sh
 9758  ping google.com
 9759  sh refresh-akp.sh
 9760  ping google.com
 9761  sh refresh-akp.sh
 9762  ping google.com
 9763  sh refresh-akp.sh
 9764  export HTTPS_PROXY=http://************:7890\nexport HTTP_PROXY=http://************:7890
 9765  sh refresh-akp.sh
 9766  unset http_proxy\nunset https_proxy\nunset HTTP_PROXY\nunset HTTPS_PROXY
 9767  sh refresh-akp.sh
 9768  docker images
 9769  sh refresh-akp.sh
 9770  docker pull us-docker.pkg.dev/akuity/akp/akuity-platform
 9771  tiger
 9772  dev
 9773  docker login
 9774  docker pull us-docker.pkg.dev/akuity/akp/akuity-platform
 9775  cat ~/.docker/config.json
 9776  sh refresh-akp.sh
 9777  cd ..
 9778  cd akuity-platform
 9779  PUSH_LATEST=true IMAGE_REPO=us-docker.pkg.dev/akuity/akp/akuity-platform TAG=latest make image
 9780  ls
 9781  cd ../kubevision-scripts
 9782  ENABLE_KARGO=false ./start-akp.sh
 9783  k get po -A
 9784  k get po -A -w
 9785  k get po -A 
 9786  k get po -A -w
 9787  k get po -A 
 9788  k delete po -n akuity-platform portal-server-6f6458ff54-wcrd6
 9789  k delete po -n akuity-platform portal-server-6f6458ff54-wcrd6 --force
 9790  k get po -A 
 9791  k get po -A  -w
 9792  k get po -A
 9793  k describe po -n argocd-10yevm2finkfigc0 k3s-746f868f9-rt6bv
 9794  docker pull quay.io/akuity/rancher/k3s:v1.32.3-k3s1"
 9795  docker pull quay.io/akuity/rancher/k3s:v1.32.3-k3s1
 9796  k get po -A
 9797  k3d cluster list
 9798  ENABLE_KARGO=false ./start-akp.sh
 9799  k get po -A
 9800  kx
 9801  k get po -A
 9802  ENABLE_KARGO=false ./start-akp.sh
 9803  git status
 9804  git pull
 9805  git log
 9806  git branch
 9807  git pull
 9808  git status
 9809  ENABLE_KARGO=false ./start-akp.sh
 9810  vim /etc/hosts
 9811  sudo vim /etc/hosts
 9812  k3d cluster create akuity-customer
 9813  kx
 9814  k get po -A
 9815  k3d cluster create akuity-customer
 9816  k get po -A
 9817  kx
 9818  k get -A
 9819  k get po -A
 9820  k get po -A -w
 9821  k get po -A
 9822  k describe po -n argocd-d18wj1v0ctqmac0v argocd-application-controller-7459d7579c-sbwkd
 9823  docker pull quay.io/akuity/argocd:v3.0.11-ak.60
 9824  exit
 9825  TOKEN="eyJhbGciOiJSUzI1NiIsImtpZCI6IjM5ZTg2MjE4YjZiMjY4MjYyNGJkYWEwMjk2M2EwODEzNWFlNWQxNTgifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.RXpIwit6bNoW5_iM-1Uw-VS6CePzfSukpQBBoVRJqy9dOxW54pZ7NHKtWQLNdQusGhdtV6aAyXvVZjFrAM_rzBgTpECX-D8q1a1RTylUZDqpvWDSsWm6aBEHMt5F5HpcuYqwh-XUWFdwYecemrAHSZ_nsf1PTcMm0c-toUt3WR8eQZ2zHnCJW3zWMHShV4zkQH-ngYXKLvP06Gaahrcv81DwhEd-yYCYnG1u2P3zPUzrhKcHxOcXYBvIOgJyiiBQk8odEGgNDu0sH-j_IM7ClyEzTBKMgVQYDQbCAlllU7Y3-i-uvKVl8atB_Rax4EgwkHQabG3QqdQ8z8nh1SSf0Q" && curl -s -H "Authorization: Bearer $TOKEN" "https://portal-server.akuity-platform/api/v1/orgs/ksk5hfo7v4za9d4r/argocd/instances/d18wj1v0ctqmac0v/clusters/xhlxx4comjwzh72s/manifests" -k| kubectl apply -f -
 9826  k get po -A
 9827  k describe po -n akuity-agent argocd-repo-server-6d4d9875f8-96mv8
 9828  k get po -A
 9829  k describe po -n akuity-agent argocd-notifications-controller-69dc7fc645-49llw
 9830  k get po -A
 9831  k describe po -n akuity-agent argocd-notifications-controller-69dc7fc645-49llw
 9832  k get cm -A
 9833  k get cm -A |grep argocd-tls-certs-cm
 9834  k get cm -A
 9835  k get po -A
 9836  k get cm -n akuity-agent
 9837  k get po -A
 9838  k delete po -n akuity-agent NAMESPACE                 NAME                                               READY   STATUS              RESTARTS      AGE\nakuity-agent              akuity-agent-7dfd5f5745-ddxcr                      0/1     Running             0             3m14s\nakuity-agent              akuity-agent-7dfd5f5745-ft4zs                      0/1     Running             0             3m14s\nakuity-agent              argocd-application-controller-55956c4676-gshpd     2/3     Running             0             3m14s\nakuity-agent              argocd-notifications-controller-69dc7fc645-49llw   0/1     ContainerCreating   0             3m14s\nakuity-agent              argocd-redis-5f5cdf7686-4r7dv                      1/1     Running             0             3m14s\nakuity-agent              argocd-repo-server-6d4d9875f8-96mv8                0/1     Init:0/1            0             3m13s\nakuity-agent              argocd-repo-server-6d4d9875f8-tthrm                0/1     Init:0/1            0             3m13s\nakuity-platform           addon-controller-7f8d849d66-7vff5                  1/1     Running             5 (27m ago)   29m\nakuity-platform           aims-server-5c8c564f4d-hq9rm                       1/1     Running             0             12m\nakuity-platform           dex-74b9dbc5cf-gws2t                               1/1     Running             0             29m\nakuity-platform           platform-controller-5f4955f985-778wf               1/1     Running             0             12m\nakuity-platform           portal-server-6df5fbb4c-9s45m                      1/1     Running             0             12m\nakuity-platform           postgres-7ccb65b454-2cjfn                          1/1     Running             0             29m\nargocd-d18wj1v0ctqmac0v   agent-server-75db9c6467-5wql5                      1/1     Running             0             6m16s\nargocd-d18wj1v0ctqmac0v   agent-server-75db9c6467-gzmpt                      1/1     Running             0             6m17s\nargocd-d18wj1v0ctqmac0v   argocd-application-controller-76f6cf5f4d-fjzsx     3/3     Running             0             5m55s\nargocd-d18wj1v0ctqmac0v   argocd-applicationset-controller-c5fb865cc-7hk6t   3/3     Running             0             5m54s\nargocd-d18wj1v0ctqmac0v   argocd-redis-ha-haproxy-7b49f55745-skp76           1/1     Running             0             6m15s\nargocd-d18wj1v0ctqmac0v   argocd-redis-ha-haproxy-7b49f55745-tcwfv           1/1     Running             0             6m15s\nargocd-d18wj1v0ctqmac0v   argocd-redis-ha-server-0                           3/3     Running             0             6m17s\nargocd-d18wj1v0ctqmac0v   argocd-redis-ha-server-1                           3/3     Running             0             6m17s\nargocd-d18wj1v0ctqmac0v   argocd-redis-ha-server-2                           3/3     Running             0             6m17s\nargocd-d18wj1v0ctqmac0v   argocd-repo-server-56fc9b7b98-gmcjf                2/2     Running             0             6m15s\nargocd-d18wj1v0ctqmac0v   argocd-server-b65d4cc55-98hfb                      2/2     Running             0             5m51s\nargocd-d18wj1v0ctqmac0v   argocd-server-b65d4cc55-nj2j6                      2/2     Running             0             5m51s\nargocd-d18wj1v0ctqmac0v   k3s-66cf866488-49zkr                               4/4     Running             0             6m16s\nargocd-d18wj1v0ctqmac0v   k3s-66cf866488-4nkfn                               4/4     Running             0             5m48s\nargocd-d18wj1v0ctqmac0v   k3s-webhook-56f7b697f9-7z8c6                       1/1     Running             0             5m53s\nargocd-d18wj1v0ctqmac0v   k3s-webhook-56f7b697f9-fc9sv                       1/1     Running             0             5m53s\nkargo-zh9hw103pkhyeka9    argo-rollouts-84ccc65bb6-6285r                     1/1     Running             0             5m40s\nkargo-zh9hw103pkhyeka9    k3s-55fccc764-2s72b                                3/3     Running             0             6m18s\nkargo-zh9hw103pkhyeka9    k3s-55fccc764-wfkhg                                3/3     Running             0             6m3s\nkargo-zh9hw103pkhyeka9    k3s-webhook-7478f4967d-flmcl                       1/1     Running             0             6m18s\nkargo-zh9hw103pkhyeka9    k3s-webhook-7478f4967d-wrn6p                       1/1     Running             0             6m18s\nkargo-zh9hw103pkhyeka9    kargo-api-859d9b6b94-th7mc                         1/1     Running             0             5m47s\nkargo-zh9hw103pkhyeka9    kargo-api-859d9b6b94-vl6sl                         1/1     Running             0             5m47s\nkargo-zh9hw103pkhyeka9    kargo-extension-server-7b4dc9d544-w9jw5            1/1     Running             0             6m18s\nkargo-zh9hw103pkhyeka9    kargo-external-webhooks-server-b9764b468-7kc5n     1/1     Running             0             5m44s\nkargo-zh9hw103pkhyeka9    kargo-external-webhooks-server-b9764b468-q96zd     1/1     Running             0             5m44s\nkargo-zh9hw103pkhyeka9    kargo-management-controller-7dbb7b74f4-m8qh2       1/1     Running             0             5m39s\nkargo-zh9hw103pkhyeka9    kargo-webhooks-server-7d6f77d848-5clqk             1/1     Running             0             5m43s\nkargo-zh9hw103pkhyeka9    kargo-webhooks-server-7d6f77d848-hbjk7             1/1     Running             0             5m43s\nkube-system               coredns-7ff57748d5-ff59s                           1/1     Running             0             45m\nkube-system               local-path-provisioner-5b5f758bcf-grmhb            1/1     Running             0             45m\nkube-system               svclb-traefik-6a82ca2b-h9r7b                       2/2     Running             0             29m\ntraefik-external          traefik-d6888b444-wvf86                            1/1     Running             0             29m\n
 9839  k9s
 9840  cd ../kubevision-scripts
 9841  ls
 9842  ENABLE_KARGO=false ./start-akp.sh
 9843  kx
 9844  history |grep k3d-akuity-customer
 9845  kx
 9846  ENABLE_KARGO=false ./start-akp.sh
 9847  k get po -A
 9848  TOKEN="eyJhbGciOiJSUzI1NiIsImtpZCI6IjM5ZTg2MjE4YjZiMjY4MjYyNGJkYWEwMjk2M2EwODEzNWFlNWQxNTgifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.RXpIwit6bNoW5_iM-1Uw-VS6CePzfSukpQBBoVRJqy9dOxW54pZ7NHKtWQLNdQusGhdtV6aAyXvVZjFrAM_rzBgTpECX-D8q1a1RTylUZDqpvWDSsWm6aBEHMt5F5HpcuYqwh-XUWFdwYecemrAHSZ_nsf1PTcMm0c-toUt3WR8eQZ2zHnCJW3zWMHShV4zkQH-ngYXKLvP06Gaahrcv81DwhEd-yYCYnG1u2P3zPUzrhKcHxOcXYBvIOgJyiiBQk8odEGgNDu0sH-j_IM7ClyEzTBKMgVQYDQbCAlllU7Y3-i-uvKVl8atB_Rax4EgwkHQabG3QqdQ8z8nh1SSf0Q" && curl -s -H "Authorization: Bearer $TOKEN" "https://portal-server.akuity-platform/api/v1/orgs/ksk5hfo7v4za9d4r/argocd/instances/d18wj1v0ctqmac0v/clusters/xhlxx4comjwzh72s/manifests?skipNamespace=true" -k | kubectl delete -f - && kubectl delete ns akp-demo
 9849  TOKEN="eyJhbGciOiJSUzI1NiIsImtpZCI6IjM5ZTg2MjE4YjZiMjY4MjYyNGJkYWEwMjk2M2EwODEzNWFlNWQxNTgifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.RXpIwit6bNoW5_iM-1Uw-VS6CePzfSukpQBBoVRJqy9dOxW54pZ7NHKtWQLNdQusGhdtV6aAyXvVZjFrAM_rzBgTpECX-D8q1a1RTylUZDqpvWDSsWm6aBEHMt5F5HpcuYqwh-XUWFdwYecemrAHSZ_nsf1PTcMm0c-toUt3WR8eQZ2zHnCJW3zWMHShV4zkQH-ngYXKLvP06Gaahrcv81DwhEd-yYCYnG1u2P3zPUzrhKcHxOcXYBvIOgJyiiBQk8odEGgNDu0sH-j_IM7ClyEzTBKMgVQYDQbCAlllU7Y3-i-uvKVl8atB_Rax4EgwkHQabG3QqdQ8z8nh1SSf0Q" && curl -s -H "Authorization: Bearer $TOKEN" "https://portal-server.akuity-platform/api/v1/orgs/ksk5hfo7v4za9d4r/argocd/instances/d18wj1v0ctqmac0v/clusters/xhlxx4comjwzh72s/manifests?skipNamespace=true" | kubectl delete -f - && kubectl delete ns akp-demo
 9850  k get po -A
 9851  k get po -A -w
 9852  k get po -A 
 9853  history |grep akuity-customer
 9854  ENABLE_KARGO=false ./start-akp.sh
 9855  k get po -A 
 9856  history |grep akuity-customer
 9857  kx
 9858  ENABLE_KARGO=false ./start-akp.sh
 9859  kx
 9860  docker pull ghcr.io/k3d-io/k3d-proxy:5.8.3
 9861  kx
 9862  k get po -A
 9863  k get po -A -w
 9864  ENABLE_KARGO=false ./start-akp.sh
 9865  vim /etc/hosts
 9866  sudo vim /etc/hosts
 9867  k get po -A -w
 9868  k get po -A 
 9869  ENABLE_KARGO=false ./start-akp.sh
 9870  k get po -A 
 9871  ping google.com
 9872  k get po -A -w
 9873  k get po -A
 9874  k delete ns akuity-agent
 9875  k get po -A
 9876  kx
 9877  k get po -A
 9878  kx
 9879  k get po -A
 9880  TOKEN="eyJhbGciOiJSUzI1NiIsImtpZCI6IjM5ZTg2MjE4YjZiMjY4MjYyNGJkYWEwMjk2M2EwODEzNWFlNWQxNTgifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.RXpIwit6bNoW5_iM-1Uw-VS6CePzfSukpQBBoVRJqy9dOxW54pZ7NHKtWQLNdQusGhdtV6aAyXvVZjFrAM_rzBgTpECX-D8q1a1RTylUZDqpvWDSsWm6aBEHMt5F5HpcuYqwh-XUWFdwYecemrAHSZ_nsf1PTcMm0c-toUt3WR8eQZ2zHnCJW3zWMHShV4zkQH-ngYXKLvP06Gaahrcv81DwhEd-yYCYnG1u2P3zPUzrhKcHxOcXYBvIOgJyiiBQk8odEGgNDu0sH-j_IM7ClyEzTBKMgVQYDQbCAlllU7Y3-i-uvKVl8atB_Rax4EgwkHQabG3QqdQ8z8nh1SSf0Q" && curl -s -H "Authorization: Bearer $TOKEN" "https://portal-server.akuity-platform/api/v1/orgs/ksk5hfo7v4za9d4r/argocd/instances/csz0ilrk0pupday4/clusters/zcovu2ckbn4h2f9r/manifests" -k | kubectl apply -f -
 9881  k get po -A
 9882  k describe po -n akuity-agent argocd-application-controller-59ffd64c76-5q2xq
 9883  k get po -A
 9884  k delete ns akuity
 9885  sudo vim /etc/hosts
 9886  git status
 9887  git diff
 9888  cd portal/ui
 9889  pnpm run lint --fix
 9890  git status
 9891  git diff
 9892  git add src
 9893  git status
 9894  git commit -m "update" -s
 9895  git push
 9896  git status
 9897  git branch
 9898  git checkout   test-runbook
 9899  git status
 9900  git log
 9901  ping google.com
 9902  ping github.com
 9903  tiger
 9904  git status
 9905  cd ..
 9906  git status
 9907  cd ..
 9908  git status
 9909  git checkout internal
 9910  git status
 9911  git branch
 9912  git chekcout   ai-mesasge-refactor
 9913  git checkout   ai-mesasge-refactor
 9914  git status
 9915  cd /Users/<USER>/MyPro/akuity/akuity-platform && buf generate
 9916  make generate-in-container
 9917  buf generate --config buf.gen.yaml
 9918  make generate-buf
 9919  make generate-in-container
 9920  echo 'Terminal capability test'
 9921  make generate-in-container
 9922  ping google.com
 9923  ak
 9924  cd akuity-platform
 9925  ls
 9926  make generate-in-container
 9927  make generate
 9928  ak
 9929  cd akuity-platform
 9930  make generate
 9931  make generate-in-container
 9932  dev
 9933  tiger
 9934  make generate-in-container
 9935  git status
 9936  git  diff
 9937  make generate-in-container
 9938  git status
 9939  git diff
 9940  make generate-in-container
 9941  git status
 9942  cd ../kubevision-scripts
 9943  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 9944  git diff
 9945  git status
 9946  git diff docs/generated/swagger/apidocs.swagger.yaml
 9947  git add aims api docs internal pkg portal 
 9948  git status
 9949  git commit -m "add enum type of aditional content type" -s
 9950  git push
 9951  git pull --rebase
 9952  git push
 9953  make generate-in-container
 9954  git status
 9955  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
 9956  git status
 9957  git diff
 9958  git status
 9959  git add aims api docs internal pkg portal
 9960  git status
 9961  git commit -m "rename enum type of aditional content type" -s
 9962  git push
 9963  cd /portal/ui
 9964  cd portal/ui
 9965  pnpm run lint --fix
 9966  git status
 9967  git diff
 9968  git add src
 9969  git status
 9970  git commit -m "fix linter error" -s
 9971  git push
 9972  helm version
 9973  buf 
 9974  buf version
 9975  buf -v
 9976  buf --version
 9977  node -v
 9978  tiger
 9979  ak
 9980  cd ../SlackUp
 9981  ls
 9982  ./SlackUp
 9983  ak
 9984  cd ../SlackUp
 9985  ls
 9986  ./SlackUp
 9987  dev
 9988  tiger
 9989  git branch
 9990  git status
 9991  git branch
 9992  git checkout test-runbook
 9993  git status
 9994  git pull
 9995  git log
 9996  git reset --hard HEAD~1
 9997  git pull
 9998  git status
 9999  git log
10000  clear
10001  echo 'Terminal capability test'
10002  git status
10003  cd ../kubevision-scripts
10004  sh refresh-akp.sh
10005  git status
10006  git diff internal/services/ai/service.go
10007  git status
10008  git add internal
10009  git tatus
10010  git status
10011  git commit -m "add prompt" -s
10012  git status
10013  git log
10014  git status
10015  git checkout main
10016  git pull
10017  clear
10018  git status
10019  git log
10020  git checkout -b test-runbook-tmp
10021  git cherry-pick f0d0d768ece5d2eda965afb20e754cc241788191
10022  git status
10023  git checkout pkg/api/gen/organization/v1/organization.pb.go
10024  git status
10025  git add pkg
10026  git status
10027  git cherry-pick --continue
10028  git cherry-pick 3c102a3f6109b31e7281cd9d41531f567ef7928d
10029  git cherry-pick ddf78e59af7ac4653a616ab3b3b5cfc3a47e3cc5
10030  git status
10031  git add pkg
10032  git cherry-pick --continue
10033  git cherry-pick ddf78e59af7ac4653a616ab3b3b5cfc3a47e3cc5
10034  git status
10035  git add pkg
10036  git status
10037  git cherry-pick --continue
10038  git log
10039  git cherry-pick b7af5c4d6c171974f5ba41095c23868419711bca
10040  git status
10041  git log
10042  make generate-in-container
10043  git status
10044  git diff
10045  git status
10046  git add pkg
10047  git status
10048  git commit --amend
10049  git status
10050  sh refresh-akp.sh
10051  git log
10052  git checkout test-runbook
10053  git status
10054  git add internal
10055  git status
10056  git log
10057  git commit --amend
10058  sh refresh-akp.sh
10059  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
10060  git status
10061  make generate-in-container
10062  git status
10063  git log
10064  git status
10065  git push
10066  git log
10067  cd /Users/<USER>/MyPro/akuity/kubevision-scripts && UI=true ./refresh-akp.sh
10068  k9s
10069  ak
10070  cd akuity-platform
10071  history |grep apply |grep git
